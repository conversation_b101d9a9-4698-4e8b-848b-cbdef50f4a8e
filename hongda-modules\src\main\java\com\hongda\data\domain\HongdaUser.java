package com.hongda.data.domain;

import com.hongda.common.annotation.OssUrl;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.hongda.common.annotation.Excel;
import com.hongda.common.annotation.OssUrl;
import com.hongda.common.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 小程序用户信息 (对应用户中心、登录功能)对象 hongda_user
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Schema(description = "小程序用户信息")
public class HongdaUser extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用户ID, 主键 */
    @Schema(description = "用户ID", example = "1")
    private Long id;

    /** 微信用户唯一标识 */
    @Excel(name = "微信用户唯一标识")
    @Schema(description = "微信用户唯一标识", example = "oU1234567890abcdef")
    private String openid;

    /** 微信开放平台唯一标识 (可选) */
    @Excel(name = "微信开放平台唯一标识 (可选)")
    @Schema(description = "微信开放平台唯一标识", example = "uU1234567890abcdef")
    private String unionid;

    /** 手机号 */
    @Excel(name = "手机号")
    @Schema(description = "手机号", example = "13800138000")
    private String phone;

    /** 用户昵称 (支持微信一键登录获取) */
    @Excel(name = "用户昵称 (支持微信一键登录获取)")
    @Schema(description = "用户昵称", example = "张三")
    private String nickname;

    /** 用户头像链接 (支持微信一键登录获取) */
    @Excel(name = "用户头像链接 (支持微信一键登录获取)")
    @Schema(description = "用户头像链接", example = "https://example.com/avatar.jpg")
    @OssUrl
    private String avatarUrl;

    /** 账户状态 (0=正常, 1=禁用) */
    @Excel(name = "账户状态 (0=正常, 1=禁用)")
    @Schema(description = "账户状态", example = "0", allowableValues = {"0", "1"})
    private Integer status;

    /** 软删除标识 (0=未删除, 1=已删除) */
    @Excel(name = "软删除标识 (0=未删除, 1=已删除)")
    @Schema(description = "软删除标识", example = "0", allowableValues = {"0", "1"})
    private Integer isDeleted;

    /** 软删除时间 */
    @Excel(name = "软删除时间")
    @Schema(description = "软删除时间")
    private java.util.Date deletedAt;

    /** 手机号哈希值 (用于复活账号) */
    @Excel(name = "手机号哈希值")
    @Schema(description = "手机号哈希值，用于复活账号时匹配")
    private String phoneHash;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setOpenid(String openid) 
    {
        this.openid = openid;
    }

    public String getOpenid() 
    {
        return openid;
    }

    public void setUnionid(String unionid) 
    {
        this.unionid = unionid;
    }

    public String getUnionid() 
    {
        return unionid;
    }

    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }

    public void setNickname(String nickname) 
    {
        this.nickname = nickname;
    }

    public String getNickname() 
    {
        return nickname;
    }

    public void setAvatarUrl(String avatarUrl) 
    {
        this.avatarUrl = avatarUrl;
    }

    public String getAvatarUrl() 
    {
        return avatarUrl;
    }

    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }

    public void setIsDeleted(Integer isDeleted) 
    {
        this.isDeleted = isDeleted;
    }

    public Integer getIsDeleted() 
    {
        return isDeleted;
    }

    public void setDeletedAt(java.util.Date deletedAt) 
    {
        this.deletedAt = deletedAt;
    }

    public java.util.Date getDeletedAt() 
    {
        return deletedAt;
    }

    public void setPhoneHash(String phoneHash) 
    {
        this.phoneHash = phoneHash;
    }

    public String getPhoneHash() 
    {
        return phoneHash;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("openid", getOpenid())
            .append("unionid", getUnionid())
            .append("phone", getPhone())
            .append("nickname", getNickname())
            .append("avatarUrl", getAvatarUrl())
            .append("status", getStatus())
            .append("isDeleted", getIsDeleted())
            .append("deletedAt", getDeletedAt())
            .append("phoneHash", getPhoneHash())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
