package com.hongda.data.mapper;

import java.util.List;
import com.hongda.data.domain.HongdaEventRegistration;

/**
 * 活动报名记录 (对应“报名数据”管理)Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
public interface HongdaEventRegistrationMapper 
{
    /**
     * 查询活动报名记录 (对应“报名数据”管理)
     * 
     * @param id 活动报名记录 (对应“报名数据”管理)主键
     * @return 活动报名记录 (对应“报名数据”管理)
     */
    public HongdaEventRegistration selectHongdaEventRegistrationById(Long id);

    /**
     * 查询活动报名记录 (对应“报名数据”管理)列表
     * 
     * @param hongdaEventRegistration 活动报名记录 (对应“报名数据”管理)
     * @return 活动报名记录 (对应“报名数据”管理)集合
     */
    public List<HongdaEventRegistration> selectHongdaEventRegistrationList(HongdaEventRegistration hongdaEventRegistration);

    /**
     * 新增活动报名记录 (对应“报名数据”管理)
     * 
     * @param hongdaEventRegistration 活动报名记录 (对应“报名数据”管理)
     * @return 结果
     */
    public int insertHongdaEventRegistration(HongdaEventRegistration hongdaEventRegistration);

    /**
     * 修改活动报名记录 (对应“报名数据”管理)
     * 
     * @param hongdaEventRegistration 活动报名记录 (对应“报名数据”管理)
     * @return 结果
     */
    public int updateHongdaEventRegistration(HongdaEventRegistration hongdaEventRegistration);

    /**
     * 删除活动报名记录 (对应“报名数据”管理)
     * 
     * @param id 活动报名记录 (对应“报名数据”管理)主键
     * @return 结果
     */
    public int deleteHongdaEventRegistrationById(Long id);

    /**
     * 批量删除活动报名记录 (对应“报名数据”管理)
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHongdaEventRegistrationByIds(Long[] ids);

    /**
     * 根据活动ID查询报名记录列表
     * 
     * @param eventId 活动ID
     * @return 报名记录集合
     */
    public List<HongdaEventRegistration> selectRegistrationListByEventId(Long eventId);

    /**
     * 根据活动ID查询报名列表（并关联用户信息）
     * @param eventId 活动ID
     * @return 报名列表
     */
    public List<HongdaEventRegistration> selectRegistrationWithUserByEventId(Long eventId);

    /**
     * 查询报名记录列表（并关联用户信息）- 支持分页和条件查询
     * @param hongdaEventRegistration 查询条件
     * @return 报名记录集合
     */
    public List<HongdaEventRegistration> selectRegistrationWithUserList(HongdaEventRegistration hongdaEventRegistration);

    /**
     * 根据活动ID删除报名记录
     * 
     * @param eventId 活动ID
     * @return 结果
     */
    public int deleteRegistrationByEventId(Long eventId);

    /**
     * 根据手机号查找用户ID
     * 
     * @param phone 手机号
     * @return 用户ID
     */
    public Long selectUserIdByPhone(String phone);
}
