<template>
  <el-card shadow="never" class="form-card">
    <template #header>
      <div class="card-header">
        <span class="card-title">详细内容</span>
      </div>
    </template>
    
    <el-form-item label="活动图文详情" prop="details">
      <editor
        :model-value="form.details"
        @update:model-value="updateField('details', $event)"
        :min-height="300"
      />
    </el-form-item>
  </el-card>
</template>

<script setup>
// Props
const props = defineProps({
  form: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['update:form'])

// Methods
const updateField = (field, value) => {
  const updatedForm = { ...props.form, [field]: value }
  emit('update:form', updatedForm)
}
</script>

<style scoped>
/* 表单卡片样式 */
.form-card {
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.form-card :deep(.el-card__header) {
  padding: 16px 20px;
  background-color: #fafafa;
  border-bottom: 1px solid #e4e7ed;
}

.form-card :deep(.el-card__body) {
  padding: 24px 20px;
}

/* 卡片标题样式 */
.card-header {
  display: flex;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}
</style>
