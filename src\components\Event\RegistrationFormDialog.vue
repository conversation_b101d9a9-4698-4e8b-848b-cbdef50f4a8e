<template>
  <el-dialog 
    :title="formTitle" 
    v-model="open" 
    width="700px" 
    append-to-body
  >
    <el-form 
      ref="registrationFormRef" 
      :model="formData" 
      :rules="rules" 
      label-width="100px"
    >
      <el-form-item label="用户手机号" prop="userPhone">
        <el-input 
          v-model="formData.userPhone" 
          placeholder="请输入用户手机号" 
          maxlength="11"
          :disabled="formData.id != null"
        />
        <div style="font-size: 12px; color: #999; margin-top: 4px;">
          {{ formData.id ? '修改时无法更改手机号' : '通过手机号识别用户身份' }}
        </div>
      </el-form-item>
      
      <el-form-item label="报名状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio :label="0">已报名</el-radio>
          <el-radio :label="1">已取消</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <!-- 动态表单渲染区域 -->
      <div v-if="currentFormDefinition && currentFormDefinition.length > 0">
        <el-divider content-position="left">报名表单内容</el-divider>
        <div style="background: #f9f9f9; padding: 15px; border-radius: 4px;">
          <form-create 
            v-model="dynamicFormValue" 
            :rule="currentFormDefinition" 
            :option="formCreateOption"
            :key="formCreateKey"
            ref="dynamicFormRef"
            @change="handleDynamicFormChange"
          />
        </div>
      </div>
      <el-alert 
        v-else 
        title="该活动暂无自定义表单设计" 
        type="info" 
        show-icon 
        :closable="false" 
      />
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
        <el-button @click="handleCancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, getCurrentInstance } from 'vue'

const { proxy } = getCurrentInstance()

// Props
const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  formTitle: {
    type: String,
    default: ''
  },
  formData: {
    type: Object,
    required: true
  },
  rules: {
    type: Object,
    required: true
  },
  currentFormDefinition: {
    type: Array,
    default: () => []
  },
  dynamicFormValue: {
    type: Object,
    default: () => ({})
  },
  formCreateOption: {
    type: Object,
    default: () => ({})
  },
  formCreateKey: {
    type: Number,
    default: 0
  }
})

// Emits
const emit = defineEmits([
  'update:open', 
  'update:dynamicFormValue',
  'submit', 
  'cancel',
  'dynamic-form-change'
])

// Refs
const registrationFormRef = ref()
const dynamicFormRef = ref()

// Methods
const handleSubmit = () => {
  registrationFormRef.value.validate(valid => {
    if (valid) {
      emit('submit', {
        formData: props.formData,
        dynamicFormValue: props.dynamicFormValue,
        dynamicFormRef: dynamicFormRef.value
      })
    }
  })
}

const handleCancel = () => {
  emit('cancel')
}

const handleDynamicFormChange = (field, value, rule, formData) => {
  emit('dynamic-form-change', field, value, rule, formData)
}

// Expose refs for parent component
defineExpose({
  dynamicFormRef
})
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
}

.form-create-custom {
  margin-top: 10px;
}
</style>
