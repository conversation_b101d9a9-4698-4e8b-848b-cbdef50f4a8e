package com.hongda.platform.service.impl;

import java.util.List;
import com.hongda.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.hongda.platform.mapper.HongdaConsultantMapper;
import com.hongda.platform.domain.HongdaConsultant;
import com.hongda.platform.service.IHongdaConsultantService;

/**
 * 顾问管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
@Service
public class HongdaConsultantServiceImpl implements IHongdaConsultantService 
{
    @Autowired
    private HongdaConsultantMapper hongdaConsultantMapper;

    /**
     * 查询顾问管理
     * 
     * @param id 顾问管理主键
     * @return 顾问管理
     */
    @Override
    public HongdaConsultant selectHongdaConsultantById(Long id)
    {
        return hongdaConsultantMapper.selectHongdaConsultantById(id);
    }

    /**
     * 查询顾问管理列表
     * 
     * @param hongdaConsultant 顾问管理
     * @return 顾问管理
     */
    @Override
    public List<HongdaConsultant> selectHongdaConsultantList(HongdaConsultant hongdaConsultant)
    {
        return hongdaConsultantMapper.selectHongdaConsultantList(hongdaConsultant);
    }

    /**
     * 新增顾问管理
     * 
     * @param hongdaConsultant 顾问管理
     * @return 结果
     */
    @Override
    public int insertHongdaConsultant(HongdaConsultant hongdaConsultant)
    {
        hongdaConsultant.setCreateTime(DateUtils.getNowDate());
        return hongdaConsultantMapper.insertHongdaConsultant(hongdaConsultant);
    }

    /**
     * 修改顾问管理
     * 
     * @param hongdaConsultant 顾问管理
     * @return 结果
     */
    @Override
    public int updateHongdaConsultant(HongdaConsultant hongdaConsultant)
    {
        hongdaConsultant.setUpdateTime(DateUtils.getNowDate());
        return hongdaConsultantMapper.updateHongdaConsultant(hongdaConsultant);
    }

    /**
     * 批量删除顾问管理
     * 
     * @param ids 需要删除的顾问管理主键
     * @return 结果
     */
    @Override
    public int deleteHongdaConsultantByIds(Long[] ids)
    {
        return hongdaConsultantMapper.deleteHongdaConsultantByIds(ids);
    }

    /**
     * 删除顾问管理信息
     * 
     * @param id 顾问管理主键
     * @return 结果
     */
    @Override
    public int deleteHongdaConsultantById(Long id)
    {
        return hongdaConsultantMapper.deleteHongdaConsultantById(id);
    }

    /**
     * 获取当前应展示的顾问信息 (状态启用且排序最前)
     *
     * @return 单个顾问信息，如果找不到则返回null
     */
    @Override
    public HongdaConsultant selectDisplayConsultant() {
        // 直接调用 Mapper 中定义的方法
        return hongdaConsultantMapper.selectDisplayConsultant();
    }
}
