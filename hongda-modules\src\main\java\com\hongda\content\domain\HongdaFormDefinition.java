package com.hongda.content.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.hongda.common.annotation.Excel;
import com.hongda.common.core.domain.BaseEntity;

/**
 * 表单定义对象 hongda_form_definition
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
@Data
public class HongdaFormDefinition extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 表单定义ID, 主键 */
    private Long id;

    /** 关联的活动ID */
    @Excel(name = "关联的活动ID")
    private Long eventId;

    /** 表单名称 */
    @Excel(name = "表单名称")
    private String name;

    /** 表单字段定义JSON */
    @Excel(name = "表单字段定义JSON")
    private String fieldsJson;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("eventId", getEventId())
            .append("name", getName())
            .append("fieldsJson", getFieldsJson())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}