import request from '@/utils/request'

// 查询导航配置列表
export function listNav(query) {
  return request({
    url: '/platform/nav/list',
    method: 'get',
    params: query
  })
}

// 查询导航配置详细
export function getNav(id) {
  return request({
    url: '/platform/nav/' + id,
    method: 'get'
  })
}

// 新增导航配置
export function addNav(data) {
  return request({
    url: '/platform/nav',
    method: 'post',
    data: data
  })
}

// 修改导航配置
export function updateNav(data) {
  return request({
    url: '/platform/nav',
    method: 'put',
    data: data
  })
}

// 删除导航配置
export function delNav(id) {
  return request({
    url: '/platform/nav/' + id,
    method: 'delete'
  })
}
