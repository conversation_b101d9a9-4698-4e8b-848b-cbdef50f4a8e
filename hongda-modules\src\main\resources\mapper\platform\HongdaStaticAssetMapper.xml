<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongda.platform.mapper.HongdaStaticAssetMapper">
    
    <resultMap type="HongdaStaticAsset" id="HongdaStaticAssetResult">
        <result property="id"    column="id"    />
        <result property="assetKey"    column="asset_key"    />
        <result property="assetName"    column="asset_name"    />
        <result property="assetUrl"    column="asset_url"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectHongdaStaticAssetVo">
        select id, asset_key, asset_name, asset_url, remark, create_by, create_time, update_by, update_time from hongda_static_asset
    </sql>

    <select id="selectHongdaStaticAssetList" parameterType="HongdaStaticAsset" resultMap="HongdaStaticAssetResult">
        <include refid="selectHongdaStaticAssetVo"/>
        <where>  
            <if test="assetKey != null  and assetKey != ''"> and asset_key = #{assetKey}</if>
            <if test="assetName != null  and assetName != ''"> and asset_name like concat('%', #{assetName}, '%')</if>
        </where>
    </select>
    
    <select id="selectHongdaStaticAssetById" parameterType="Long" resultMap="HongdaStaticAssetResult">
        <include refid="selectHongdaStaticAssetVo"/>
        where id = #{id}
    </select>

    <insert id="insertHongdaStaticAsset" parameterType="HongdaStaticAsset" useGeneratedKeys="true" keyProperty="id">
        insert into hongda_static_asset
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="assetKey != null and assetKey != ''">asset_key,</if>
            <if test="assetName != null and assetName != ''">asset_name,</if>
            <if test="assetUrl != null">asset_url,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="assetKey != null and assetKey != ''">#{assetKey},</if>
            <if test="assetName != null and assetName != ''">#{assetName},</if>
            <if test="assetUrl != null">#{assetUrl},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateHongdaStaticAsset" parameterType="HongdaStaticAsset">
        update hongda_static_asset
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetKey != null and assetKey != ''">asset_key = #{assetKey},</if>
            <if test="assetName != null and assetName != ''">asset_name = #{assetName},</if>
            <if test="assetUrl != null">asset_url = #{assetUrl},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHongdaStaticAssetById" parameterType="Long">
        delete from hongda_static_asset where id = #{id}
    </delete>

    <delete id="deleteHongdaStaticAssetByIds" parameterType="String">
        delete from hongda_static_asset where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>