package com.hongda.data.mapper;

import java.util.List;
import java.util.Map;

/**
 * 数据统计Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-22
 */
public interface StatisticMapper 
{
    /**
     * 获取总报名人次
     * 
     * @return 总报名人次
     */
    public Long getTotalRegistrations();

    /**
     * 获取今日新增报名人次
     * 
     * @return 今日新增报名人次
     */
    public Long getTodayNewRegistrations();

    /**
     * 获取总用户数
     * 
     * @return 总用户数
     */
    public Long getTotalUsers();

    /**
     * 获取正在进行中的活动数
     * 
     * @return 正在进行中的活动数
     */
    public Long getOngoingEvents();

    /**
     * 获取热门活动报名排行TOP5
     * 
     * @return 热门活动列表
     */
    public List<Map<String, Object>> getTopEvents();

    /**
     * 获取近7日报名趋势
     * 
     * @return 每日报名数据
     */
    public List<Map<String, Object>> getDailyTrend();
}