package com.hongda.content.service.impl;

import java.util.List;
import com.hongda.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.hongda.content.mapper.HongdaRegionMapper;
import com.hongda.content.domain.HongdaRegion;
import com.hongda.content.service.IHongdaRegionService;

/**
 * 地区管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@Service
public class HongdaRegionServiceImpl implements IHongdaRegionService 
{
    @Autowired
    private HongdaRegionMapper hongdaRegionMapper;

    /**
     * 查询地区管理
     * 
     * @param id 地区管理主键
     * @return 地区管理
     */
    @Override
    public HongdaRegion selectHongdaRegionById(Long id)
    {
        return hongdaRegionMapper.selectHongdaRegionById(id);
    }

    /**
     * 查询地区管理列表
     * 
     * @param hongdaRegion 地区管理
     * @return 地区管理
     */
    @Override
    public List<HongdaRegion> selectHongdaRegionList(HongdaRegion hongdaRegion)
    {
        return hongdaRegionMapper.selectHongdaRegionList(hongdaRegion);
    }

    /**
     * 新增地区管理
     * 
     * @param hongdaRegion 地区管理
     * @return 结果
     */
    @Override
    public int insertHongdaRegion(HongdaRegion hongdaRegion)
    {
        hongdaRegion.setCreateTime(DateUtils.getNowDate());
        return hongdaRegionMapper.insertHongdaRegion(hongdaRegion);
    }

    /**
     * 修改地区管理
     * 
     * @param hongdaRegion 地区管理
     * @return 结果
     */
    @Override
    public int updateHongdaRegion(HongdaRegion hongdaRegion)
    {
        hongdaRegion.setUpdateTime(DateUtils.getNowDate());
        return hongdaRegionMapper.updateHongdaRegion(hongdaRegion);
    }

    /**
     * 批量删除地区管理
     * 
     * @param ids 需要删除的地区管理主键
     * @return 结果
     */
    @Override
    public int deleteHongdaRegionByIds(Long[] ids)
    {
        return hongdaRegionMapper.deleteHongdaRegionByIds(ids);
    }

    /**
     * 删除地区管理信息
     * 
     * @param id 地区管理主键
     * @return 结果
     */
    @Override
    public int deleteHongdaRegionById(Long id)
    {
        return hongdaRegionMapper.deleteHongdaRegionById(id);
    }
}
