package com.hongda.platform.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hongda.common.annotation.Log;
import com.hongda.common.core.controller.BaseController;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.common.enums.BusinessType;
import com.hongda.platform.domain.HongdaStaticAsset;
import com.hongda.platform.service.IHongdaStaticAssetService;
import com.hongda.common.utils.poi.ExcelUtil;
import com.hongda.common.core.page.TableDataInfo;

/**
 * 小程序静态资源配置Controller
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@RestController
@RequestMapping("/platform/asset")
public class HongdaStaticAssetController extends BaseController
{
    @Autowired
    private IHongdaStaticAssetService hongdaStaticAssetService;

    /**
     * 查询小程序静态资源配置列表
     */
    @PreAuthorize("@ss.hasPermi('platform:asset:list')")
    @GetMapping("/list")
    public TableDataInfo list(HongdaStaticAsset hongdaStaticAsset)
    {
        startPage();
        List<HongdaStaticAsset> list = hongdaStaticAssetService.selectHongdaStaticAssetList(hongdaStaticAsset);
        return getDataTable(list);
    }

    /**
     * 导出小程序静态资源配置列表
     */
    @PreAuthorize("@ss.hasPermi('platform:asset:export')")
    @Log(title = "小程序静态资源配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HongdaStaticAsset hongdaStaticAsset)
    {
        List<HongdaStaticAsset> list = hongdaStaticAssetService.selectHongdaStaticAssetList(hongdaStaticAsset);
        ExcelUtil<HongdaStaticAsset> util = new ExcelUtil<HongdaStaticAsset>(HongdaStaticAsset.class);
        util.exportExcel(response, list, "小程序静态资源配置数据");
    }

    /**
     * 获取小程序静态资源配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('platform:asset:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(hongdaStaticAssetService.selectHongdaStaticAssetById(id));
    }

    /**
     * 新增小程序静态资源配置
     */
    @PreAuthorize("@ss.hasPermi('platform:asset:add')")
    @Log(title = "小程序静态资源配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HongdaStaticAsset hongdaStaticAsset)
    {
        return toAjax(hongdaStaticAssetService.insertHongdaStaticAsset(hongdaStaticAsset));
    }

    /**
     * 修改小程序静态资源配置
     */
    @PreAuthorize("@ss.hasPermi('platform:asset:edit')")
    @Log(title = "小程序静态资源配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HongdaStaticAsset hongdaStaticAsset)
    {
        return toAjax(hongdaStaticAssetService.updateHongdaStaticAsset(hongdaStaticAsset));
    }

    /**
     * 删除小程序静态资源配置
     */
    @PreAuthorize("@ss.hasPermi('platform:asset:remove')")
    @Log(title = "小程序静态资源配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(hongdaStaticAssetService.deleteHongdaStaticAssetByIds(ids));
    }
}
