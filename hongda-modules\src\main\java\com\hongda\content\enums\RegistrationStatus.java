package com.hongda.content.enums;

import java.util.Date;

/**
 * 报名状态枚举
 * 基于报名开始时间和结束时间动态计算
 * 
 * <AUTHOR>
 * @date 2025-01-22
 */
public enum RegistrationStatus {
    NOT_STARTED(0, "未开始"),
    OPEN(1, "报名中"),
    CLOSED(2, "已结束");
    
    private final Integer code;
    private final String desc;
    
    RegistrationStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据报名开始时间和结束时间计算报名状态
     * 
     * @param registrationStartTime 报名开始时间
     * @param registrationEndTime 报名结束时间
     * @return 报名状态
     */
    public static RegistrationStatus calculateStatus(Date registrationStartTime, Date registrationEndTime) {
        Date now = new Date();
        
        // 未开始：报名开始时间不为空 && 当前时间 < 报名开始时间
        if (registrationStartTime != null && now.before(registrationStartTime)) {
            return NOT_STARTED;
        } 
        // 报名中：(报名开始时间为空 || 当前时间 >= 报名开始时间) && (报名结束时间为空 || 当前时间 <= 报名结束时间)
        else if ((registrationStartTime == null || now.compareTo(registrationStartTime) >= 0) && 
                 (registrationEndTime == null || now.compareTo(registrationEndTime) <= 0)) {
            return OPEN;
        } 
        // 已结束：报名结束时间不为空 && 当前时间 > 报名结束时间
        else if (registrationEndTime != null && now.after(registrationEndTime)) {
            return CLOSED;
        }
        
        // 默认返回报名中（如果时间配置不明确）
        return OPEN;
    }
    
    /**
     * 根据状态码获取枚举
     * 
     * @param code 状态码
     * @return 报名状态枚举
     */
    public static RegistrationStatus getByCode(Integer code) {
        if (code == null) {
            return OPEN;
        }
        
        for (RegistrationStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        
        return OPEN;
    }
}
