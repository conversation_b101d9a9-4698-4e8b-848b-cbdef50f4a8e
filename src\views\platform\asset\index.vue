<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="资源唯一键" prop="assetKey">
        <el-input
            v-model="queryParams.assetKey"
            placeholder="请输入资源唯一键"
            clearable
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="资源名称" prop="assetName">
        <el-input
            v-model="queryParams.assetName"
            placeholder="请输入资源名称"
            clearable
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['platform:asset:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['platform:asset:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['platform:asset:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['platform:asset:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="assetList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="资源名称" align="center" prop="assetName" min-width="180" />
      <el-table-column label="资源唯一键" align="center" prop="assetKey" min-width="200" :show-overflow-tooltip="true" />

      <el-table-column label="资源文件" align="center" prop="assetUrl" min-width="250">
        <template #default="scope">
          <div v-if="scope.row.assetUrl" style="display: flex; align-items: center; justify-content: center; gap: 8px;">
            <template v-if="isImage(scope.row.assetUrl)">
              <el-image
                  style="width: 50px; height: 50px; border-radius: 4px; flex-shrink: 0;"
                  :src="scope.row.assetUrl"
                  :preview-src-list="scope.row.assetUrl ? [scope.row.assetUrl] : []"
                  fit="contain"
                  hide-on-click-modal
              />
            </template>
            <el-icon v-else size="30"><Document /></el-icon>
            <el-link :href="scope.row.assetUrl" type="primary" target="_blank" style="text-align: left; flex-grow: 1;">
              {{ getFileName(scope.row.assetUrl) }}
            </el-link>
          </div>
          <span v-else>未上传</span>
        </template>
      </el-table-column>

      <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true" />
      <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150" fixed="right">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['platform:asset:edit']">修改</el-button>
          <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['platform:asset:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="assetRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="资源名称" prop="assetName">
          <el-input v-model="form.assetName" placeholder="请输入资源名称，方便理解" />
        </el-form-item>
        <el-form-item label="资源唯一键" prop="assetKey">
          <el-input v-model="form.assetKey" placeholder="请输入程序中使用的唯一Key" />
        </el-form-item>

        <el-form-item label="上传资源" prop="assetUrl">
          <file-upload v-model="form.assetUrl" :limit="1" :file-size="50" />
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Asset">
import { ref, reactive, toRefs, getCurrentInstance, nextTick, computed } from 'vue';
import { listAsset, getAsset, delAsset, addAsset, updateAsset } from "@/api/platform/asset";
import FileUpload from '@/components/FileUpload/index.vue';
import { Document } from '@element-plus/icons-vue';
import { ElImage } from 'element-plus';

const { proxy } = getCurrentInstance();

const assetList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    assetKey: null,
    assetName: null,
  },
  rules: {
    assetKey: [
      { required: true, message: "资源唯一键不能为空", trigger: "blur" }
    ],
    assetName: [
      { required: true, message: "资源名称不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 定义图片格式 */
const imageExtensions = computed(() => ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp', 'ico']);

/** 【最终修正版】判断是否是图片链接 */
function isImage(url) {
  if (!url) return false;
  try {
    // 1. 先移除URL中的查询参数 (例如 ?Expires=... 部分)
    const mainUrl = url.split('?')[0];
    // 2. 从清理后的URL中获取后缀名
    const extension = mainUrl.split('.').pop().toLowerCase();
    // 3. 判断后缀名是否存在于预定义的图片格式列表中
    return imageExtensions.value.includes(extension);
  } catch (e) {
    // 如果URL格式不正确导致解析失败，则安全地返回false
    return false;
  }
}

/** 从URL中提取文件名用于显示 */
function getFileName(url) {
  if (!url) return '';
  try {
    const path = new URL(url.split('?')[0]).pathname;
    return path.substring(path.lastIndexOf('/') + 1);
  } catch (e) {
    return url;
  }
}

/** 查询小程序静态资源配置列表 */
function getList() {
  loading.value = true;
  listAsset(queryParams.value).then(response => {
    assetList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    assetKey: null,
    assetName: null,
    assetUrl: null,
    remark: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null
  };
  proxy.resetForm("assetRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加小程序静态资源配置";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value[0];
  getAsset(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改小程序静态资源配置";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["assetRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateAsset(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          nextTick(() => {
            getList();
          });
        });
      } else {
        addAsset(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          nextTick(() => {
            getList();
          });
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除资源配置编号为"' + _ids + '"的数据项？').then(function() {
    return delAsset(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('platform/asset/export', {
    ...queryParams.value
  }, `asset_${new Date().getTime()}.xlsx`);
}

// 初始化加载列表
getList();
</script>