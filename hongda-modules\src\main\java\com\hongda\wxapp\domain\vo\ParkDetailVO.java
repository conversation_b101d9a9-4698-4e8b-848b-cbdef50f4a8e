package com.hongda.wxapp.domain.vo;

import com.hongda.common.annotation.OssUrl;
import lombok.Data;

/**
 * 园区详情页视图对象
 */
@Data
public class ParkDetailVO {

    /** 园区ID */
    private Long id;

    /** 园区名称 */
    private String name;

    /** 地理位置 */
    private String location;

    /** 主要产业 */
    private String mainIndustries;

    /** 园区简介/核心优势 */
    private String summary;

    /** 园区封面图URL */
    @OssUrl
    private String coverImageUrl;

    /** 园区详情介绍 (富文本) */
    private String content;

    /** 所属国家ID */
    private Long countryId;

    /** 所属国家名称 */
    private String countryName;
}