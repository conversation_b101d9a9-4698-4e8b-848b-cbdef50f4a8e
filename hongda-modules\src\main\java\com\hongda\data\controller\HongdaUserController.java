package com.hongda.data.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hongda.common.annotation.Log;
import com.hongda.common.core.controller.BaseController;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.common.enums.BusinessType;
import com.hongda.data.domain.HongdaUser;
import com.hongda.data.service.IHongdaUserService;
import com.hongda.common.utils.poi.ExcelUtil;
import com.hongda.common.core.page.TableDataInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * 小程序用户信息 (对应用户中心、登录功能)Controller
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Tag(name = "小程序用户管理", description = "小程序用户信息管理相关接口")
@RestController
@RequestMapping("/data/miniuser")
public class HongdaUserController extends BaseController
{
    @Autowired
    private IHongdaUserService hongdaUserService;

    /**
     * 查询小程序用户信息 (对应用户中心、登录功能)列表
     */
    @Operation(summary = "查询用户列表", description = "分页查询小程序用户信息列表")
    @PreAuthorize("@ss.hasPermi('data:miniuser:list')")
    @GetMapping("/list")
    public TableDataInfo list(HongdaUser hongdaUser)
    {
        startPage();
        List<HongdaUser> list = hongdaUserService.selectHongdaUserList(hongdaUser);
        return getDataTable(list);
    }

    /**
     * 导出小程序用户信息 (对应用户中心、登录功能)列表
     */
    @Operation(summary = "导出用户列表", description = "导出小程序用户信息到Excel文件")
    @PreAuthorize("@ss.hasPermi('data:miniuser:export')")
    @Log(title = "小程序用户信息 (对应用户中心、登录功能)", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HongdaUser hongdaUser)
    {
        List<HongdaUser> list = hongdaUserService.selectHongdaUserList(hongdaUser);
        ExcelUtil<HongdaUser> util = new ExcelUtil<HongdaUser>(HongdaUser.class);
        util.exportExcel(response, list, "小程序用户信息 (对应用户中心、登录功能)数据");
    }

    /**
     * 获取小程序用户信息 (对应用户中心、登录功能)详细信息
     */
    @Operation(summary = "获取用户详情", description = "根据用户ID获取用户详细信息")
    @PreAuthorize("@ss.hasPermi('data:miniuser:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@Parameter(description = "用户ID", required = true) @PathVariable("id") Long id)
    {
        return success(hongdaUserService.selectHongdaUserById(id));
    }

    /**
     * 新增小程序用户信息 (对应用户中心、登录功能)
     */
    @Operation(summary = "新增用户", description = "新增小程序用户信息")
    @PreAuthorize("@ss.hasPermi('data:miniuser:add')")
    @Log(title = "小程序用户信息 (对应用户中心、登录功能)", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Parameter(description = "用户信息", required = true) @RequestBody HongdaUser hongdaUser)
    {
        return toAjax(hongdaUserService.insertHongdaUser(hongdaUser));
    }

    /**
     * 修改小程序用户信息 (对应用户中心、登录功能)
     */
    @Operation(summary = "修改用户", description = "修改小程序用户信息")
    @PreAuthorize("@ss.hasPermi('data:miniuser:edit')")
    @Log(title = "小程序用户信息 (对应用户中心、登录功能)", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Parameter(description = "用户信息", required = true) @RequestBody HongdaUser hongdaUser)
    {
        return toAjax(hongdaUserService.updateHongdaUser(hongdaUser));
    }

    /**
     * 删除小程序用户信息 (对应用户中心、登录功能)
     */
    @Operation(summary = "删除用户", description = "批量删除小程序用户信息")
    @PreAuthorize("@ss.hasPermi('data:miniuser:remove')")
    @Log(title = "小程序用户信息 (对应用户中心、登录功能)", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@Parameter(description = "用户ID数组", required = true) @PathVariable Long[] ids)
    {
        return toAjax(hongdaUserService.deleteHongdaUserByIds(ids));
    }
}
