import request from '@/utils/request'

// 查询小程序用户信息 (对应用户中心、登录功能)列表
export function listMiniuser(query) {
  return request({
    url: '/data/miniuser/list',
    method: 'get',
    params: query
  })
}

// 查询小程序用户信息 (对应用户中心、登录功能)详细
export function getMiniuser(id) {
  return request({
    url: '/data/miniuser/' + id,
    method: 'get'
  })
}

// 新增小程序用户信息 (对应用户中心、登录功能)
export function addMiniuser(data) {
  return request({
    url: '/data/miniuser',
    method: 'post',
    data: data
  })
}

// 修改小程序用户信息 (对应用户中心、登录功能)
export function updateMiniuser(data) {
  return request({
    url: '/data/miniuser',
    method: 'put',
    data: data
  })
}

// 删除小程序用户信息 (对应用户中心、登录功能)
export function delMiniuser(id) {
  return request({
    url: '/data/miniuser/' + id,
    method: 'delete'
  })
}
