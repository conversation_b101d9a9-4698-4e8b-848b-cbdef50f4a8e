package com.hongda.framework.config;

// 【新增】导入日志相关的类
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import com.hongda.common.config.RuoYiConfig;
import com.hongda.common.constant.Constants;
import com.hongda.framework.interceptor.RepeatSubmitInterceptor;

/**
 * 通用配置
 * * <AUTHOR>
 */
@Configuration
public class ResourcesConfig implements WebMvcConfigurer
{
    // 【新增】创建一个日志记录器实例
    private static final Logger log = LoggerFactory.getLogger(ResourcesConfig.class);

    @Autowired
    private RepeatSubmitInterceptor repeatSubmitInterceptor;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry)
    {
        // 【核心修改】在程序启动时，打印出最终使用的文件路径
        String realPath = "file:" + RuoYiConfig.getProfile() + "/";
/*        log.info("====================== 静态资源映射诊断 ======================");
        log.info("URL请求路径前缀: {}/**", Constants.RESOURCE_PREFIX);
        log.info("映射到的物理磁盘路径: {}", realPath);
        log.info("============================================================");*/

        /** 本地文件上传路径 */
        registry.addResourceHandler(Constants.RESOURCE_PREFIX + "/**")
                .addResourceLocations(realPath);
    }

    /**
     * 自定义拦截规则
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry)
    {
        registry.addInterceptor(repeatSubmitInterceptor).addPathPatterns("/**");
    }

    /**
     * 跨域配置
     */
    @Bean
    public CorsFilter corsFilter()
    {
        CorsConfiguration config = new CorsConfiguration();
        config.addAllowedOriginPattern("*");
        config.addAllowedHeader("*");
        config.addAllowedMethod("*");
        config.setMaxAge(1800L);
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", config);
        return new CorsFilter(source);
    }
}
