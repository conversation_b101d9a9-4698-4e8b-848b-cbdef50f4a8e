<template>
  <div class="app-container">
    <el-form
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        v-show="showSearch"
        label-width="80px"
        class="search-form"
    >
      <el-form-item label="中文名称" prop="nameCn">
        <el-input
            v-model="queryParams.nameCn"
            placeholder="请输入中文名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属大洲" prop="continent">
        <el-select
            v-model="queryParams.continent"
            placeholder="请选择所属大洲"
            clearable
            style="width: 200px"
        >
          <el-option
              v-for="dict in hongda_country_continent"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
            v-model="queryParams.status"
            placeholder="请选择状态"
            clearable
            style="width: 200px"
        >
          <el-option
              v-for="dict in sys_show_hide"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['content:country:add']"
        >
          新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['content:country:edit']"
        >
          修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['content:country:remove']"
        >
          删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['content:country:export']"
        >
          导出
        </el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
        v-loading="loading"
        :data="countryList"
        @selection-change="handleSelectionChange"
        style="width: 100%"
        :default-sort="{ prop: 'id', order: 'descending' }"
        stripe
        border
        :height="tableHeight"
    >
      <el-table-column type="selection" min-width="55" align="center" fixed/>
      <el-table-column label="显示顺序" align="center" prop="sortOrder" width="100" sortable/>
      <el-table-column label="国家信息" align="center" min-width="220">
        <template #default="scope">
          <div class="country-info-cell">
            <image-preview :src="scope.row.flagUrl" :width="40" :height="28" class="country-flag"/>
            <div class="country-names">
              <div class="name-cn">{{ scope.row.nameCn }}</div>
              <div class="name-en">{{ scope.row.nameEn }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="所属大洲" align="center" prop="continent" min-width="100">
        <template #default="scope">
          <dict-tag :options="hongda_country_continent" :value="scope.row.continent"/>
        </template>
      </el-table-column>
      <el-table-column label="简介" align="center" prop="summary" min-width="180" :show-overflow-tooltip="true"/>
      <el-table-column label="详细信息预览" align="center" min-width="280" max-width="400">
        <template #default="scope">
          <div class="details-preview-container">
            <div class="preview-item">
              <span class="preview-label">国家简介:</span>
              <span class="preview-text-content">{{ generateSmartPreview(scope.row.introduction) }}</span>
            </div>
            <div class="preview-item">
              <span class="preview-label">基本信息:</span>
              <div v-html="generateJsonPreview(scope.row.basicInfoJson)"></div>
            </div>
            <div class="preview-item">
              <span class="preview-label">招商政策:</span>
              <span class="preview-text-content">{{ generateSmartPreview(scope.row.investmentPolicy) }}</span>
            </div>
            <el-button
                link
                type="primary"
                @click="handleViewDetails(scope.row)"
                style="margin-top: 8px;"
            >
              查看全部详情
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" min-width="80">
        <template #default="scope">
          <dict-tag :options="sys_show_hide" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180" fixed="right">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleViewDetails(scope.row)">预览</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                     v-hasPermi="['content:country:edit']">修改
          </el-button>
          <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['content:country:remove']">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
        class="pagination-container"
    />

    <el-dialog :title="title" v-model="open" width="80%" top="5vh" append-to-body :close-on-click-modal="false"
               :close-on-press-escape="false" class="form-dialog" @close="cancel">
      <el-form ref="countryRef" :model="form" :rules="rules" label-width="120px" class="dialog-form">
        <el-tabs v-model="activeTab" class="form-tabs">
          <el-tab-pane label="核心信息" name="core">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="中文名称" prop="nameCn">
                  <el-input v-model="form.nameCn" placeholder="请输入中文名称" maxlength="50" show-word-limit/>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="英文名称" prop="nameEn">
                  <el-input v-model="form.nameEn" placeholder="请输入英文名称" maxlength="100" show-word-limit/>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="所属大洲" prop="continent">
                  <el-select v-model="form.continent" placeholder="请选择所属大洲" style="width: 100%;">
                    <el-option v-for="dict in hongda_country_continent" :key="dict.value" :label="dict.label"
                               :value="dict.value"/>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="状态" prop="status">
                  <el-radio-group v-model="form.status">
                    <el-radio v-for="dict in sys_show_hide" :key="dict.value" :label="dict.value">{{
                        dict.label
                      }}
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="显示顺序" prop="sortOrder">
                  <el-input-number v-model="form.sortOrder" controls-position="right" :min="0"/>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="一句话简介" prop="summary">
                  <el-input v-model="form.summary" type="textarea" placeholder="请输入简介，用于列表页卡片展示" :rows="3"
                            maxlength="200" show-word-limit/>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="国旗图片" prop="flagUrl">
                  <image-upload v-model="form.flagUrl" :limit="1"/>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="列表封面图" prop="listCoverUrl">
                  <image-upload v-model="form.listCoverUrl" :limit="1"/>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="详情页大图" prop="detailsCoverUrl">
                  <image-upload v-model="form.detailsCoverUrl" :limit="1"/>
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>

          <el-tab-pane label="基本信息与简介" name="basic">
            <el-form-item label="国家简介">
              <el-input v-model="form.introduction" type="textarea" :rows="12" placeholder="请输入国家简介"/>
            </el-form-item>
            <el-form-item label="基本信息项">
              <div class="basic-info-list">
                <div v-for="(item, index) in basicInfoList" :key="`item-${index}`" class="dynamic-item">
                  <el-input v-model="item.key" placeholder="属性名 (如：首都)" class="dynamic-item-key" maxlength="20"/>
                  <el-input v-model="item.value" placeholder="属性值 (如：新加坡)" class="dynamic-item-value"
                            maxlength="100"/>
                  <el-button @click="removeBasicInfoItem(index)" :icon="Delete" circle plain type="danger"
                             size="small"/>
                </div>
                <el-button @click="addBasicInfoItem" :icon="Plus" type="primary" plain class="add-item-btn">
                  添加基本信息项
                </el-button>
              </div>
            </el-form-item>
          </el-tab-pane>

          <el-tab-pane label="各项政策" name="policies">
            <el-form-item label="招商政策">
              <el-input v-model="form.investmentPolicy" type="textarea" :rows="8" placeholder="请输入招商政策"/>
            </el-form-item>
            <el-form-item label="海关政策">
              <el-input v-model="form.customsPolicy" type="textarea" :rows="8" placeholder="请输入海关政策"/>
            </el-form-item>
            <el-form-item label="税务政策">
              <el-input v-model="form.taxPolicy" type="textarea" :rows="8" placeholder="请输入税务政策"/>
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitting">{{
              submitting ? '提交中...' : '确 定'
            }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog :title="detailViewTitle" v-model="detailViewVisible" width="85%" top="5vh" append-to-body
               class="rich-text-dialog" :close-on-click-modal="false">
      <el-tabs v-model="activeContentField" tab-position="left" style="min-height: 60vh;" class="detail-tabs">
        <el-tab-pane label="基本信息" name="basicInfo">
          <div class="basic-info-display">
            <el-descriptions :column="2" border>
              <el-descriptions-item v-for="item in parsedBasicInfo" :key="item.key" :label="item.key">{{
                  item.value
                }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-tab-pane>

        <el-tab-pane v-for="field in detailTextFields" :key="field.name" :label="field.label" :name="field.name">
          <div class="plain-text-content">
            {{ (selectedCountry && selectedCountry[field.name]) || '（无内容）' }}
          </div>
        </el-tab-pane>
      </el-tabs>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="copyContent" type="primary" icon="CopyDocument">复制当前内容</el-button>
          <el-button @click="detailViewVisible = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Country">
import {listCountry, getCountry, delCountry, addCountry, updateCountry} from "@/api/content/country";
import {ref, reactive, toRefs, onMounted, computed, watch, nextTick, getCurrentInstance, onUnmounted} from 'vue';
import {Delete, Plus} from '@element-plus/icons-vue';

// 【修改】移除了不再需要的库
// import he from 'he';
// import DOMPurify from 'dompurify';
// import hljs from 'highlight.js/lib/core';
// import xml from 'highlight.js/lib/languages/xml';
// import 'highlight.js/styles/atom-one-dark.css';

// hljs.registerLanguage('html', xml);

const {proxy} = getCurrentInstance();
const {hongda_country_continent, sys_show_hide} = proxy.useDict('hongda_country_continent', 'sys_show_hide');

const countryList = ref([]);
const open = ref(false);
const loading = ref(true);
const submitting = ref(false);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const activeTab = ref("core");
const tableHeight = ref(null);
const basicInfoList = ref([]);

const data = reactive({
  form: {
    id: null,
    nameCn: null,
    nameEn: null,
    summary: null,
    continent: null,
    flagUrl: null,
    listCoverUrl: null,
    detailsCoverUrl: null,
    introduction: null,
    basicInfoJson: null,
    investmentPolicy: null,
    customsPolicy: null,
    taxPolicy: null,
    laborPolicy: null,
    status: "0",
    sortOrder: 0,
  },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    nameCn: null,
    continent: null,
    status: null,
  },
  rules: {
    nameCn: [{required: true, message: "中文名称不能为空", trigger: "blur"}],
    continent: [{required: true, message: "所属大洲不能为空", trigger: "change"}],
    status: [{required: true, message: "状态不能为空", trigger: "change"}]
  }
});

const {queryParams, form, rules} = toRefs(data);

const parsedBasicInfo = computed(() => {
  if (!selectedCountry.value?.basicInfoJson) return [];
  try {
    const data = JSON.parse(selectedCountry.value.basicInfoJson);
    return Array.isArray(data) ? data.filter(item => item.key && item.value) : [];
  } catch (e) {
    return [];
  }
});

const updateTableHeight = () => {
  const windowHeight = window.innerHeight;
  const headerHeight = 160;
  const paginationHeight = 60;
  const padding = 40;
  tableHeight.value = windowHeight - headerHeight - paginationHeight - padding;
};

onMounted(() => {
  getList();
  updateTableHeight();
  window.addEventListener('resize', updateTableHeight);
});

onUnmounted(() => {
  window.removeEventListener('resize', updateTableHeight);
});

async function getList() {
  try {
    loading.value = true;
    const response = await listCountry(queryParams.value);
    countryList.value = response.rows;
    total.value = response.total;
  } catch (error) {
    proxy.$modal.msgError("获取数据失败");
  } finally {
    loading.value = false;
  }
}

function reset() {
  form.value = {
    id: null,
    nameCn: null,
    nameEn: null,
    summary: null,
    continent: null,
    flagUrl: null,
    listCoverUrl: null,
    detailsCoverUrl: null,
    // 【重要】将所有文本域字段显式地设置为null或空字符串
    introduction: null,
    basicInfoJson: null,
    investmentPolicy: null,
    customsPolicy: null,
    taxPolicy: null,
    laborPolicy: null,
    status: "0",
    sortOrder: 0,
  };
  basicInfoList.value = [];
  proxy.resetForm("countryRef");
}

function cancel() {
  open.value = false;
  reset();
}

function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

function handleAdd() {
  reset();
  activeTab.value = "core";
  open.value = true;
  title.value = "添加国别信息";
}

async function handleUpdate(row) {
  try {
    reset();
    activeTab.value = "core";
    const _id = row?.id || ids.value[0];
    const response = await getCountry(_id);
    form.value = response.data;
    if (form.value.basicInfoJson) {
      basicInfoList.value = JSON.parse(form.value.basicInfoJson) || [];
    } else {
      basicInfoList.value = [];
    }
    open.value = true;
    title.value = "修改国别信息";
  } catch (error) {
    proxy.$modal.msgError("获取数据失败");
  }
}

async function submitForm() {
  try {
    const valid = await proxy.$refs["countryRef"].validate();
    if (!valid) return;
    submitting.value = true;
    const validItems = basicInfoList.value.filter(item => item.key && item.key.trim() && item.value && item.value.trim());
    form.value.basicInfoJson = JSON.stringify(validItems);
    if (form.value.id != null) {
      await updateCountry(form.value);
      proxy.$modal.msgSuccess("修改成功");
    } else {
      await addCountry(form.value);
      proxy.$modal.msgSuccess("新增成功");
    }
    open.value = false;
    getList();
  } catch (error) {
    proxy.$modal.msgError(form.value.id ? "修改失败" : "新增失败");
  } finally {
    submitting.value = false;
  }
}

async function handleDelete(row) {
  try {
    const _ids = row?.id || ids.value;
    const names = row ? row.nameCn : countryList.value.filter(item => ids.value.includes(item.id)).map(item => item.nameCn).join('、');
    await proxy.$modal.confirm(`是否确认删除国别信息"${names}"？`);
    await delCountry(_ids);
    getList();
    proxy.$modal.msgSuccess("删除成功");
  } catch (error) {
    if (error !== 'cancel') proxy.$modal.msgError("删除失败");
  }
}

function handleExport() {
  proxy.download('content/country/export', {...queryParams.value}, `country_${new Date().getTime()}.xlsx`);
}

function addBasicInfoItem() {
  basicInfoList.value.push({key: '', value: ''});
}

function removeBasicInfoItem(index) {
  basicInfoList.value.splice(index, 1);
}

// --- 详情预览相关 ---
const detailViewVisible = ref(false);
const detailViewTitle = ref('');
const selectedCountry = ref(null);
const activeContentField = ref('basicInfo');

// 【修改】重命名为 detailTextFields
const detailTextFields = [
  {name: 'introduction', label: '国家简介'},
  {name: 'investmentPolicy', label: '招商政策'},
  {name: 'customsPolicy', label: '海关政策'},
  {name: 'taxPolicy', label: '税务政策'},
];

// 查看详情
function handleViewDetails(row) {
  selectedCountry.value = row;
  detailViewTitle.value = `查看详情: ${row.nameCn}`;
  activeContentField.value = 'basicInfo';
  detailViewVisible.value = true;
}

// 【修改】简化复制逻辑
async function copyContent() {
  try {
    let contentToCopy = '';
    const fieldName = activeContentField.value;

    if (!selectedCountry.value) return;

    if (fieldName === 'basicInfo') {
      contentToCopy = parsedBasicInfo.value
          .map(item => `${item.key}: ${item.value}`)
          .join('\n');
    } else {
      contentToCopy = selectedCountry.value[fieldName] || '';
    }

    if (contentToCopy) {
      await navigator.clipboard.writeText(contentToCopy);
      proxy.$modal.msgSuccess('内容已复制到剪贴板');
    } else {
      proxy.$modal.msgWarning('没有可复制的内容');
    }
  } catch (err) {
    proxy.$modal.msgError('复制失败');
  }
}


// --- 表格内预览生成函数 ---
// 【修改】简化为纯文本处理
function generateSmartPreview(text = '') {
  if (!text) return '（无）';
  const plainText = String(text).trim();
  if (!plainText) return '（空）';
  return plainText.substring(0, 30) + (plainText.length > 30 ? '...' : '');
}

function generateJsonPreview(jsonStr = '') {
  if (!jsonStr) return '<span class="preview-text-empty">（无）</span>';
  try {
    const data = JSON.parse(jsonStr);
    if (!Array.isArray(data)) return '<span class="preview-text-error">（数据格式错误）</span>';
    const validItems = data.filter(item => item.key && item.value);
    if (validItems.length === 0) return '<span class="preview-text-empty">（无有效数据）</span>';
    let html = validItems.slice(0, 2).map(item =>
        `<div class="json-preview-item">
        <span class="json-key">${item.key}:</span>
        <span class="json-value">${String(item.value).substring(0, 10)}...</span>
      </div>`
    ).join('');
    if (validItems.length > 2) {
      html += '<span class="json-more">等' + validItems.length + '项</span>';
    }
    return html;
  } catch (e) {
    return '<span class="preview-text-error">（JSON格式错误）</span>';
  }
}
</script>

<style scoped>
/* === 全局样式 === */
.app-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* === 搜索表单样式 === */
.search-form {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* === 表格样式优化 === */
.el-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: white;
}

.el-table .el-table__header-wrapper {
  background: #fafafa;
}

.el-table .el-table__row:hover {
  background-color: #f5f7fa;
}

/* === 国家信息单元格样式 === */
.country-info-cell {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

.country-flag {
  flex-shrink: 0;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.country-names {
  flex: 1;
  min-width: 0;
}

.name-cn {
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px;
}

.name-en {
  font-size: 12px;
  color: #909399;
  line-height: 1.3;
  margin-top: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* === 详情预览容器样式 === */
.details-preview-container {
  padding: 12px;
  text-align: left;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.preview-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  font-size: 12px;
  line-height: 1.5;
}

.preview-item:last-of-type {
  margin-bottom: 6px;
}

.preview-label {
  font-weight: 600;
  color: #606266;
  margin-right: 6px;
  white-space: nowrap;
  min-width: 60px;
}

.preview-text-content {
  color: #303133;
  flex: 1;
}

.preview-text-empty {
  color: #c0c4cc;
  font-style: italic;
}

.preview-text-error {
  color: #f56c6c;
  font-style: italic;
}

/* JSON预览样式 */
.json-preview-item {
  margin-bottom: 3px;
  line-height: 1.4;
}

.json-key {
  font-weight: 600;
  color: #606266;
}

.json-value {
  color: #303133;
}

.json-more {
  color: #909399;
  font-size: 11px;
  margin-left: 4px;
}

/* === 分页样式 === */
.pagination-container {
  margin-top: 20px;
  text-align: center;
}

/* === 弹窗样式 === */
.form-dialog .el-dialog__body {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.dialog-form .el-form-item {
  margin-bottom: 20px;
}

.form-tabs .el-tabs__content {
  padding-top: 20px;
}

/* === 动态表单项样式 === */
.basic-info-list {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;
}

.dynamic-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.dynamic-item:last-of-type {
  margin-bottom: 16px;
}

.dynamic-item-key {
  width: 150px;
  flex-shrink: 0;
}

.dynamic-item-value {
  flex: 1;
}

.add-item-btn {
  width: 100%;
}

/* === 详情弹窗样式 === */
.rich-text-dialog .el-dialog__body {
  padding: 10px 20px;
}

.detail-tabs .el-tabs--left .el-tabs__content {
  padding-left: 20px;
}

.basic-info-display {
  padding: 20px;
  background: white;
  border-radius: 6px;
}

/* 【修改】这个样式现在用于显示所有详情文本 */
.plain-text-content {
  height: 55vh;
  overflow-y: auto;
  padding: 20px;
  background-color: #fafafa;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  line-height: 1.6;
  white-space: pre-wrap; /* 核心：保留换行和空格 */
  word-wrap: break-word;
  font-size: 14px;
  color: #303133;
}
</style>