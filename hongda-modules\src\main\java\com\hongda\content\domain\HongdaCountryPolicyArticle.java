package com.hongda.content.domain;

import com.hongda.common.annotation.OssUrl;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.hongda.common.annotation.Excel;
import com.hongda.common.core.domain.BaseEntity;

/**
 * 国别政策管理对象 hongda_country_policy_article
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
public class HongdaCountryPolicyArticle extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 文章主键ID */
    private Long articleId;

    /** 关联的国家ID */
    private Long countryId;

    /** 政策大类 */
    @Excel(name = "政策大类")
    private String policyType;

    /** 文章子分类 */
    @Excel(name = "文章子分类")
    private String categoryName;

    /** 文章标题 */
    @Excel(name = "文章标题")
    private String title;

    /** 文章摘要 */
    @Excel(name = "文章摘要")
    private String summary;

    /** 文章正文 */
    @Excel(name = "文章正文")
    private String content;

    /** 封面图URL */
    @Excel(name = "封面图URL")
    @OssUrl
    private String coverImage;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    /** 显示顺序 */
    @Excel(name = "显示顺序")
    private Long sortOrder;

    private String countryName;

    public void setCountryName(String countryName)
    {
        this.countryName = countryName;
    }

    public String getCountryName()
    {
        return countryName;
    }

    public void setArticleId(Long articleId) 
    {
        this.articleId = articleId;
    }

    public Long getArticleId() 
    {
        return articleId;
    }

    public void setCountryId(Long countryId) 
    {
        this.countryId = countryId;
    }

    public Long getCountryId() 
    {
        return countryId;
    }

    public void setPolicyType(String policyType) 
    {
        this.policyType = policyType;
    }

    public String getPolicyType() 
    {
        return policyType;
    }

    public void setCategoryName(String categoryName) 
    {
        this.categoryName = categoryName;
    }

    public String getCategoryName() 
    {
        return categoryName;
    }

    public void setTitle(String title) 
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }

    public void setSummary(String summary) 
    {
        this.summary = summary;
    }

    public String getSummary() 
    {
        return summary;
    }

    public void setContent(String content) 
    {
        this.content = content;
    }

    public String getContent() 
    {
        return content;
    }

    public void setCoverImage(String coverImage) 
    {
        this.coverImage = coverImage;
    }

    public String getCoverImage() 
    {
        return coverImage;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setSortOrder(Long sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Long getSortOrder() 
    {
        return sortOrder;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("articleId", getArticleId())
            .append("countryId", getCountryId())
            .append("policyType", getPolicyType())
            .append("categoryName", getCategoryName())
            .append("title", getTitle())
            .append("summary", getSummary())
            .append("content", getContent())
            .append("coverImage", getCoverImage())
            .append("status", getStatus())
            .append("sortOrder", getSortOrder())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
