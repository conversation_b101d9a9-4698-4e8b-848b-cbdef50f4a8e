package com.hongda.framework.security.filter;

import java.io.IOException;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import com.hongda.common.core.domain.model.LoginUser;
import com.hongda.common.utils.SecurityUtils;
import com.hongda.common.utils.StringUtils;
import com.hongda.framework.web.service.TokenService;

/**
 * token过滤器 验证token有效性
 * 
 * <AUTHOR>
 */
@Component
public class JwtAuthenticationTokenFilter extends OncePerRequestFilter
{
    @Autowired
    private TokenService tokenService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
            throws ServletException, IOException
    {
        // 检查是否是微信小程序相关的请求路径
        String requestURI = request.getRequestURI();
        boolean isWxAppRequest = requestURI.contains("/api/v1/wxapp/") || 
                                requestURI.contains("/common/upload") ||
                                requestURI.contains("/api/v1/login");
        
        if (isWxAppRequest) {
            // 对于微信小程序请求，验证微信用户Token但不设置Spring Security上下文
            Long wxUserId = tokenService.getWxUserId(request);
            if (wxUserId != null) {
                // Token有效，但不设置Spring Security认证信息
                // 微信用户的认证通过getWxUserId方法已经完成
            }
        } else {
            // 传统Web用户的认证流程
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (StringUtils.isNotNull(loginUser) && StringUtils.isNull(SecurityUtils.getAuthentication()))
            {
                tokenService.verifyToken(loginUser);
                UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(loginUser, null, loginUser.getAuthorities());
                authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                SecurityContextHolder.getContext().setAuthentication(authenticationToken);
            }
        }
        
        chain.doFilter(request, response);
    }
}
