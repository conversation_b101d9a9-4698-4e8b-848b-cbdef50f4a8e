package com.hongda.content.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hongda.common.annotation.Log;
import com.hongda.common.core.controller.BaseController;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.common.enums.BusinessType;
import com.hongda.content.domain.HongdaArticle;
import com.hongda.content.service.IHongdaArticleService;
import com.hongda.common.utils.poi.ExcelUtil;
import com.hongda.common.core.page.TableDataInfo;

/**
 * 资讯文章Controller
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
@RestController
@RequestMapping("/content/article")
public class HongdaArticleController extends BaseController
{
    @Autowired
    private IHongdaArticleService hongdaArticleService;

    /**
     * 查询资讯文章列表
     */
    @PreAuthorize("@ss.hasPermi('content:article:list')")
    @GetMapping("/list")
    public TableDataInfo list(HongdaArticle hongdaArticle)
    {
        startPage();
        List<HongdaArticle> list = hongdaArticleService.selectHongdaArticleList(hongdaArticle);
        return getDataTable(list);
    }

    /**
     * 导出资讯文章列表
     */
    @PreAuthorize("@ss.hasPermi('content:article:export')")
    @Log(title = "资讯文章", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HongdaArticle hongdaArticle)
    {
        List<HongdaArticle> list = hongdaArticleService.selectHongdaArticleList(hongdaArticle);
        ExcelUtil<HongdaArticle> util = new ExcelUtil<HongdaArticle>(HongdaArticle.class);
        util.exportExcel(response, list, "资讯文章数据");
    }

    /**
     * 获取资讯文章详细信息
     */
    @PreAuthorize("@ss.hasPermi('content:article:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(hongdaArticleService.selectHongdaArticleById(id));
    }

    /**
     * 新增资讯文章
     */
    @PreAuthorize("@ss.hasPermi('content:article:add')")
    @Log(title = "资讯文章", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HongdaArticle hongdaArticle)
    {
        return toAjax(hongdaArticleService.insertHongdaArticle(hongdaArticle));
    }

    /**
     * 修改资讯文章
     */
    @PreAuthorize("@ss.hasPermi('content:article:edit')")
    @Log(title = "资讯文章", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HongdaArticle hongdaArticle)
    {
        return toAjax(hongdaArticleService.updateHongdaArticle(hongdaArticle));
    }

    /**
     * 删除资讯文章
     */
    @PreAuthorize("@ss.hasPermi('content:article:remove')")
    @Log(title = "资讯文章", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(hongdaArticleService.deleteHongdaArticleByIds(ids));
    }
}
