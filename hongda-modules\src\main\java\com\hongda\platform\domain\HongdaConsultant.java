package com.hongda.platform.domain;

import com.hongda.common.annotation.OssUrl;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.hongda.common.annotation.Excel;
import com.hongda.common.annotation.OssUrl;
import com.hongda.common.core.domain.BaseEntity;

/**
 * 顾问管理对象 hongda_consultant
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
public class HongdaConsultant extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 顾问ID */
    private Long id;

    /** 顾问名称 */
    @Excel(name = "顾问名称")
    private String name;

    /** 顾问头像 */
    @Excel(name = "顾问头像")
    @OssUrl
    private String avatarUrl;

    /** 简介 */
    @Excel(name = "简介")
    private String introduction;

    /** 二维码图片 */
    @Excel(name = "二维码图片")
    @OssUrl
    private String qrCodeUrl;

    /** 显示顺序 */
    @Excel(name = "显示顺序")
    private Long sortOrder;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }

    public void setAvatarUrl(String avatarUrl) 
    {
        this.avatarUrl = avatarUrl;
    }

    public String getAvatarUrl() 
    {
        return avatarUrl;
    }

    public void setIntroduction(String introduction) 
    {
        this.introduction = introduction;
    }

    public String getIntroduction() 
    {
        return introduction;
    }

    public void setQrCodeUrl(String qrCodeUrl) 
    {
        this.qrCodeUrl = qrCodeUrl;
    }

    public String getQrCodeUrl() 
    {
        return qrCodeUrl;
    }

    public void setSortOrder(Long sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Long getSortOrder() 
    {
        return sortOrder;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("avatarUrl", getAvatarUrl())
            .append("introduction", getIntroduction())
            .append("qrCodeUrl", getQrCodeUrl())
            .append("sortOrder", getSortOrder())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
