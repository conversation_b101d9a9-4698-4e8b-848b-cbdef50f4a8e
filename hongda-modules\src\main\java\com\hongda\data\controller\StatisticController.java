package com.hongda.data.controller;

import java.util.List;
import java.util.Map;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hongda.common.core.controller.BaseController;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.data.service.IStatisticService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * 数据统计Controller
 * 
 * <AUTHOR>
 * @date 2025-01-22
 */
@Tag(name = "数据统计管理", description = "数据指挥中心统计接口")
@RestController
@RequestMapping("/data/statistic")
public class StatisticController extends BaseController
{
    @Autowired
    private IStatisticService statisticService;

    /**
     * 获取核心指标统计数据
     */
    @Operation(summary = "获取核心指标统计", description = "获取系统核心指标统计数据，包括总用户数、总活动数、总报名数等")
    @PreAuthorize("@ss.hasPermi('data:registration:list')")
    @GetMapping("/summary")
    public AjaxResult getSummary()
    {
        Map<String, Object> summary = statisticService.getSummaryStatistics();
        return success(summary);
    }

    /**
     * 获取热门活动报名排行TOP5
     */
    @Operation(summary = "获取热门活动排行", description = "获取报名人数最多的前5个活动")
    @PreAuthorize("@ss.hasPermi('data:registration:list')")
    @GetMapping("/top-events")
    public AjaxResult getTopEvents()
    {
        Map<String, Object> topEvents = statisticService.getTopEventsStatistics();
        return success(topEvents);
    }

    /**
     * 获取近7日报名趋势
     */
    @Operation(summary = "获取报名趋势", description = "获取近7日的报名数据趋势")
    @PreAuthorize("@ss.hasPermi('data:registration:list')")
    @GetMapping("/daily-trend")
    public AjaxResult getDailyTrend()
    {
        Map<String, Object> dailyTrend = statisticService.getDailyTrendStatistics();
        return success(dailyTrend);
    }
}