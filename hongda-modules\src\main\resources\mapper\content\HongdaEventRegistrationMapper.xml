<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongda.content.mapper.HongdaEventRegistrationMapper">
    
    <resultMap type="HongdaEventRegistration" id="HongdaEventRegistrationResult">
        <result property="id"            column="id"              />
        <result property="eventId"       column="event_id"        />
        <result property="userId"        column="user_id"         />
        <result property="formData"      column="form_data"       />
        <result property="status"        column="status"          />
        <result property="createTime"    column="registration_time"     />
    </resultMap>

    <sql id="selectHongdaEventRegistrationVo">
        select id, event_id, user_id, form_data, status, registration_time
        from hongda_event_registration
    </sql>

    <select id="selectRegistrationListByEventId" parameterType="Long" resultMap="HongdaEventRegistrationResult">
        <include refid="selectHongdaEventRegistrationVo"/>
        where event_id = #{eventId}
        order by registration_time desc
    </select>
        
    <insert id="insertRegistration" parameterType="HongdaEventRegistration" useGeneratedKeys="true" keyProperty="id">
        insert into hongda_event_registration
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="eventId != null">event_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="formData != null">form_data,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">registration_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="eventId != null">#{eventId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="formData != null">#{formData},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <delete id="deleteRegistrationById" parameterType="Long">
        delete from hongda_event_registration where id = #{id}
    </delete>

</mapper>