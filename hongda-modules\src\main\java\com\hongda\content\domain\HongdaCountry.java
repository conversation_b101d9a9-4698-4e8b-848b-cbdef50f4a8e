package com.hongda.content.domain;

import com.hongda.common.annotation.Excel;
import com.hongda.common.annotation.OssUrl;
import com.hongda.common.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 国别信息对象 hongda_country (V2.1)
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "国别信息实体对象")
public class HongdaCountry extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 国家ID, 主键 */
    @Schema(description = "国家ID, 主键")
    private Long id;

    /** 中文名称 */
    @Excel(name = "中文名称")
    @Schema(description = "中文名称")
    private String nameCn;

    /** 英文名称 */
    @Excel(name = "英文名称")
    @Schema(description = "英文名称")
    private String nameEn;

    /** 一句话简介 (用于列表卡片) */
    @Excel(name = "一句话简介")
    @Schema(description = "一句话简介 (用于列表卡片)")
    private String summary;

    /** 所属大洲 */
    @Excel(name = "所属大洲", readConverterExp = "ASIA=亚洲,EUROPE=欧洲,NORTH_AMERICA=北美洲,SOUTH_AMERICA=南美洲,AFRICA=非洲,OCEANIA=大洋洲")
    @Schema(description = "所属大洲 (字典: hongda_country_continent)")
    private String continent;

    /** 国旗图片URL */
    @Excel(name = "国旗图片URL")
    @Schema(description = "国旗图片URL")
    @OssUrl
    private String flagUrl;

    /** 列表封面图URL */
    @Excel(name = "列表封面图URL")
    @Schema(description = "列表封面图URL")
    @OssUrl
    private String listCoverUrl;

    /** 详情页顶部大图URL */
    @Excel(name = "详情页顶部大图URL")
    @Schema(description = "详情页顶部大图URL")
    @OssUrl
    private String detailsCoverUrl;

    /** 国家简介 (富文本) */
    @Excel(name = "国家简介")
    @Schema(description = "国家简介 (富文本)")
    private String introduction;

    /** 基本信息 (JSON格式) */
    @Excel(name = "基本信息 (JSON)")
    @Schema(description = "基本信息 (JSON格式)")
    private String basicInfoJson;

    /** 招商政策 (富文本) */
    @Excel(name = "招商政策")
    @Schema(description = "招商政策 (富文本)")
    private String investmentPolicy;

    /** 海关政策 (富文本) */
    @Excel(name = "海关政策")
    @Schema(description = "海关政策 (富文本)")
    private String customsPolicy;

    /** 税务政策 (富文本) */
    @Excel(name = "税务政策")
    @Schema(description = "税务政策 (富文本)")
    private String taxPolicy;

    /** 状态 (0=显示, 1=隐藏) */
    @Excel(name = "状态", readConverterExp = "0=显示,1=隐藏")
    @Schema(description = "状态 (字典: sys_show_hide)")
    private String status;

    /** 显示顺序 */
    @Excel(name = "显示顺序")
    @Schema(description = "显示顺序")
    private Integer sortOrder;
}