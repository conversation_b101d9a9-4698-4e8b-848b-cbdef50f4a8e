/*
  核心：
  这个文件定义了文章内容在小程序中的最终外观。
  它将被 WangEditor 编辑器引用，以实现“所见即所得”。
  所有样式都包裹在一个父选择器下，以确保它们只在编辑器内部生效。
*/
.w-e-text-container [data-slate-editor] {
  // 全局字体与段落
  p {
    line-height: 1.8;
    margin: 1.2em 0;
    color: #2c3e50;
  }

  // 各级标题
  h1 {
    font-size: 1.8em;
    font-weight: bold;
    margin: 1.5em 0 1em 0;
    color: #2c3e50;
    border-bottom: 3px solid #6c757d;
    padding-bottom: 0.5em;
  }

  h2 {
    font-size: 1.5em;
    font-weight: bold;
    margin: 1.4em 0 1em 0;
    color: #2c3e50;
    border-bottom: 2px solid #6c757d;
    padding-bottom: 0.3em;
  }

  h3 {
    font-size: 1.25em;
    font-weight: bold;
    margin: 1.3em 0 0.8em 0;
    color: #2c3e50;
    border-left: 4px solid #6c757d;
    padding-left: 15px;
  }

  // 引用块
  blockquote {
    border-left: 4px solid #6c757d;
    padding: 15px 20px;
    margin: 20px 0;
    background: #f8f9fa;
    border-radius: 8px;
    color: #495057;
    font-style: italic;
  }

  // 代码块
  pre {
    background: #2d3748;
    color: #e2e8f0;
    padding: 20px;
    margin: 20px 0;
    border-radius: 8px;
    font-family: "Fira Code", monospace;
    overflow-x: auto;
  }

  code {
    font-family: "Fira Code", monospace;
    background: #f7fafc;
    color: #e53e3e;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.9em;
    border: 1px solid #e2e8f0;
  }

  // 列表
  ul, ol {
    padding-left: 30px;
    margin: 15px 0;
  }

  li {
    margin-bottom: 8px;
    line-height: 1.6;
    color: #2c3e50;
  }

  // 表格
  table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    border-radius: 8px;
    overflow: hidden;
  }

  th {
    background: #f8f9fa;
    color: #495057;
    padding: 12px 15px;
    text-align: left;
    font-weight: bold;
    border: 1px solid #dee2e6;
  }

  td {
    border: 1px solid #dee2e6;
    padding: 12px 15px;
    background: #fff;
    color: #2c3e50;
  }

  // 图片
  img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    display: block;
    margin: 20px auto;
  }
}