package com.hongda.content.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.hongda.content.domain.HongdaEvent;

/**
 * 活动管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@Mapper
public interface HongdaEventMapper 
{
    /**
     * 查询活动管理
     * 
     * @param id 活动管理主键
     * @return 活动管理
     */
    public HongdaEvent selectHongdaEventById(Long id);

    /**
     * 查询活动管理列表
     * 
     * @param hongdaEvent 活动管理
     * @return 活动管理集合
     */
    public List<HongdaEvent> selectHongdaEventList(HongdaEvent hongdaEvent);

    /**
     * 新增活动管理
     * 
     * @param hongdaEvent 活动管理
     * @return 结果
     */
    public int insertHongdaEvent(HongdaEvent hongdaEvent);

    /**
     * 修改活动管理
     * 
     * @param hongdaEvent 活动管理
     * @return 结果
     */
    public int updateHongdaEvent(HongdaEvent hongdaEvent);

    /**
     * 删除活动管理
     * 
     * @param id 活动管理主键
     * @return 结果
     */
    public int deleteHongdaEventById(Long id);

    /**
     * 批量删除活动管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHongdaEventByIds(Long[] ids);

    /**
     * 批量更新已过报名截止时间但未结束的活动状态为"报名中"
     * 
     * @return 更新的记录数
     */
    public int updateStatusToOngoing();

    /**
     * 批量更新已过结束时间的活动状态为"已结束"
     * 
     * @return 更新的记录数
     */
    public int updateStatusToEnded();

    /**
     * 增加活动报名人数
     * 
     * @param eventId 活动ID
     * @return 更新的记录数
     */
    public int incrementRegisteredCount(Long eventId);

    /**
     * 减少活动报名人数
     * 
     * @param eventId 活动ID
     * @return 更新的记录数
     */
    public int decrementRegisteredCount(Long eventId);

    /**
     * 查询推广活动列表
     * 
     * @return 推广活动列表
     */
    public List<HongdaEvent> selectPromotionEventList();

}
