package com.hongda.data.mapper;

import java.util.List;
import com.hongda.data.domain.HongdaUser;

/**
 * 小程序用户信息 (对应用户中心、登录功能)Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface HongdaUserMapper 
{
    /**
     * 查询小程序用户信息 (对应用户中心、登录功能)
     * 
     * @param id 小程序用户信息 (对应用户中心、登录功能)主键
     * @return 小程序用户信息 (对应用户中心、登录功能)
     */
    public HongdaUser selectHongdaUserById(Long id);

    /**
     * 查询小程序用户信息 (对应用户中心、登录功能)列表
     * 
     * @param hongdaUser 小程序用户信息 (对应用户中心、登录功能)
     * @return 小程序用户信息 (对应用户中心、登录功能)集合
     */
    public List<HongdaUser> selectHongdaUserList(HongdaUser hongdaUser);

    /**
     * 新增小程序用户信息 (对应用户中心、登录功能)
     * 
     * @param hongdaUser 小程序用户信息 (对应用户中心、登录功能)
     * @return 结果
     */
    public int insertHongdaUser(HongdaUser hongdaUser);

    /**
     * 修改小程序用户信息 (对应用户中心、登录功能)
     * 
     * @param hongdaUser 小程序用户信息 (对应用户中心、登录功能)
     * @return 结果
     */
    public int updateHongdaUser(HongdaUser hongdaUser);

    /**
     * 删除小程序用户信息 (对应用户中心、登录功能)
     * 
     * @param id 小程序用户信息 (对应用户中心、登录功能)主键
     * @return 结果
     */
    public int deleteHongdaUserById(Long id);

    /**
     * 批量删除小程序用户信息 (对应用户中心、登录功能)
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHongdaUserByIds(Long[] ids);

    /**
     * 根据微信openid查询用户
     * 
     * @param openid 微信用户唯一标识
     * @return 用户信息
     */
    public HongdaUser selectHongdaUserByOpenid(String openid);

    /**
     * 根据微信openid查询用户（包含软删除用户）
     * 
     * @param openid 微信用户唯一标识
     * @return 用户信息
     */
    public HongdaUser selectHongdaUserByOpenidIncludeDeleted(String openid);

    /**
     * 根据手机号哈希查询已删除的用户
     * 
     * @param phoneHash 手机号哈希值
     * @return 用户信息
     */
    public HongdaUser selectDeletedByPhoneHash(String phoneHash);

    /**
     * 复活用户账号（将软删除状态改为正常）
     * 
     * @param userId 用户ID
     * @return 更新行数
     */
    public int reactivateUserById(Long userId);
}
