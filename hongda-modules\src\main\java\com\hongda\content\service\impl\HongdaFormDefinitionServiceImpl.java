package com.hongda.content.service.impl;

import com.hongda.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.hongda.content.mapper.HongdaFormDefinitionMapper;
import com.hongda.content.domain.HongdaFormDefinition;
import com.hongda.content.service.IHongdaFormDefinitionService;

/**
 * 表单定义Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
@Service
public class HongdaFormDefinitionServiceImpl implements IHongdaFormDefinitionService 
{
    @Autowired
    private HongdaFormDefinitionMapper hongdaFormDefinitionMapper;

    /**
     * 根据活动ID查询表单定义
     * 
     * @param eventId 活动ID
     * @return 表单定义
     */
    @Override
    public HongdaFormDefinition selectFormDefinitionByEventId(Long eventId)
    {
        return hongdaFormDefinitionMapper.selectFormDefinitionByEventId(eventId);
    }

    /**
     * 保存或更新表单定义
     * 
     * @param formDefinition 表单定义
     * @return 结果
     */
    @Override
    public int saveOrUpdateFormDefinition(HongdaFormDefinition formDefinition)
    {
        // 先根据 eventId 查询，看记录是否已存在
        HongdaFormDefinition existing = hongdaFormDefinitionMapper.selectFormDefinitionByEventId(formDefinition.getEventId());
        
        if (existing != null) {
            // 如果存在，则执行 UPDATE 操作
            formDefinition.setId(existing.getId());
            formDefinition.setUpdateTime(DateUtils.getNowDate());
            return hongdaFormDefinitionMapper.updateFormDefinition(formDefinition);
        } else {
            // 如果不存在，则执行 INSERT 操作
            formDefinition.setCreateTime(DateUtils.getNowDate());
            return hongdaFormDefinitionMapper.insertFormDefinition(formDefinition);
        }
    }
} 