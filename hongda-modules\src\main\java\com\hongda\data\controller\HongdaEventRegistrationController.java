package com.hongda.data.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hongda.common.annotation.Log;
import com.hongda.common.core.controller.BaseController;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.common.enums.BusinessType;
import com.hongda.data.domain.HongdaEventRegistration;
import com.hongda.data.service.IHongdaEventRegistrationService;
import com.hongda.common.utils.poi.ExcelUtil;
import com.hongda.common.core.page.TableDataInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * 活动报名记录 (对应"报名数据"管理)Controller
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@Tag(name = "活动报名管理", description = "活动报名记录管理相关接口")
@RestController
@RequestMapping("/data/registration")
public class HongdaEventRegistrationController extends BaseController
{
    @Autowired
    private IHongdaEventRegistrationService hongdaEventRegistrationService;

    /**
     * 查询活动报名记录 (对应"报名数据"管理)列表
     */
    @Operation(summary = "查询活动报名记录列表", description = "分页查询活动报名记录列表")
    @PreAuthorize("@ss.hasPermi('data:registration:list')")
    @GetMapping("/list")
    public TableDataInfo list(HongdaEventRegistration hongdaEventRegistration)
    {
        startPage();
        List<HongdaEventRegistration> list = hongdaEventRegistrationService.selectHongdaEventRegistrationList(hongdaEventRegistration);
        return getDataTable(list);
    }

    /**
     * 导出活动报名记录 (对应"报名数据"管理)列表
     */
    @Operation(summary = "导出活动报名记录", description = "导出活动报名记录数据到Excel")
    @PreAuthorize("@ss.hasPermi('data:registration:export')")
    @Log(title = "活动报名记录 (对应报名数据管理)", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HongdaEventRegistration hongdaEventRegistration)
    {
        List<HongdaEventRegistration> list = hongdaEventRegistrationService.selectHongdaEventRegistrationList(hongdaEventRegistration);
        ExcelUtil<HongdaEventRegistration> util = new ExcelUtil<HongdaEventRegistration>(HongdaEventRegistration.class);
        util.exportExcel(response, list, "活动报名记录 (对应报名数据管理)数据");
    }

    /**
     * 获取活动报名记录 (对应"报名数据"管理)详细信息
     */
    @Operation(summary = "获取活动报名记录详情", description = "根据ID获取活动报名记录详细信息")
    @PreAuthorize("@ss.hasPermi('data:registration:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@Parameter(description = "报名记录ID") @PathVariable("id") Long id)
    {
        return success(hongdaEventRegistrationService.selectHongdaEventRegistrationById(id));
    }

    /**
     * 新增活动报名记录 (对应"报名数据"管理)
     */
    @Operation(summary = "新增活动报名记录", description = "创建新的活动报名记录")
    @PreAuthorize("@ss.hasPermi('data:registration:add')")
    @Log(title = "活动报名记录 (对应报名数据管理)", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HongdaEventRegistration hongdaEventRegistration)
    {
        // 如果提供了手机号，先根据手机号查找用户ID
        if (hongdaEventRegistration.getRegistrationPhone() != null && !hongdaEventRegistration.getRegistrationPhone().trim().isEmpty()) {
            // 验证手机号格式
            String phone = hongdaEventRegistration.getRegistrationPhone().trim();
            if (!phone.matches("^1[3-9]\\d{9}$")) {
                return error("手机号格式不正确");
            }
            
            // 根据手机号查找用户ID
            Long userId = hongdaEventRegistrationService.selectUserIdByPhone(phone);
            if (userId == null) {
                return error("未找到该手机号对应的用户，请确认手机号是否正确或用户是否已注册");
            }
            
            hongdaEventRegistration.setUserId(userId);
        }
        
        // 验证用户ID是否存在
        if (hongdaEventRegistration.getUserId() == null) {
            return error("用户ID不能为空");
        }
        
        // 设置报名时间为当前时间
        if (hongdaEventRegistration.getRegistrationTime() == null) {
            hongdaEventRegistration.setRegistrationTime(new java.util.Date());
        }
        
        return toAjax(hongdaEventRegistrationService.insertHongdaEventRegistration(hongdaEventRegistration));
    }

    /**
     * 修改活动报名记录 (对应"报名数据"管理)
     */
    @Operation(summary = "修改活动报名记录", description = "更新活动报名记录信息")
    @PreAuthorize("@ss.hasPermi('data:registration:edit')")
    @Log(title = "活动报名记录 (对应报名数据管理)", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HongdaEventRegistration hongdaEventRegistration)
    {
        return toAjax(hongdaEventRegistrationService.updateHongdaEventRegistration(hongdaEventRegistration));
    }

    /**
     * 删除活动报名记录 (对应报名数据管理)
     */
    @Operation(summary = "删除活动报名记录", description = "批量删除活动报名记录")
    @PreAuthorize("@ss.hasPermi('data:registration:remove')")
    @Log(title = "活动报名记录 (对应报名数据管理)", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@Parameter(description = "报名记录ID数组") @PathVariable Long[] ids)
    {
        return toAjax(hongdaEventRegistrationService.deleteHongdaEventRegistrationByIds(ids));
    }

    /**
     * 根据活动ID获取报名列表（用于活动管理页面的查看报名弹窗）
     */
    @Operation(summary = "根据活动ID获取报名列表", description = "获取指定活动的报名记录列表")
    @PreAuthorize("@ss.hasPermi('data:registration:list')")
    @GetMapping("/list/{eventId}")
    public AjaxResult getRegistrationList(@Parameter(description = "活动ID") @PathVariable("eventId") Long eventId)
    {
        List<HongdaEventRegistration> registrations = hongdaEventRegistrationService.selectRegistrationListByEventId(eventId);
        return success(registrations);
    }

    /**
     * 获取指定活动的所有报名记录（并关联用户信息）- 支持分页
     */
    @Operation(summary = "获取报名记录（含用户信息）", description = "分页查询报名记录并关联用户信息")
    @PreAuthorize("@ss.hasPermi('content:event:query')") // 权限可以复用活动管理的查询权限
    @GetMapping("/list-with-user")
    public TableDataInfo getRegistrationListWithUser(HongdaEventRegistration hongdaEventRegistration)
    {
        startPage(); // 启动分页
        List<HongdaEventRegistration> list = hongdaEventRegistrationService.selectRegistrationWithUserList(hongdaEventRegistration);
        return getDataTable(list); // 返回若依标准的分页数据格式
    }

    /**
     * 根据活动ID导出报名数据
     */
    @Operation(summary = "导出活动报名数据", description = "根据活动ID导出报名数据到Excel")
    @PreAuthorize("@ss.hasPermi('content:event:query')")
    @Log(title = "活动报名数据导出", businessType = BusinessType.EXPORT)
    @PostMapping("/export-by-event")
    public void exportByEvent(HttpServletResponse response, HongdaEventRegistration hongdaEventRegistration)
    {
        List<HongdaEventRegistration> list = hongdaEventRegistrationService.selectRegistrationWithUserList(hongdaEventRegistration);
        ExcelUtil<HongdaEventRegistration> util = new ExcelUtil<HongdaEventRegistration>(HongdaEventRegistration.class);
        util.exportExcel(response, list, "活动报名数据");
    }
}
