package com.hongda.wxapp.controller;

import com.hongda.common.core.controller.BaseController;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.platform.domain.HongdaStaticAsset;
import com.hongda.platform.service.IHongdaStaticAssetService;
import com.hongda.wxapp.domain.vo.WxAppAssetVO; // 1. 导入新的VO
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 小程序端 - 静态资源接口 (修正版)
 */
@Tag(name = "小程序静态资源接口", description = "为小程序提供全局静态资源的查询功能")
@RestController
@RequestMapping("/api/v1/assets")
public class WxAppAssetsController extends BaseController {

    @Autowired
    private IHongdaStaticAssetService staticAssetService;

    @Operation(summary = "获取所有小程序静态资源")
    @GetMapping("/all")
    public AjaxResult getAllAssets() {
        // 由于静态资源表没有status字段，因此创建一个空的查询对象即可
        HongdaStaticAsset queryParams = new HongdaStaticAsset();

        List<HongdaStaticAsset> assetList = staticAssetService.selectHongdaStaticAssetList(queryParams);

        // 【关键修改】将实体列表转换为VO列表
        List<WxAppAssetVO> voList = assetList.stream()
                .filter(asset -> asset.getAssetKey() != null && asset.getAssetUrl() != null)
                .map(asset -> {
                    WxAppAssetVO vo = new WxAppAssetVO();
                    vo.setAssetKey(asset.getAssetKey());
                    vo.setAssetUrl(asset.getAssetUrl()); // 此处传递的仍是 objectName
                    return vo;
                })
                .collect(Collectors.toList());

        // 返回VO列表。AOP切面会拦截这个结果，
        // 并自动处理voList中每个对象的 assetUrl 字段。
        return AjaxResult.success(voList);
    }
}
