import request from '@/utils/request'

// 查询国别政策管理列表
export function listCountryPolicy(query) {
  return request({
    url: '/content/countryPolicy/list',
    method: 'get',
    params: query
  })
}

// 查询国别政策管理详细
export function getCountryPolicy(articleId) {
  return request({
    url: '/content/countryPolicy/' + articleId,
    method: 'get'
  })
}

// 新增国别政策管理
export function addCountryPolicy(data) {
  return request({
    url: '/content/countryPolicy',
    method: 'post',
    data: data
  })
}

// 修改国别政策管理
export function updateCountryPolicy(data) {
  return request({
    url: '/content/countryPolicy',
    method: 'put',
    data: data
  })
}

// 删除国别政策管理
export function delCountryPolicy(articleId) {
  return request({
    url: '/content/countryPolicy/' + articleId,
    method: 'delete'
  })
}
