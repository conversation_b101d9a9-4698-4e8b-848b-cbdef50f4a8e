package com.hongda;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

/**
 * 启动程序
 * 
 * <AUTHOR>
 */
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class })
public class HongDaApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(HongDaApplication.class, args);
        System.out.println("""
                (o´▽`o)ﾉ  红大 · “走出去”服务平台 - 启动成功, 扬帆起航!
                
                                   |
                                 --|--
                                   |
                                __|___
                           ` |----| o|
                             |____|__|
                                |
                         _______|_________
                         \\ B H O N G D A /
                          \\_____________ /
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                """);
    }
}
