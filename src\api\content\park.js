import request from '@/utils/request'

// 查询园区信息列表
export function listPark(query) {
  return request({
    url: '/content/park/list',
    method: 'get',
    params: query
  })
}

// 查询园区信息详细
export function getPark(id) {
  return request({
    url: '/content/park/' + id,
    method: 'get'
  })
}

// 新增园区信息
export function addPark(data) {
  return request({
    url: '/content/park',
    method: 'post',
    data: data
  })
}

// 修改园区信息
export function updatePark(data) {
  return request({
    url: '/content/park',
    method: 'put',
    data: data
  })
}

// 删除园区信息
export function delPark(id) {
  return request({
    url: '/content/park/' + id,
    method: 'delete'
  })
}
