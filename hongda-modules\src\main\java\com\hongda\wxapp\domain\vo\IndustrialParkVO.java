package com.hongda.wxapp.domain.vo;

import com.hongda.common.annotation.OssUrl;
import lombok.Data;

/**
 * 工业园区卡片视图对象 (在国别详情中展示)
 */
@Data
public class IndustrialParkVO {

    /** 园区ID */
    private Long id;

    /** 园区名称 */
    private String name;

    /** 地理位置 */
    private String location;

    /** 主要产业 */
    private String industries;

    /** 核心优势/特点 */
    private String features;

    /** 【新增】园区列表封面图URL */
    @OssUrl
    private String coverImageUrl;
}