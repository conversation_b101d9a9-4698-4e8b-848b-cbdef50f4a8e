package com.hongda.common.config;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class OssAutoConfig {

    @Autowired
    private AliOssConfigProperties aliOssConfigProperties;

    @Bean
    public OSS ossClient() {
        log.info("-----------------开始创建OSSClient--------------------");
        OSS ossClient = new OSSClientBuilder().build(
                aliOssConfigProperties.getEndpoint(),
                aliOssConfigProperties.getAccessKeyId(),
                aliOssConfigProperties.getAccessKeySecret()
        );

        // 判断存储桶是否存在，如果不存在则创建
        if (!ossClient.doesBucketExist(aliOssConfigProperties.getBucketName())) {
            ossClient.createBucket(aliOssConfigProperties.getBucketName());
            log.info("存储桶 {} 创建成功。", aliOssConfigProperties.getBucketName());
        }
        log.info("-----------------OSSClient创建完成--------------------");
        return ossClient;
    }
}