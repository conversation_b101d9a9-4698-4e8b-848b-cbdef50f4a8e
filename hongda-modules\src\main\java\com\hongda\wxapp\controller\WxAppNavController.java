package com.hongda.wxapp.controller;

import com.hongda.common.core.controller.BaseController;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.platform.domain.HongdaNav; // 假设您的实体类名为 HongdaNav
import com.hongda.platform.service.IHongdaNavService; // 假设您的服务接口名为 IHongdaNavService
import com.hongda.wxapp.domain.vo.NavVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/v1/nav")
public class WxAppNavController extends BaseController {

    @Autowired
    private IHongdaNavService hongdaNavService; // 注入您系统已有的导航服务

    @GetMapping("/list")
    public AjaxResult getNavListByPosition(@RequestParam("positionCode") String positionCode) {
        HongdaNav queryParams = new HongdaNav();
        queryParams.setPositionCode(positionCode);
        queryParams.setStatus(1); // 只查询启用的导航

        // 调用现有服务，按sort_order排序
        List<HongdaNav> navList = hongdaNavService.selectHongdaNavList(queryParams);

        // 转换为VO列表
        List<NavVO> voList = navList.stream().map(nav -> {
            NavVO vo = new NavVO();
            BeanUtils.copyProperties(nav, vo);
            return vo;
        }).collect(Collectors.toList());

        return AjaxResult.success(voList);
    }
}