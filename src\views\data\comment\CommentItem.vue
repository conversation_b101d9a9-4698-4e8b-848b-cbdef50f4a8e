<template>
  <div class="comment-item" :class="getCommentClass()">
    <!-- 连接线 -->
    <div v-if="level > 0" class="comment-line" :style="getLineStyle()"></div>

    <!-- 评论内容卡片 -->
    <div class="comment-card" :style="getCardStyle()">
      <!-- 选择框 -->
      <div class="comment-select">
        <el-checkbox :model-value="selectedMap[comment.id]"
                     @change="(val) => emit('select', comment.id, val)"></el-checkbox>
      </div>

      <!-- 评论主体 -->
      <div class="comment-body">
        <!-- 用户信息和时间 -->
        <div class="comment-header">
          <div class="user-info">
            <el-avatar :size="32" :src="comment.avatarUrl || undefined">
              <el-icon>
                <User/>
              </el-icon>
            </el-avatar>
            <div class="user-details">
              <span class="username">{{ comment.nickname || '匿名用户' }}</span>
              <span class="comment-time">{{ parseTime(comment.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </div>
          </div>
          <div class="comment-status">
            <dict-tag :options="dictStatus" :value="comment.status"/>
          </div>
        </div>

        <!-- 关联内容信息 -->
        <div class="comment-related" v-if="level === 0">
          <dict-tag :options="dictRelatedType" :value="comment.relatedType"/>
          <span class="related-title" :title="comment.relatedTitle">{{ comment.relatedTitle }}</span>
        </div>

        <!-- 评论内容 -->
        <div class="comment-content">
          {{ comment.content }}
        </div>

        <!-- 操作按钮 -->
        <div class="comment-actions">
          <el-button link type="primary" size="small" @click="emit('reply', comment)">
            <el-icon>
              <ChatDotRound/>
            </el-icon>
            回复
          </el-button>
          <el-button link type="primary" size="small" @click="emit('edit', comment)" v-hasPermi="['data:comment:edit']">
            <el-icon>
              <Edit/>
            </el-icon>
            修改
          </el-button>
          <el-button link type="danger" size="small" @click="emit('delete', comment)"
                     v-hasPermi="['data:comment:remove']">
            <el-icon>
              <Delete/>
            </el-icon>
            删除
          </el-button>

          <!-- 展开/收起按钮 -->
          <el-button
              v-if="comment.children && comment.children.length > 0"
              link
              type="primary"
              size="small"
              @click="emit('toggle-expand', comment.id)"
          >
            <el-icon>
              <ArrowDown v-if="!expandedMap[comment.id]"/>
              <ArrowUp v-else/>
            </el-icon>
            {{ expandedMap[comment.id] ? '收起' : '展开' }} ({{ comment.children.length }})
          </el-button>
        </div>
      </div>
    </div>

    <!-- 子评论 -->
    <div v-if="comment.children && comment.children.length > 0 && expandedMap[comment.id]" class="comment-children">
      <CommentItem
          v-for="child in getDisplayChildren()"
          :key="child.id"
          :comment="child"
          :level="level + 1"
          :expanded-map="expandedMap"
          :selected-map="selectedMap"
          :dict-related-type="dictRelatedType"
          :dict-status="dictStatus"
          @toggle-expand="(id) => emit('toggle-expand', id)"
          @select="(id, val) => emit('select', id, val)"
          @edit="(c) => emit('edit', c)"
          @delete="(c) => emit('delete', c)"
          @reply="(c) => emit('reply', c)"
      />
      <div v-if="shouldShowMoreLink()" class="more-replies">
        <el-button link type="primary" size="small" @click="emit('load-more', comment.id)">
          还有 {{ comment.children.length - 3 }} 条回复...
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import {getCurrentInstance} from 'vue';
import {ArrowDown, ArrowUp, ChatDotRound, Delete, Edit, User} from '@element-plus/icons-vue';

const {proxy} = getCurrentInstance();
const {parseTime} = proxy;

const props = defineProps({
  comment: Object,
  level: {
    type: Number,
    default: 0
  },
  expandedMap: Object,
  selectedMap: Object,
  dictRelatedType: Array,
  dictStatus: Array,
});

const emit = defineEmits(['toggle-expand', 'select', 'edit', 'delete', 'reply', 'load-more']);

const getCommentClass = () => {
  const baseClass = {};
  if (props.level <= 9) {
    baseClass[`level-${props.level}`] = true;
  } else {
    baseClass['level-deep'] = true;
  }
  return baseClass;
};

const getCardStyle = () => ({
  marginLeft: `${Math.min(props.level * 40, 400)}px`, // 最大缩进400px(10层)
  backgroundColor: getBackgroundColor(),
  borderLeftWidth: getBorderLeftWidth(),
  borderLeftColor: getBorderLeftColor()
});

const getBackgroundColor = () => {
  const colors = ['#ffffff', '#f8f9fa'];
  return colors[props.level % 2];
};

const getBorderLeftWidth = () => {
  if (props.level === 0) return '4px';
  if (props.level === 1) return '3px';
  return '2px';
};

const getBorderLeftColor = () => {
  const colors = [
    '#409eff', // level 0
    '#67c23a', // level 1
    '#e6a23c', // level 2
    '#f56c6c', // level 3
    '#9c27b0', // level 4
    '#00bcd4', // level 5
    '#ff9800', // level 6
    '#607d8b', // level 7
    '#795548', // level 8
    '#8bc34a', // level 9
    '#ff5722'  // level 10+
  ];
  return colors[Math.min(props.level, 10)];
};

const getLineStyle = () => ({
  left: `${Math.min((props.level - 1) * 40 + 20, 380)}px`, // 对应10层缩进
  height: '100%'
});

const getDisplayChildren = () => {
  if (props.level >= 6 && props.comment.children && props.comment.children.length > 3) {
    return props.comment.children.slice(0, 3);
  }
  return props.comment.children || [];
};

const shouldShowMoreLink = () => {
  return props.level >= 6 && props.comment.children && props.comment.children.length > 3;
};
</script>

<style scoped lang="scss">
.comment-item {
  position: relative;
}

.comment-line {
  position: absolute;
  top: 16px;
  left: 20px;
  bottom: 16px;
  width: 2px;
  background: #dcdfe6;
  border-radius: 1px;
}

.comment-card {
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  margin-bottom: 8px;
  transition: all 0.3s ease;
  display: flex;
  padding: 16px 16px 16px 36px;
}

.comment-card:hover {
  border-color: #c0c4cc;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

// 为每个层级定义不同的左边框颜色
.level-0 .comment-card {
  border-left: 4px solid #409eff;
}

.level-1 .comment-card {
  border-left: 3px solid #67c23a;
}

.level-2 .comment-card {
  border-left: 2px solid #e6a23c;
}

.level-3 .comment-card {
  border-left: 2px solid #f56c6c;
}

.level-4 .comment-card {
  border-left: 2px solid #9c27b0;
}

.level-5 .comment-card {
  border-left: 2px solid #00bcd4;
}

.level-6 .comment-card {
  border-left: 2px solid #ff9800;
}

.level-7 .comment-card {
  border-left: 2px solid #607d8b;
}

.level-8 .comment-card {
  border-left: 2px solid #795548;
}

.level-9 .comment-card {
  border-left: 2px solid #8bc34a;
}

.level-deep .comment-card {
  border-left: 2px solid #ff5722;
}

.comment-select {
  margin-right: 12px;
  display: flex;
  align-items: flex-start;
  padding-top: 8px;
}

.comment-body {
  flex: 1;
  min-width: 0;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.username {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.comment-time {
  font-size: 12px;
  color: #909399;
}

.comment-related {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #f5f7fa;
  padding: 6px 10px;
  border-radius: 4px;
}

.related-title {
  font-size: 13px;
  color: #606266;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.comment-content {
  color: #303133;
  line-height: 1.7;
  margin-bottom: 12px;
  word-break: break-word;
  font-size: 14px;
  white-space: pre-wrap;
}

.comment-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  border-top: 1px solid #f0f2f5;
  padding-top: 12px;
}

.comment-children {
  margin-top: 8px;
  animation: slideDown 0.3s ease-out;
}

.more-replies {
  margin-left: 160px;
  color: #909399;
  font-size: 12px;
  text-align: center;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px dashed #e1e6f0;
}

@media (max-width: 768px) {
  .comment-card {
    margin-left: 0 !important;
    padding: 12px;
    border-left-width: 3px !important;
  }
  .comment-line {
    display: none;
  }
  .user-info {
    gap: 8px;
  }
  .comment-actions {
    gap: 8px;
    flex-wrap: wrap;
  }
  .comment-content {
    font-size: 13px;
  }
  .related-title {
    max-width: 180px;
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>