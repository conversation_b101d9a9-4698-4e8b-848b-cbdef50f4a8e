package com.hongda.content.service;

import java.util.List;
import java.util.Map;
import com.hongda.content.domain.HongdaEvent;

/**
 * 活动管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
public interface IHongdaEventService 
{
    /**
     * 查询活动管理
     * 
     * @param id 活动管理主键
     * @return 活动管理
     */
    public HongdaEvent selectHongdaEventById(Long id);

    /**
     * 查询活动管理列表
     * 
     * @param hongdaEvent 活动管理
     * @return 活动管理集合
     */
    public List<HongdaEvent> selectHongdaEventList(HongdaEvent hongdaEvent);

    /**
     * 新增活动管理
     * 
     * @param hongdaEvent 活动管理
     * @return 结果
     */
    public int insertHongdaEvent(HongdaEvent hongdaEvent);

    /**
     * 修改活动管理
     * 
     * @param hongdaEvent 活动管理
     * @return 结果
     */
    public int updateHongdaEvent(HongdaEvent hongdaEvent);

    /**
     * 批量删除活动管理
     * 
     * @param ids 需要删除的活动管理主键集合
     * @return 结果
     */
    public int deleteHongdaEventByIds(Long[] ids);

    /**
     * 删除活动管理信息
     * 
     * @param id 活动管理主键
     * @return 结果
     */
    public int deleteHongdaEventById(Long id);

    /**
     * 定时任务：自动更新活动状态
     * 根据当前时间自动更新活动状态：
     * 1. 将已过报名截止时间但未结束的活动状态更新为"报名中"
     * 2. 将已过结束时间的活动状态更新为"已结束"
     * 
     * @return 更新的活动数量
     */
    public int updateEventStatusAutomatically();

    /**
     * 切换活动推广状态
     * 
     * @param eventId 活动ID
     * @param promotionData 推广配置数据
     * @return 操作结果
     */
    int changeEventPromotionStatus(Long eventId, HongdaEvent promotionData);

    /**
     * 获取推广活动列表
     * 
     * @return 推广活动列表
     */
    List<HongdaEvent> selectPromotionEventList();

    /**
     * 同步推广信息到广告表
     * @deprecated 活动推广功能独立于广告管理，此方法已废弃
     * 
     * @param event 活动对象
     * @return 广告记录ID
     */
    @Deprecated
    Long syncPromotionToAd(HongdaEvent event);

    /**
     * 从广告表移除推广信息
     * @deprecated 活动推广功能独立于广告管理，此方法已废弃
     * 
     * @param eventId 活动ID
     * @return 操作结果
     */
    @Deprecated
    int removePromotionFromAd(Long eventId);

    /**
     * 根据活动状态查询活动列表
     * 
     * @param activityStatus 活动状态：0=未开始，1=进行中，2=已结束
     * @return 活动列表
     */
    List<HongdaEvent> selectEventListByActivityStatus(Integer activityStatus);

    /**
     * 根据报名状态查询活动列表
     * 
     * @param registrationStatus 报名状态：0=未开始，1=报名中，2=已结束
     * @return 活动列表
     */
    List<HongdaEvent> selectEventListByRegistrationStatus(Integer registrationStatus);

    /**
     * 获取活动状态统计
     * 
     * @return 状态统计Map
     */
    Map<String, Integer> getEventStatusStatistics();

    /**
     * 查询即将开始的活动（1小时内）
     * 
     * @return 即将开始的活动列表
     */
    List<HongdaEvent> selectUpcomingEvents();

    /**
     * 查询报名即将截止的活动（24小时内）
     * 
     * @return 报名即将截止的活动列表
     */
    List<HongdaEvent> selectRegistrationDeadlineEvents();


}
