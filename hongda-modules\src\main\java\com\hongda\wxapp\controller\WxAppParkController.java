package com.hongda.wxapp.controller;

import com.hongda.common.core.controller.BaseController;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.content.domain.HongdaCountry;
import com.hongda.content.domain.HongdaPark;
import com.hongda.content.service.IHongdaCountryService;
import com.hongda.content.service.IHongdaParkService;
import com.hongda.wxapp.domain.vo.ParkDetailVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1/park")
public class WxAppParkController extends BaseController {

    @Autowired
    private IHongdaParkService hongdaParkService;

    @Autowired
    private IHongdaCountryService hongdaCountryService; // 用于获取国家名称

    @GetMapping("/{id}")
    public AjaxResult getParkDetail(@PathVariable("id") Long id) {
        HongdaPark park = hongdaParkService.selectHongdaParkById(id);

        if (park == null || !"0".equals(park.getStatus())) {
            return AjaxResult.error("该园区信息不存在或已下线");
        }

        // 组装VO，并附带上级国家信息
        ParkDetailVO vo = new ParkDetailVO();
        BeanUtils.copyProperties(park, vo);

        if (park.getCountryId() != null) {
            HongdaCountry country = hongdaCountryService.selectHongdaCountryById(park.getCountryId());
            if (country != null) {
                vo.setCountryName(country.getNameCn());
            }
        }
        return AjaxResult.success(vo);
    }
}