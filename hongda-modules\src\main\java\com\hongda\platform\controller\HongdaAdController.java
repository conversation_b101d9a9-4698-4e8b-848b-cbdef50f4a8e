package com.hongda.platform.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hongda.common.annotation.Log;
import com.hongda.common.core.controller.BaseController;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.common.enums.BusinessType;
import com.hongda.platform.domain.HongdaAd;
import com.hongda.platform.service.IHongdaAdService;
import com.hongda.common.utils.poi.ExcelUtil;
import com.hongda.common.core.page.TableDataInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * 广告管理Controller
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Tag(name = "广告管理", description = "广告管理相关接口")
@RestController
@RequestMapping("/platform/ad")
public class HongdaAdController extends BaseController
{
    @Autowired
    private IHongdaAdService hongdaAdService;

    /**
     * 查询广告管理列表
     */
    @Operation(summary = "查询广告列表", description = "分页查询广告列表")
    @PreAuthorize("@ss.hasPermi('platform:ad:list')")
    @GetMapping("/list")
    public TableDataInfo list(HongdaAd hongdaAd)
    {
        startPage();
        List<HongdaAd> list = hongdaAdService.selectHongdaAdList(hongdaAd);
        return getDataTable(list);
    }

    /**
     * 导出广告管理列表
     */
    @Operation(summary = "导出广告列表", description = "导出广告数据到Excel")
    @PreAuthorize("@ss.hasPermi('platform:ad:export')")
    @Log(title = "广告管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HongdaAd hongdaAd)
    {
        List<HongdaAd> list = hongdaAdService.selectHongdaAdList(hongdaAd);
        ExcelUtil<HongdaAd> util = new ExcelUtil<HongdaAd>(HongdaAd.class);
        util.exportExcel(response, list, "广告管理数据");
    }

    /**
     * 获取广告管理详细信息
     */
    @Operation(summary = "获取广告详情", description = "根据ID获取广告详细信息")
    @PreAuthorize("@ss.hasPermi('platform:ad:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@Parameter(description = "广告ID") @PathVariable("id") Long id)
    {
        return success(hongdaAdService.selectHongdaAdById(id));
    }

    /**
     * 新增广告管理
     */
    @Operation(summary = "新增广告", description = "创建新的广告")
    @PreAuthorize("@ss.hasPermi('platform:ad:add')")
    @Log(title = "广告管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HongdaAd hongdaAd)
    {
        return toAjax(hongdaAdService.insertHongdaAd(hongdaAd));
    }

    /**
     * 修改广告管理
     */
    @Operation(summary = "修改广告", description = "更新广告信息")
    @PreAuthorize("@ss.hasPermi('platform:ad:edit')")
    @Log(title = "广告管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HongdaAd hongdaAd)
    {
        return toAjax(hongdaAdService.updateHongdaAd(hongdaAd));
    }

    /**
     * 删除广告管理
     */
    @Operation(summary = "删除广告", description = "批量删除广告")
    @PreAuthorize("@ss.hasPermi('platform:ad:remove')")
    @Log(title = "广告管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@Parameter(description = "广告ID数组") @PathVariable Long[] ids)
    {
        return toAjax(hongdaAdService.deleteHongdaAdByIds(ids));
    }
}
