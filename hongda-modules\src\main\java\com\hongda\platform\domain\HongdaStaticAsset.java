package com.hongda.platform.domain;

import com.hongda.common.annotation.OssUrl;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.hongda.common.annotation.Excel;
import com.hongda.common.core.domain.BaseEntity;

/**
 * 小程序静态资源配置对象 hongda_static_asset
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
public class HongdaStaticAsset extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 资源唯一键 (程序中使用) */
    @Excel(name = "资源唯一键 (程序中使用)")
    private String assetKey;

    /** 资源名称 */
    @Excel(name = "资源名称")
    private String assetName;

    /** 资源URL */
    @Excel(name = "资源URL")
    @OssUrl
    private String assetUrl;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setAssetKey(String assetKey) 
    {
        this.assetKey = assetKey;
    }

    public String getAssetKey() 
    {
        return assetKey;
    }

    public void setAssetName(String assetName) 
    {
        this.assetName = assetName;
    }

    public String getAssetName() 
    {
        return assetName;
    }

    public void setAssetUrl(String assetUrl) 
    {
        this.assetUrl = assetUrl;
    }

    public String getAssetUrl() 
    {
        return assetUrl;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("assetKey", getAssetKey())
            .append("assetName", getAssetName())
            .append("assetUrl", getAssetUrl())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
