package com.hongda.platform.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hongda.common.annotation.Log;
import com.hongda.common.core.controller.BaseController;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.common.enums.BusinessType;
import com.hongda.platform.domain.HongdaConsultant;
import com.hongda.platform.service.IHongdaConsultantService;
import com.hongda.common.utils.poi.ExcelUtil;
import com.hongda.common.core.page.TableDataInfo;

/**
 * 顾问管理Controller
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
@RestController
@RequestMapping("/platform/consultant")
public class HongdaConsultantController extends BaseController
{
    @Autowired
    private IHongdaConsultantService hongdaConsultantService;

    /**
     * 查询顾问管理列表
     */
    @PreAuthorize("@ss.hasPermi('platform:consultant:list')")
    @GetMapping("/list")
    public TableDataInfo list(HongdaConsultant hongdaConsultant)
    {
        startPage();
        List<HongdaConsultant> list = hongdaConsultantService.selectHongdaConsultantList(hongdaConsultant);
        return getDataTable(list);
    }

    /**
     * 导出顾问管理列表
     */
    @PreAuthorize("@ss.hasPermi('platform:consultant:export')")
    @Log(title = "顾问管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HongdaConsultant hongdaConsultant)
    {
        List<HongdaConsultant> list = hongdaConsultantService.selectHongdaConsultantList(hongdaConsultant);
        ExcelUtil<HongdaConsultant> util = new ExcelUtil<HongdaConsultant>(HongdaConsultant.class);
        util.exportExcel(response, list, "顾问管理数据");
    }

    /**
     * 获取顾问管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('platform:consultant:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(hongdaConsultantService.selectHongdaConsultantById(id));
    }

    /**
     * 新增顾问管理
     */
    @PreAuthorize("@ss.hasPermi('platform:consultant:add')")
    @Log(title = "顾问管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HongdaConsultant hongdaConsultant)
    {
        return toAjax(hongdaConsultantService.insertHongdaConsultant(hongdaConsultant));
    }

    /**
     * 修改顾问管理
     */
    @PreAuthorize("@ss.hasPermi('platform:consultant:edit')")
    @Log(title = "顾问管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HongdaConsultant hongdaConsultant)
    {
        return toAjax(hongdaConsultantService.updateHongdaConsultant(hongdaConsultant));
    }

    /**
     * 删除顾问管理
     */
    @PreAuthorize("@ss.hasPermi('platform:consultant:remove')")
    @Log(title = "顾问管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(hongdaConsultantService.deleteHongdaConsultantByIds(ids));
    }
}
