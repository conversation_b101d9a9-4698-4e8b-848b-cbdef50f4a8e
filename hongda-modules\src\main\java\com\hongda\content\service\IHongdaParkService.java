package com.hongda.content.service;

import java.util.List;
import com.hongda.content.domain.HongdaPark;

/**
 * 园区信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface IHongdaParkService 
{
    /**
     * 查询园区信息
     * 
     * @param id 园区信息主键
     * @return 园区信息
     */
    public HongdaPark selectHongdaParkById(Long id);

    /**
     * 查询园区信息列表
     * 
     * @param hongdaPark 园区信息
     * @return 园区信息集合
     */
    public List<HongdaPark> selectHongdaParkList(HongdaPark hongdaPark);

    /**
     * 新增园区信息
     * 
     * @param hongdaPark 园区信息
     * @return 结果
     */
    public int insertHongdaPark(HongdaPark hongdaPark);

    /**
     * 修改园区信息
     * 
     * @param hongdaPark 园区信息
     * @return 结果
     */
    public int updateHongdaPark(HongdaPark hongdaPark);

    /**
     * 批量删除园区信息
     * 
     * @param ids 需要删除的园区信息主键集合
     * @return 结果
     */
    public int deleteHongdaParkByIds(Long[] ids);

    /**
     * 删除园区信息信息
     * 
     * @param id 园区信息主键
     * @return 结果
     */
    public int deleteHongdaParkById(Long id);
}
