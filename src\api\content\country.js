import request from '@/utils/request'

// 查询国别信息列表
export function listCountry(query) {
  return request({
    url: '/content/country/list',
    method: 'get',
    params: query
  })
}

// 查询国别信息详细
export function getCountry(id) {
  return request({
    url: '/content/country/' + id,
    method: 'get'
  })
}

// 新增国别信息
export function addCountry(data) {
  return request({
    url: '/content/country',
    method: 'post',
    data: data
  })
}

// 修改国别信息
export function updateCountry(data) {
  return request({
    url: '/content/country',
    method: 'put',
    data: data
  })
}

// 删除国别信息
export function delCountry(id) {
  return request({
    url: '/content/country/' + id,
    method: 'delete'
  })
}

// 获取所有国别信息的精简列表 (用于下拉框)
export function listAllCountry(query) {
  return request({
    url: '/content/country/all', // 【【【 核心修复点 】】】
    method: 'get',
    params: query
  })
}