package com.hongda.wxapp.controller;

import com.hongda.common.core.controller.BaseController;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.common.core.page.TableDataInfo;
import com.hongda.content.domain.HongdaArticle;
import com.hongda.content.domain.HongdaTag;
import com.hongda.content.service.IHongdaArticleService;
import com.hongda.content.service.IHongdaTagService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 小程序端 - 内容接口控制器
 * <p>
 * 遵循简洁、直接的原则，直接返回数据库实体，由前端处理展示。
 * 专为小程序客户端提供数据服务。
 * </p>
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/v1/content") // 统一、带版本号的API路径
public class WxAppArticleController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(WxAppArticleController.class);

    @Autowired
    private IHongdaArticleService articleService;

    @Autowired
    private IHongdaTagService tagService;


    /**
     * 获取所有可用的文章分类标签
     * <p>
     * 用于在前端展示筛选标签列表。
     * </p>
     *
     * @return 标签列表
     */
    @GetMapping("/tags")
    public AjaxResult getTags() {
        // 传入一个空对象，表示查询所有标签
        List<HongdaTag> list = tagService.selectHongdaTagList(new HongdaTag());
        return AjaxResult.success(list);
    }

    /**
     * 分页获取资讯文章列表.
     *
     * @param queryParams RuoYi框架会自动绑定大部分查询参数 (如 title, pageNum, pageSize)
     * @param tagIdsStr   [KEY DEFENSIVE PATTERN] 我们专门使用一个独立的 @RequestParam 来接收 'tagIds'。
     * 这可以优雅地处理前端传来的单个ID字符串 (e.g., "20")，
     * 避免了Spring MVC尝试将 "20" 自动绑定到 List<Long> tagIds 时可能发生的类型转换错误。
     * @return 分页的文章数据
     */
    @GetMapping("/articles")
    public TableDataInfo getArticles(HongdaArticle queryParams,
                                     @RequestParam(value = "tagIds", required = false) String tagIdsStr) {

        // 检查并处理标签ID字符串
        if (tagIdsStr != null && !tagIdsStr.trim().isEmpty()) {
            log.info("Controller received tagIds parameter: '{}'", tagIdsStr);

            try {
                // 将字符串 (如 "20" 或 "20,21") 解析成一个 Long 类型的列表
                List<Long> tagIdList = Arrays.stream(tagIdsStr.split(","))
                        .map(String::trim)
                        .filter(s -> !s.isEmpty())
                        .map(Long::parseLong)
                        .collect(Collectors.toList());

                // 将解析好的列表设置到查询对象中，以供MyBatis使用
                queryParams.setTagIds(tagIdList);

            } catch (NumberFormatException e) {
                log.warn("Received malformed tagIds parameter: '{}'. It will be ignored.", tagIdsStr, e);
                // 异常发生时，不设置tagIds，查询将忽略标签筛选，行为安全。
            }
        }

        // 启动分页
        startPage();
        // 执行查询，此时 queryParams 可能已经包含了处理好的 tagIds
        List<HongdaArticle> list = articleService.selectHongdaArticleList(queryParams);
        // 返回封装好的分页结果
        return getDataTable(list);
    }


    /**
     * 获取单篇资讯文章的详情
     * <p>
     * 同时，调用此接口会为文章增加阅读量。
     * </p>
     *
     * @param id 文章ID
     * @return 文章的详细信息
     */
    @GetMapping("/article/{id}")
    public AjaxResult getArticleDetail(@PathVariable("id") Long id) {
        // 【关键修改2】: 直接使用通用的 articleService 来实现功能

        // 1. 调用文章服务增加阅读量
        articleService.incrementArticleViewCount(id);

        // 2. 直接调用文章服务获取完整的文章实体，不再需要DTO
        HongdaArticle article = articleService.selectHongdaArticleById(id);

        if (article == null) {
            return AjaxResult.error("文章不存在或已被删除");
        }
        // 3. 直接返回完整的 HongdaArticle 对象
        return AjaxResult.success(article);
    }
}
