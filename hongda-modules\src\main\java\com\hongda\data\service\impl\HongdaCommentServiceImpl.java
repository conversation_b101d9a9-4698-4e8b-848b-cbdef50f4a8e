package com.hongda.data.service.impl;

import com.hongda.common.utils.DateUtils;
import com.hongda.data.domain.HongdaComment;
import com.hongda.data.domain.vo.HongdaCommentVo;
import com.hongda.data.mapper.HongdaCommentMapper;
import com.hongda.data.service.IHongdaCommentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 评论管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class HongdaCommentServiceImpl implements IHongdaCommentService
{
    @Autowired
    private HongdaCommentMapper hongdaCommentMapper;

    @Override
    public HongdaCommentVo selectHongdaCommentById(Long id)
    {
        return hongdaCommentMapper.selectHongdaCommentById(id);
    }

    @Override
    public Map<String, Object> selectHongdaCommentList(HongdaComment hongdaComment)
    {
        boolean isSearchMode = (hongdaComment.getRelatedType() != null && !hongdaComment.getRelatedType().isEmpty()) ||
                hongdaComment.getUserId() != null ||
                (hongdaComment.getRelatedTitle() != null && !hongdaComment.getRelatedTitle().isEmpty()) ||
                (hongdaComment.getStatus() != null);

        List<HongdaCommentVo> baseList;
        if (isSearchMode) {
            baseList = hongdaCommentMapper.selectHongdaCommentList(hongdaComment);
        } else {
            baseList = hongdaCommentMapper.selectHongdaCommentList(new HongdaComment());
        }

        Set<HongdaCommentVo> finalCommentSet;
        Set<Long> idsToExpand = new HashSet<>();

        if (isSearchMode) {
            finalCommentSet = new HashSet<>(baseList);
            List<HongdaCommentVo> allComments = hongdaCommentMapper.selectHongdaCommentList(new HongdaComment());
            Map<Long, HongdaCommentVo> allCommentsMap = allComments.stream()
                    .collect(Collectors.toMap(HongdaCommentVo::getId, c -> c, (k1, k2) -> k1));

            for (HongdaCommentVo comment : baseList) {
                HongdaCommentVo current = comment;
                while (current.getParentId() != null && current.getParentId() != 0) {
                    HongdaCommentVo parent = allCommentsMap.get(current.getParentId());
                    if (parent != null) {
                        finalCommentSet.add(parent);
                        idsToExpand.add(parent.getId());
                        current = parent;
                    } else {
                        break;
                    }
                }
            }
        } else {
            finalCommentSet = new HashSet<>(baseList);
        }

        List<HongdaCommentVo> treeList = buildTree(new ArrayList<>(finalCommentSet));

        Map<String, Object> result = new HashMap<>();
        result.put("list", treeList);
        result.put("expandedIds", idsToExpand);

        return result;
    }

    private List<HongdaCommentVo> buildTree(List<HongdaCommentVo> list) {
        if (list == null || list.isEmpty()) {
            return new ArrayList<>();
        }

        List<HongdaCommentVo> treeList = new ArrayList<>();
        Map<Long, HongdaCommentVo> map = list.stream()
                .collect(Collectors.toMap(HongdaCommentVo::getId, node -> node, (k1, k2) -> k1));

        for (HongdaCommentVo node : list) {
            if (node.getParentId() == null || node.getParentId() == 0) {
                treeList.add(node);
            } else {
                HongdaCommentVo parent = map.get(node.getParentId());
                if (parent != null) {
                    if (parent.getChildren() == null) {
                        parent.setChildren(new ArrayList<>());
                    }
                    parent.getChildren().add(node);
                }
            }
        }

        treeList.sort((a, b) -> {
            if (a.getCreateTime() == null) return 1;
            if (b.getCreateTime() == null) return -1;
            return b.getCreateTime().compareTo(a.getCreateTime());
        });

        for (HongdaCommentVo parent : treeList) {
            sortChildren(parent);
        }

        return treeList;
    }

    private void sortChildren(HongdaCommentVo parent) {
        if (parent.getChildren() != null && !parent.getChildren().isEmpty()) {
            parent.getChildren().sort((a, b) -> {
                if (a.getCreateTime() == null) return 1;
                if (b.getCreateTime() == null) return -1;
                return a.getCreateTime().compareTo(b.getCreateTime());
            });
            for (HongdaCommentVo child : parent.getChildren()) {
                sortChildren(child);
            }
        }
    }

    @Override
    public int insertHongdaComment(HongdaComment hongdaComment) {
        // 如果是回复 (parentId > 0)
        if (hongdaComment.getParentId() != null && hongdaComment.getParentId() > 0) {
            // 【已修改】将变量类型从 HongdaComment 更改为 HongdaCommentVo
            HongdaCommentVo parentComment = hongdaCommentMapper.selectHongdaCommentById(hongdaComment.getParentId());

            if (parentComment != null) {
                hongdaComment.setReplyToUserId(parentComment.getUserId());
            }
        }
        hongdaComment.setCreateTime(DateUtils.getNowDate());
        return hongdaCommentMapper.insertHongdaComment(hongdaComment);
    }

    @Override
    public int updateHongdaComment(HongdaComment hongdaComment)
    {
        hongdaComment.setUpdateTime(DateUtils.getNowDate());
        return hongdaCommentMapper.updateHongdaComment(hongdaComment);
    }

    @Override
    public int deleteHongdaCommentByIds(Long[] ids)
    {
        return hongdaCommentMapper.deleteHongdaCommentByIds(ids);
    }

    @Override
    public int deleteHongdaCommentById(Long id)
    {
        return hongdaCommentMapper.deleteHongdaCommentById(id);
    }

    @Override
    public List<HongdaCommentVo> selectHongdaCommentListForExport(HongdaComment hongdaComment)
    {
        return hongdaCommentMapper.selectHongdaCommentList(hongdaComment);
    }

    @Override
    public List<HongdaCommentVo> selectWxAppCommentList(HongdaComment hongdaComment)
    {
        List<HongdaCommentVo> list = hongdaCommentMapper.selectHongdaCommentList(hongdaComment);
        return buildTree(list);
    }

    // 已删除 toggleLike 和所有与点赞状态相关的私有方法
}