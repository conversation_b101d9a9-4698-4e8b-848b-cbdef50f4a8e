import request from '@/utils/request'

// 查询地区管理列表
export function listRegion(query) {
  return request({
    url: '/content/region/list',
    method: 'get',
    params: query
  })
}

// 查询地区管理详细
export function getRegion(id) {
  return request({
    url: '/content/region/' + id,
    method: 'get'
  })
}

// 新增地区管理
export function addRegion(data) {
  return request({
    url: '/content/region',
    method: 'post',
    data: data
  })
}

// 修改地区管理
export function updateRegion(data) {
  return request({
    url: '/content/region',
    method: 'put',
    data: data
  })
}

// 删除地区管理
export function delRegion(id) {
  return request({
    url: '/content/region/' + id,
    method: 'delete'
  })
}
