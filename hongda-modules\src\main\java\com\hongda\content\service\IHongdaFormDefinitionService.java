package com.hongda.content.service;

import com.hongda.content.domain.HongdaFormDefinition;
import org.apache.ibatis.annotations.Mapper;

/**
 * 表单定义Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
@Mapper
public interface IHongdaFormDefinitionService 
{
    /**
     * 根据活动ID查询表单定义
     * 
     * @param eventId 活动ID
     * @return 表单定义
     */
    public HongdaFormDefinition selectFormDefinitionByEventId(Long eventId);

    /**
     * 保存或更新表单定义
     * 
     * @param formDefinition 表单定义
     * @return 结果
     */
    public int saveOrUpdateFormDefinition(HongdaFormDefinition formDefinition);
} 