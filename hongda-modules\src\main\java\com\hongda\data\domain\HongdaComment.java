package com.hongda.data.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.hongda.common.annotation.Excel;
import com.hongda.common.core.domain.BaseEntity;

/**
 * 评论管理对象 hongda_comment
 * * <AUTHOR>
 * @date 2025-07-28
 */
public class HongdaComment extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 评论ID, 主键 */
    private Long id;

    /** 发表用户ID */
    @Excel(name = "发表用户ID")
    private Long userId;

    /** 关联类型 */
    @Excel(name = "关联类型")
    private String relatedType;

    /** 关联的内容ID */
    @Excel(name = "关联的内容ID")
    private Long relatedId;

    /** 父评论ID */
    @Excel(name = "父评论ID")
    private Long parentId;

    /** 评论内容 */
    @Excel(name = "评论内容")
    private String content;

    /** 回复的目标用户ID */
    @Excel(name = "回复目标用户ID")
    private Long replyToUserId;

    /** 状态 */
    @Excel(name = "状态")
    private Integer status;

    private String relatedTitle;

    public Long getReplyToUserId() {
        return replyToUserId;
    }

    public void setReplyToUserId(Long replyToUserId) {
        this.replyToUserId = replyToUserId;
    }

    public String getRelatedTitle() {
        return relatedTitle;
    }

    public void setRelatedTitle(String relatedTitle) {
        this.relatedTitle = relatedTitle;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId()
    {
        return userId;
    }

    public void setRelatedType(String relatedType)
    {
        this.relatedType = relatedType;
    }

    public String getRelatedType()
    {
        return relatedType;
    }

    public void setRelatedId(Long relatedId)
    {
        this.relatedId = relatedId;
    }

    public Long getRelatedId()
    {
        return relatedId;
    }

    public void setParentId(Long parentId)
    {
        this.parentId = parentId;
    }

    public Long getParentId()
    {
        return parentId;
    }

    public void setContent(String content)
    {
        this.content = content;
    }

    public String getContent()
    {
        return content;
    }

    public void setStatus(Integer status)
    {
        this.status = status;
    }

    public Integer getStatus()
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("userId", getUserId())
                .append("relatedType", getRelatedType())
                .append("relatedId", getRelatedId())
                .append("parentId", getParentId())
                .append("content", getContent())
                .append("status", getStatus())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}