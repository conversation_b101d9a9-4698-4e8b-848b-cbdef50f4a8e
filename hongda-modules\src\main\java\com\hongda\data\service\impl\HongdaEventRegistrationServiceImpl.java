package com.hongda.data.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.hongda.data.mapper.HongdaEventRegistrationMapper;
import com.hongda.data.domain.HongdaEventRegistration;
import com.hongda.data.service.IHongdaEventRegistrationService;

/**
 * 活动报名记录 (对应“报名数据”管理)Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@Service
public class HongdaEventRegistrationServiceImpl implements IHongdaEventRegistrationService 
{
    @Autowired
    private HongdaEventRegistrationMapper hongdaEventRegistrationMapper;

    /**
     * 查询活动报名记录 (对应“报名数据”管理)
     * 
     * @param id 活动报名记录 (对应“报名数据”管理)主键
     * @return 活动报名记录 (对应“报名数据”管理)
     */
    @Override
    public HongdaEventRegistration selectHongdaEventRegistrationById(Long id)
    {
        return hongdaEventRegistrationMapper.selectHongdaEventRegistrationById(id);
    }

    /**
     * 查询活动报名记录 (对应“报名数据”管理)列表
     * 
     * @param hongdaEventRegistration 活动报名记录 (对应“报名数据”管理)
     * @return 活动报名记录 (对应“报名数据”管理)
     */
    @Override
    public List<HongdaEventRegistration> selectHongdaEventRegistrationList(HongdaEventRegistration hongdaEventRegistration)
    {
        return hongdaEventRegistrationMapper.selectHongdaEventRegistrationList(hongdaEventRegistration);
    }

    /**
     * 新增活动报名记录 (对应“报名数据”管理)
     * 
     * @param hongdaEventRegistration 活动报名记录 (对应“报名数据”管理)
     * @return 结果
     */
    @Override
    public int insertHongdaEventRegistration(HongdaEventRegistration hongdaEventRegistration)
    {
        return hongdaEventRegistrationMapper.insertHongdaEventRegistration(hongdaEventRegistration);
    }

    /**
     * 修改活动报名记录 (对应“报名数据”管理)
     * 
     * @param hongdaEventRegistration 活动报名记录 (对应“报名数据”管理)
     * @return 结果
     */
    @Override
    public int updateHongdaEventRegistration(HongdaEventRegistration hongdaEventRegistration)
    {
        return hongdaEventRegistrationMapper.updateHongdaEventRegistration(hongdaEventRegistration);
    }

    /**
     * 批量删除活动报名记录 (对应“报名数据”管理)
     * 
     * @param ids 需要删除的活动报名记录 (对应“报名数据”管理)主键
     * @return 结果
     */
    @Override
    public int deleteHongdaEventRegistrationByIds(Long[] ids)
    {
        return hongdaEventRegistrationMapper.deleteHongdaEventRegistrationByIds(ids);
    }

    /**
     * 删除活动报名记录 (对应"报名数据"管理)信息
     * 
     * @param id 活动报名记录 (对应"报名数据"管理)主键
     * @return 结果
     */
    @Override
    public int deleteHongdaEventRegistrationById(Long id)
    {
        return hongdaEventRegistrationMapper.deleteHongdaEventRegistrationById(id);
    }

    /**
     * 根据活动ID查询报名记录列表
     * 
     * @param eventId 活动ID
     * @return 报名记录集合
     */
    @Override
    public List<HongdaEventRegistration> selectRegistrationListByEventId(Long eventId)
    {
        return hongdaEventRegistrationMapper.selectRegistrationListByEventId(eventId);
    }

    /**
     * 根据活动ID查询报名列表（并关联用户信息）
     * 
     * @param eventId 活动ID
     * @return 报名列表
     */
    @Override
    public List<HongdaEventRegistration> selectRegistrationWithUserByEventId(Long eventId)
    {
        return hongdaEventRegistrationMapper.selectRegistrationWithUserByEventId(eventId);
    }

    /**
     * 查询报名记录列表（并关联用户信息）- 支持分页和条件查询
     * 
     * @param hongdaEventRegistration 查询条件
     * @return 报名记录集合
     */
    @Override
    public List<HongdaEventRegistration> selectRegistrationWithUserList(HongdaEventRegistration hongdaEventRegistration)
    {
        return hongdaEventRegistrationMapper.selectRegistrationWithUserList(hongdaEventRegistration);
    }

    /**
     * 根据手机号查找用户ID
     * 
     * @param phone 手机号
     * @return 用户ID
     */
    @Override
    public Long selectUserIdByPhone(String phone)
    {
        return hongdaEventRegistrationMapper.selectUserIdByPhone(phone);
    }
}
