package com.hongda.data.service.impl;

import java.util.List;
import com.hongda.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.hongda.data.mapper.HongdaUserMapper;
import com.hongda.data.domain.HongdaUser;
import com.hongda.data.service.IHongdaUserService;

/**
 * 小程序用户信息 (对应用户中心、登录功能)Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Service
public class HongdaUserServiceImpl implements IHongdaUserService 
{
    @Autowired
    private HongdaUserMapper hongdaUserMapper;

    /**
     * 查询小程序用户信息 (对应用户中心、登录功能)
     * 
     * @param id 小程序用户信息 (对应用户中心、登录功能)主键
     * @return 小程序用户信息 (对应用户中心、登录功能)
     */
    @Override
    public HongdaUser selectHongdaUserById(Long id)
    {
        return hongdaUserMapper.selectHongdaUserById(id);
    }

    /**
     * 查询小程序用户信息 (对应用户中心、登录功能)列表
     * 
     * @param hongdaUser 小程序用户信息 (对应用户中心、登录功能)
     * @return 小程序用户信息 (对应用户中心、登录功能)
     */
    @Override
    public List<HongdaUser> selectHongdaUserList(HongdaUser hongdaUser)
    {
        return hongdaUserMapper.selectHongdaUserList(hongdaUser);
    }

    /**
     * 新增小程序用户信息 (对应用户中心、登录功能)
     * 
     * @param hongdaUser 小程序用户信息 (对应用户中心、登录功能)
     * @return 结果
     */
    @Override
    public int insertHongdaUser(HongdaUser hongdaUser)
    {
        hongdaUser.setCreateTime(DateUtils.getNowDate());
        return hongdaUserMapper.insertHongdaUser(hongdaUser);
    }

    /**
     * 修改小程序用户信息 (对应用户中心、登录功能)
     * 
     * @param hongdaUser 小程序用户信息 (对应用户中心、登录功能)
     * @return 结果
     */
    @Override
    public int updateHongdaUser(HongdaUser hongdaUser)
    {
        hongdaUser.setUpdateTime(DateUtils.getNowDate());
        return hongdaUserMapper.updateHongdaUser(hongdaUser);
    }

    /**
     * 批量删除小程序用户信息 (对应用户中心、登录功能)
     * 
     * @param ids 需要删除的小程序用户信息 (对应用户中心、登录功能)主键
     * @return 结果
     */
    @Override
    public int deleteHongdaUserByIds(Long[] ids)
    {
        return hongdaUserMapper.deleteHongdaUserByIds(ids);
    }

    /**
     * 删除小程序用户信息 (对应用户中心、登录功能)信息
     * 
     * @param id 小程序用户信息 (对应用户中心、登录功能)主键
     * @return 结果
     */
    @Override
    public int deleteHongdaUserById(Long id)
    {
        return hongdaUserMapper.deleteHongdaUserById(id);
    }
}
