<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongda.content.mapper.HongdaFormDefinitionMapper">
    
    <resultMap type="HongdaFormDefinition" id="HongdaFormDefinitionResult">
        <result property="id"          column="id"          />
        <result property="eventId"     column="event_id"    />
        <result property="name"        column="name"        />
        <result property="fieldsJson"  column="fields_json" />
        <result property="createTime"  column="create_time" />
    </resultMap>

    <sql id="selectHongdaFormDefinitionVo">
        select id, event_id, name, fields_json, create_time from hongda_form_definition
    </sql>

    <select id="selectFormDefinitionByEventId" parameterType="Long" resultMap="HongdaFormDefinitionResult">
        <include refid="selectHongdaFormDefinitionVo"/>
        where event_id = #{eventId}
    </select>
        
    <insert id="insertFormDefinition" parameterType="HongdaFormDefinition" useGeneratedKeys="true" keyProperty="id">
        insert into hongda_form_definition
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="eventId != null">event_id,</if>
            <if test="name != null">name,</if>
            <if test="fieldsJson != null">fields_json,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="eventId != null">#{eventId},</if>
            <if test="name != null">#{name},</if>
            <if test="fieldsJson != null">#{fieldsJson},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateFormDefinition" parameterType="HongdaFormDefinition">
        update hongda_form_definition
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="fieldsJson != null">fields_json = #{fieldsJson},</if>
        </trim>
        where event_id = #{eventId}
    </update>

    <delete id="deleteFormDefinitionByEventId" parameterType="Long">
        delete from hongda_form_definition where event_id = #{eventId}
    </delete>

</mapper>