<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <EventSearchForm
      :queryParams="queryParams"
      :showSearch="showSearch"
      @search="handleQuery"
      @reset="resetQuery"
    />

    <!-- 操作按钮行 -->
    <EventActionButtons
      :single="single"
      :multiple="multiple"
      :showSearch="showSearch"
      @add="handleAdd"
      @update="handleUpdate"
      @delete="handleDelete"
      @export="handleExport"
      @refresh="getList"
      @update:showSearch="showSearch = $event"
    />

    <!-- 数据表格 -->
    <EventTable
      :loading="loading"
      :eventList="eventList"
      @selection-change="handleSelectionChange"
      @view-registrations="handleViewRegistrations"
      @update="handleUpdate"
      @delete="handleDelete"
      @hot-status-change="handleHotStatusChange"
      @promotion-status-change="handlePromotionStatusChange"
    />
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改活动管理对话框 -->
    <EventFormDialog
      :open="open"
      :title="title"
      :form="form"
      :rules="rules"
      :selectedLocation="selectedLocation"
      :locationOptions="locationOptions"
      :currentFormDefinition="currentFormDefinition"
      :designerOption="designerOption"
      @location-change="handleLocationChange"
      @confirm="handleConfirm"
      @cancel="cancel"
      @update:open="open = $event"
      @update:form="form = $event"
      ref="eventFormDialogRef"
    />

    <!-- 查看报名列表对话框 -->
    <RegistrationListDialog
      :open="registrationListOpen"
      :dialogTitle="registrationDialogTitle"
      :loading="registrationLoading"
      :registrationList="currentRegistrationList"
      :total="registrationTotal"
      :queryParams="registrationQueryParams"
      :currentFormDefinition="currentFormDefinition"
      :selectedIds="registrationIds"
      @search="getRegistrationList"
      @reset="resetRegistrationQuery"
      @add="() => handleRegistrationOperation('add')"
      @edit="(row) => handleRegistrationOperation('edit', row)"
      @delete="(row) => handleRegistrationOperation('delete', row)"
      @export="handleExportRegistration"
      @selection-change="handleRegistrationSelectionChange"
      @update:open="registrationListOpen = $event"
      @update:queryParams="registrationQueryParams = $event"
    />

    <!-- 推广配置对话框 -->
    <PromotionConfigDialog
      :open="promotionDialogOpen"
      :dialogTitle="promotionDialogTitle"
      :form="promotionForm"
      :rules="promotionRules"
      @submit="submitPromotionForm"
      @cancel="cancelPromotionForm"
      @update:open="promotionDialogOpen = $event"
      @update:form="Object.assign(promotionForm, $event)"
    />

    <!-- 新增/修改报名对话框 -->
    <RegistrationFormDialog
      :open="registrationFormOpen"
      :formTitle="registrationFormTitle"
      :formData="registrationFormData"
      :rules="registrationFormRules"
      :currentFormDefinition="currentFormDefinition"
      :dynamicFormValue="dynamicFormValue"
      :formCreateOption="formCreateOption"
      :formCreateKey="formCreateKey"
      @submit="submitRegistrationForm"
      @cancel="cancelRegistrationForm"
      @dynamic-form-change="handleDynamicFormChange"
      @update:open="registrationFormOpen = $event"
      @update:dynamicFormValue="dynamicFormValue = $event"
      @update:formData="registrationFormData = $event"
      ref="registrationFormDialogRef"
    />


  </div>
</template>

<script setup name="Event">
import { ref, reactive, toRefs, getCurrentInstance, nextTick } from 'vue';
const { proxy } = getCurrentInstance();

// 组件导入
import EventSearchForm from '@/components/Event/EventSearchForm.vue'
import EventActionButtons from '@/components/Event/EventActionButtons.vue'
import EventTable from '@/components/Event/EventTable.vue'
import EventFormDialog from '@/components/Event/EventFormDialog.vue'
import RegistrationListDialog from '@/components/Event/RegistrationListDialog.vue'
import PromotionConfigDialog from '@/components/Event/PromotionConfigDialog.vue'
import RegistrationFormDialog from '@/components/Event/RegistrationFormDialog.vue'

// API导入
import { listEvent, getEvent, delEvent, addEvent, updateEvent, changeEventHotStatus, changeEventPromotionStatus } from "@/api/content/event";
import { getFormDefinition, saveFormDefinition } from "@/api/content/form";
import { getRegistrationListWithUser, addRegistration, updateRegistration, delRegistration } from "@/api/data/registration";

// 导入全国省市区数据
import { regionData } from 'element-china-area-data';



// 基础状态
const eventList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const activeTab = ref("basicInfo");

// 组件引用
const eventFormDialogRef = ref();
const registrationFormDialogRef = ref();


// 表单相关
const designer = ref(null);
const currentFormDefinition = ref(null);

// 报名管理相关状态
const registrationState = reactive({
  listOpen: false,
  loading: false,
  dialogTitle: "",
  currentList: [],
  total: 0,
  formOpen: false,
  formTitle: "",
  formData: {},
  single: true,
  multiple: true,
  ids: []
});

// 动态表单相关
const dynamicFormValue = ref({});
const formCreateKey = ref(0);
const dynamicFormRef = ref(null);

// 省市区级联选择器相关
const selectedLocation = ref([]);

// 推广相关数据
const promotionDialogOpen = ref(false);
const promotionDialogTitle = ref("");
const currentPromotionEvent = ref(null);

const promotionForm = reactive({
  id: null,
  isPromoted: 0,
  promotionTitle: '',
  promotionImageUrl: '',
  promotionStartTime: null,
  promotionEndTime: null,
  promotionSortOrder: 0
});

const promotionRules = {
  promotionTitle: [
    { required: true, message: "推广标题不能为空", trigger: "blur" }
  ],
  promotionImageUrl: [
    { required: true, message: "推广图片不能为空", trigger: "blur" }
  ]
};

// 省市区级联数据（使用全国数据）
const locationOptions = ref(regionData);

// 查询参数
const registrationQueryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  eventId: null,
  nickname: null,
  phone: null,
  status: null
});

// 报名表单验证规则
const registrationFormRules = {
  userPhone: [
    { required: true, message: "手机号不能为空", trigger: "blur" },
    { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号格式", trigger: "blur" }
  ],
  status: [
    { required: true, message: "请选择报名状态", trigger: "change" }
  ]
};

// 配置项
const formCreateOption = ref({
  resetBtn: false,
  submitBtn: false,
  globalClass: 'form-create-custom',
  form: {
    labelWidth: '120px',
    labelPosition: 'right'
  }
});

const designerOption = ref({
  height: '500px'
});

// 主数据和表单
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    title: null,
    location: null,
    registrationStatus: null,
  },
  rules: {
    title: [{ required: true, message: "活动名称不能为空", trigger: "blur" }],
    startTime: [{ required: true, message: "活动开始时间不能为空", trigger: "change" }],
    province: [{ required: true, message: "请选择活动地点", trigger: "change" }],
    addressDetail: [{ required: true, message: "详细地址不能为空", trigger: "blur" }],
  }
});

const { queryParams, form, rules } = toRefs(data);

// 计算属性 - 简化解构赋值
const {
  listOpen: registrationListOpen,
  loading: registrationLoading,
  dialogTitle: registrationDialogTitle,
  currentList: currentRegistrationList,
  total: registrationTotal,
  formOpen: registrationFormOpen,
  formTitle: registrationFormTitle,
  formData: registrationFormData,
  single: registrationSingle,
  multiple: registrationMultiple,
  ids: registrationIds
} = toRefs(registrationState);



// 通用API调用函数
const apiCall = async (apiFunc, params = null) => {
  try {
    return await apiFunc(params);
  } catch (error) {
    proxy.$modal.msgError(error.message || "操作失败");
    throw error;
  }
};

// 重置表单数据
const resetFormData = (type = 'event') => {
  if (type === 'event') {
    form.value = {
      id: null,
      title: null,
      iconUrl: null,
      coverImageUrl: null,
      details: '',
      summary: null,
      sellPoint: null,
      startTime: null,
      endTime: null,
      location: null,
      province: null,
      city: null,
      district: null,
      addressDetail: null,
      registrationDeadline: null,
      registrationStartTime: null,
      registrationEndTime: null,
      maxParticipants: 0,
      status: "0",
      isHot: 0
    };
    // 重置级联选择器
    selectedLocation.value = [];
    proxy.resetForm("eventRef");
    activeTab.value = "basicInfo";
    currentFormDefinition.value = null;
    if(designer.value) {
      designer.value.setRule([]);
    }
  } else if (type === 'registration') {
    registrationFormData.value = {
      id: null,
      eventId: registrationQueryParams.eventId,
      userId: null,
      userPhone: '',
      status: 0
    };
    dynamicFormValue.value = {};
    formCreateKey.value += 1;
  }
};

// extractUserInfo方法已移除，现在直接使用后端API返回的用户信息

// 事件处理方法
function getList() {
  loading.value = true;

  // 创建一个不包含前端筛选参数的查询对象，用于获取所有数据进行前端分页
  const backendQuery = {
    pageNum: 1,
    pageSize: 9999, // 获取所有数据
    title: queryParams.value.title,
    location: queryParams.value.location
  };

  apiCall(listEvent, backendQuery).then(response => {
    let allRows = response.rows || [];

    // 前端筛选：按报名状态过滤
    if (queryParams.value.registrationStatus !== null && queryParams.value.registrationStatus !== '') {
      const targetRegistrationStatus = parseInt(queryParams.value.registrationStatus);
      allRows = allRows.filter(event => {
        return event.registrationStatus === targetRegistrationStatus;
      });
    }

    // 前端分页
    const pageSize = queryParams.value.pageSize;
    const pageNum = queryParams.value.pageNum;
    const startIndex = (pageNum - 1) * pageSize;
    const endIndex = startIndex + pageSize;

    eventList.value = allRows.slice(startIndex, endIndex);
    total.value = allRows.length; // 筛选后的总数
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  });
}

function cancel() {
  open.value = false;
  resetFormData('event');
}

// 处理级联选择器变化
function handleLocationChange(value) {
  if (value && value.length === 3) {
    form.value.province = value[0];
    form.value.city = value[1];
    form.value.district = value[2];
  } else {
    form.value.province = null;
    form.value.city = null;
    form.value.district = null;
  }
}

// 根据省市区字段设置级联选择器的值
function setLocationFromForm() {
  if (form.value.province && form.value.city && form.value.district) {
    selectedLocation.value = [form.value.province, form.value.city, form.value.district];
  } else {
    selectedLocation.value = [];
  }
}

function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

function handleAdd() {
  resetFormData('event');
  open.value = true;
  title.value = "添加活动";
}

async function handleUpdate(row) {
  resetFormData('event');
  const _id = row?.id || ids.value[0];

  const response = await apiCall(getEvent, _id);
  form.value = response.data;

  // 设置级联选择器的值
  setLocationFromForm();

  open.value = true;
  title.value = "修改活动";

  nextTick(async () => {
    try {
      const formDefRes = await getFormDefinition(_id);
      if (formDefRes.data && formDefRes.data.fieldsJson) {
        if (eventFormDialogRef.value?.designer) {
          eventFormDialogRef.value.designer.setRule(JSON.parse(formDefRes.data.fieldsJson));
        }
      } else {
        if (eventFormDialogRef.value?.designer) {
          eventFormDialogRef.value.designer.setRule([]);
        }
      }
    } catch (error) {
      if (eventFormDialogRef.value?.designer) {
        eventFormDialogRef.value.designer.setRule([]);
      }
    }
  });
}

function submitForm(eventFormRef) {
  const formRef = eventFormRef || proxy.$refs["eventRef"];
  formRef.validate(valid => {
    if (valid) {
      const isUpdate = form.value.id != null;
      const apiFunc = isUpdate ? updateEvent : addEvent;

      apiCall(apiFunc, form.value).then(response => {
        if (!isUpdate) {
          form.value.id = response.data.id;
          title.value = "修改活动";
          if (eventFormDialogRef.value) {
            eventFormDialogRef.value.activeTab = "formDesign";
          }
          proxy.$modal.msgSuccess("新增活动成功，请继续设计报名表单！");
        } else {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
        }
        getList();
      });
    }
  });
}

function handleDelete(row) {
  const _ids = row?.id || ids.value;
  proxy.$modal.confirm('是否确认删除活动管理编号为"' + _ids + '"的数据项？').then(() => {
    return apiCall(delEvent, _ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

function handleExport() {
  proxy.download('content/event/export', {
    ...queryParams.value
  }, `event_${new Date().getTime()}.xlsx`);
}



function handleConfirm(dialogData) {
  const { activeTab, eventFormRef, designer } = dialogData;

  if (activeTab === 'formDesign') {
    // 在报名表单设计标签页时，保存表单设计并关闭对话框
    if (!form.value.id) {
      proxy.$modal.msgError("活动ID不存在，无法保存！");
      return;
    }
    const formJson = designer.getRule();
    const dataToSave = {
       eventId: form.value.id,
       fieldsJson: JSON.stringify(formJson)
    };
    apiCall(saveFormDefinition, dataToSave).then(() => {
       proxy.$modal.msgSuccess("报名表单设计已保存！");
       open.value = false;
    });
  } else {
    // 在基本信息标签页时，调用原有的 submitForm 逻辑
    submitForm(eventFormRef);
  }
}

async function handleViewRegistrations(row) {
  registrationQueryParams.eventId = row.id;
  registrationQueryParams.pageNum = 1;
  registrationDialogTitle.value = `【${row.title}】的报名列表`;
  
  try {
    const formDefRes = await getFormDefinition(row.id);
    currentFormDefinition.value = formDefRes.data?.fieldsJson ? 
      JSON.parse(formDefRes.data.fieldsJson) : [];
  } catch (error) {
    currentFormDefinition.value = [];
  }
  
  registrationListOpen.value = true;
  getRegistrationList();
}

function getRegistrationList() {
  registrationLoading.value = true;
  apiCall(getRegistrationListWithUser, registrationQueryParams).then(response => {
    // 直接使用后端返回的用户信息，无需前端解析
    const processedRows = response.rows.map(row => ({
      ...row,
      userInfo: {
        nickname: row.userNickname || row.hongdaUser?.nickname || '未知用户',
        phone: row.userPhone || row.hongdaUser?.phone || '未绑定',
        avatarUrl: row.hongdaUser?.avatarUrl || null
      }
    }));
    
    currentRegistrationList.value = processedRows;
    registrationTotal.value = response.total || 0;
    registrationLoading.value = false;
  }).catch(() => {
    registrationLoading.value = false;
  });
}



// 统一的报名操作处理
function handleRegistrationOperation(operation, row = null) {
  const operationMap = {
    add: () => setupRegistrationForm(),
    edit: () => {
      const targetRow = row || currentRegistrationList.value.find(item => 
        item.id === registrationIds.value[0]
      );
      if (!targetRow) {
        proxy.$modal.msgError("请选择一行数据进行修改");
        return;
      }
      setupRegistrationForm(targetRow);
    },
    delete: () => {
      const _ids = row ? [row.id] : registrationIds.value;
      const _names = row ? (row.userInfo?.nickname || '未知用户') : `${registrationIds.value.length}条记录`;
      
      if (_ids.length === 0) {
        proxy.$modal.msgError("请选择要删除的数据");
        return;
      }
      
      proxy.$modal.confirm(`是否确认删除${_names}的报名数据？`).then(() => {
        const deletePromises = _ids.map(id => apiCall(delRegistration, id));
        return Promise.all(deletePromises);
      }).then(() => {
        getRegistrationList();
        proxy.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    }
  };
  
  operationMap[operation]?.();
}

const setupRegistrationForm = (row = null) => {
  if (row) {
    // 修改模式
    let parsedFormData = {};
    try {
      if (row.formData) {
        parsedFormData = typeof row.formData === 'string' ? JSON.parse(row.formData) : row.formData;
      }
    } catch (error) {
      parsedFormData = {};
    }
    
    registrationFormData.value = {
      id: row.id,
      eventId: row.eventId,
      userId: row.userId,
      userPhone: row.userPhone || row.hongdaUser?.phone || '',
      status: row.status
    };
    
    registrationFormTitle.value = "修改报名";
    
    // 设置表单数据
    nextTick(() => {
      setTimeout(() => {
        const emptyFormData = {};
        if (currentFormDefinition.value && Array.isArray(currentFormDefinition.value)) {
          currentFormDefinition.value.forEach(field => {
            if (field.field) {
              emptyFormData[field.field] = getDefaultValue(field.type);
            }
          });
        }
        
        const finalFormData = { ...emptyFormData, ...parsedFormData };
        dynamicFormValue.value = finalFormData;
        
        // 使用API设置值
        if (dynamicFormRef.value?.api?.setValue) {
          try {
            Object.entries(finalFormData).forEach(([key, value]) => {
              dynamicFormRef.value.api.setValue(key, value);
            });
          } catch (error) {
            console.warn('设置表单值失败:', error);
          }
        }
      }, 300);
    });
  } else {
    // 新增模式
    resetFormData('registration');
    registrationFormTitle.value = "新增";
    
    nextTick(() => {
      if (dynamicFormRef.value?.api?.clearValue) {
        try {
          dynamicFormRef.value.api.clearValue();
        } catch (error) {
          console.warn('清除表单值失败:', error);
        }
      }
      setTimeout(() => {
        dynamicFormValue.value = {};
      }, 100);
    });
  }
  
  registrationFormOpen.value = true;
  formCreateKey.value += 1;
};

// 获取字段默认值
const getDefaultValue = (fieldType) => {
  const defaultValues = {
    checkbox: [],
    switch: false,
    inputNumber: null
  };
  return defaultValues[fieldType] || '';
};



function cancelRegistrationForm() {
  registrationFormOpen.value = false;
  resetFormData('registration');
}

function submitRegistrationForm(dialogData) {
  const { formData, dynamicFormValue, dynamicFormRef } = dialogData;

  // 验证动态表单
  if (dynamicFormRef && currentFormDefinition.value && currentFormDefinition.value.length > 0) {
    try {
      if (typeof dynamicFormRef.validate === 'function') {
        const validateResult = dynamicFormRef.validate();
        if (validateResult && typeof validateResult.then === 'function') {
          validateResult.then(() => doSubmitRegistration(formData, dynamicFormValue)).catch(() => {
            proxy.$modal.msgError("请完善报名表单信息");
          });
        } else {
          if (validateResult) {
            doSubmitRegistration(formData, dynamicFormValue);
          } else {
            proxy.$modal.msgError("请完善报名表单信息");
          }
        }
      } else {
        doSubmitRegistration(formData, dynamicFormValue);
      }
    } catch (error) {
      doSubmitRegistration(formData, dynamicFormValue);
    }
  } else {
    doSubmitRegistration(formData, dynamicFormValue);
  }
}

function doSubmitRegistration(formData, currentDynamicFormData) {
  console.log('提交的动态表单数据:', currentDynamicFormData);

  // 直接使用普通对象，不需要复杂的清理函数
  const submitData = {
    ...formData,
    formData: JSON.stringify(currentDynamicFormData || {}),
    // 将前端的userPhone字段映射到后端的registrationPhone字段
    registrationPhone: formData.userPhone
  };

  const isUpdate = submitData.id;
  const apiFunc = isUpdate ? updateRegistration : addRegistration;

  apiCall(apiFunc, submitData).then(() => {
    proxy.$modal.msgSuccess(isUpdate ? "修改成功" : "新增成功");
    registrationFormOpen.value = false;
    getRegistrationList();
  });
}

// 处理动态表单变化
function handleDynamicFormChange(field, value) {
  console.log('动态表单数据变化:', field, value);
  // 确保 v-model 正确更新，只更新字段值
  if (field && value !== undefined) {
    dynamicFormValue.value = {
      ...dynamicFormValue.value,
      [field]: value
    };
  }
}

function handleExportRegistration() {
  if (!registrationQueryParams.eventId) {
    proxy.$modal.msgError("活动ID不存在，无法导出");
    return;
  }
  
  proxy.download('data/registration/export-by-event', {
    eventId: registrationQueryParams.eventId,
    status: registrationQueryParams.status,
    nickname: registrationQueryParams.nickname,
    phone: registrationQueryParams.phone
  }, `registration_${new Date().getTime()}.xlsx`);
}

function resetRegistrationQuery() {
  Object.assign(registrationQueryParams, {
    nickname: null,
    phone: null,
    status: null,
    pageNum: 1
  });
  getRegistrationList();
}

function handleRegistrationSelectionChange(selection) {
  registrationIds.value = selection.map(item => item.id);
  registrationSingle.value = selection.length !== 1;
  registrationMultiple.value = !selection.length;
}

async function handleHotStatusChange(row, newValue) {
  const statusText = newValue === 1 ? '热门' : '普通';
  
  try {
    await proxy.$modal.confirm(`确认将活动"${row.title}"设置为${statusText}活动？`, "状态变更", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    });
        
    row.hotStatusLoading = true;
    
    await apiCall(changeEventHotStatus, {
      id: row.id,
      isHot: newValue
    });
    
    proxy.$modal.msgSuccess(`活动状态已更新为${statusText}活动`);
    
  } catch (error) {
    if (error === 'cancel') {
      row.isHot = row.isHot === 1 ? 0 : 1;
    } else {
      row.isHot = row.isHot === 1 ? 0 : 1;
      proxy.$modal.msgError("状态更新失败，请稍后重试");
    }
  } finally {
    row.hotStatusLoading = false;
  }
}

/**
 * 处理推广状态变化
 */
function handlePromotionStatusChange(row, value) {
  if (value === 1) {
    // 开启推广，显示配置对话框
    currentPromotionEvent.value = row;
    promotionDialogTitle.value = "配置活动推广";
    
    // 使用活动信息作为默认值
    promotionForm.id = row.id;
    promotionForm.isPromoted = 1;
    promotionForm.promotionTitle = row.title;
    promotionForm.promotionImageUrl = row.coverImageUrl;
    promotionForm.promotionStartTime = row.startTime;
    promotionForm.promotionEndTime = row.endTime;
    promotionForm.promotionSortOrder = 0;
    
    promotionDialogOpen.value = true;
  } else {
    // 关闭推广，直接提交
    proxy.$modal.confirm('确认关闭该活动的推广功能？').then(() => {
      submitPromotionStatusChange(row.id, { id: row.id, isPromoted: 0 });
    }).catch(() => {
      // 取消操作，恢复开关状态
      row.isPromoted = 1;
    });
  }
}

/**
 * 提交推广配置表单
 */
function submitPromotionForm(formData) {
  submitPromotionStatusChange(formData.id, formData);
}

/**
 * 提交推广状态变更
 */
function submitPromotionStatusChange(eventId, formData) {
  const targetRow = eventList.value.find(item => item.id === eventId);
  if (targetRow) {
    targetRow.promotionStatusLoading = true;
  }
  
  changeEventPromotionStatus(formData).then(() => {
    proxy.$modal.msgSuccess(formData.isPromoted === 1 ? "推广开启成功" : "推广关闭成功");
    promotionDialogOpen.value = false;
    getList(); // 刷新列表
  }).catch(error => {
    proxy.$modal.msgError("操作失败：" + error.message);
    // 恢复开关状态
    if (targetRow) {
      targetRow.isPromoted = targetRow.isPromoted === 1 ? 0 : 1;
    }
  }).finally(() => {
    if (targetRow) {
      targetRow.promotionStatusLoading = false;
    }
  });
}

/**
 * 取消推广配置
 */
function cancelPromotionForm() {
  promotionDialogOpen.value = false;
  // 恢复开关状态
  if (currentPromotionEvent.value) {
    currentPromotionEvent.value.isPromoted = 0;
  }
}







// 初始化
getList();
</script>

<style scoped>
/* 主容器样式 */
.app-container {
  padding: 20px;
}
</style>
