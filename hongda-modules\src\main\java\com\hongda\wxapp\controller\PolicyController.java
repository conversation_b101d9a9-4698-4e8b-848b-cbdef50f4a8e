package com.hongda.wxapp.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.hongda.common.core.domain.AjaxResult;
import com.hongda.common.core.domain.dto.PolicyAcceptDTO;
import com.hongda.common.utils.ServletUtils;
import com.hongda.common.utils.ip.IpUtils;
import com.hongda.framework.web.service.TokenService;
import com.hongda.wxapp.service.IPolicyService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;

/**
 * 协议文档接口控制器
 *
 * <AUTHOR>
 */
@Tag(name = "协议文档接口", description = "用户协议和隐私政策的展示与同意记录")
@RestController
@RequestMapping("/api/v1/policy")
public class PolicyController
{
    @Autowired
    private IPolicyService policyService;

    @Autowired
    private TokenService tokenService;

    /**
     * 获取最新协议内容（公开接口）
     */
    @Operation(summary = "获取最新协议内容", description = "获取指定类型的最新版本协议内容")
    @GetMapping("/latest")
    public AjaxResult getLatestPolicy(
            @Parameter(description = "协议类型", example = "user_agreement", required = true)
            @RequestParam("type") String type)
    {
        return policyService.getLatestPolicy(type);
    }

    /**
     * 获取指定版本协议内容（公开接口，可选）
     */
    @Operation(summary = "获取指定版本协议内容", description = "获取指定类型和版本的协议内容")
    @GetMapping("/by-version")
    public AjaxResult getPolicyByVersion(
            @Parameter(description = "协议类型", example = "user_agreement", required = true)
            @RequestParam("type") String type,
            @Parameter(description = "协议版本", example = "1.0.0", required = true)
            @RequestParam("version") String version)
    {
        return policyService.getPolicyByVersion(type, version);
    }

    /**
     * 用户同意协议（需要登录）
     */
    @Operation(summary = "用户同意协议", description = "记录用户对指定协议版本的同意")
    @PostMapping("/accept")
    public AjaxResult acceptPolicy(@RequestBody PolicyAcceptDTO acceptDTO)
    {
        // 从Token中获取当前登录用户的ID
        HttpServletRequest request = ServletUtils.getRequest();
        Long userId = tokenService.getWxUserId(request);
        if (userId == null)
        {
            return AjaxResult.error("用户未登录或登录已过期");
        }

        // 获取用户IP和User-Agent
        String ip = IpUtils.getIpAddr(request);
        String userAgent = request.getHeader("User-Agent");

        return policyService.acceptPolicy(userId, acceptDTO, ip, userAgent);
    }

    /**
     * 获取用户已同意的协议版本（需要登录）
     */
    @Operation(summary = "获取用户已同意的协议版本", description = "查询用户对指定类型协议的最新同意记录")
    @GetMapping("/user-accepted")
    public AjaxResult getUserAcceptedPolicy(
            @Parameter(description = "协议类型", example = "user_agreement", required = true)
            @RequestParam("type") String type)
    {
        // 从Token中获取当前登录用户的ID
        HttpServletRequest request = ServletUtils.getRequest();
        Long userId = tokenService.getWxUserId(request);
        if (userId == null)
        {
            return AjaxResult.error("用户未登录或登录已过期");
        }

        return policyService.getUserAcceptedPolicy(userId, type);
    }
}
