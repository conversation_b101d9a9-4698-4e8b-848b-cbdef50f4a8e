package com.hongda.data.controller;

import java.util.List;
import java.util.Map;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hongda.common.annotation.Log;
import com.hongda.common.core.controller.BaseController;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.common.enums.BusinessType;
import com.hongda.data.domain.HongdaComment;
import com.hongda.data.domain.vo.HongdaCommentVo;
import com.hongda.data.service.IHongdaCommentService;
import com.hongda.common.utils.poi.ExcelUtil;

/**
 * 评论管理Controller
 */
@RestController
@RequestMapping("/data/comment")
public class HongdaCommentController extends BaseController
{
    @Autowired
    private IHongdaCommentService hongdaCommentService;

    /**
     * [修改] 查询评论管理列表 (树形)
     */
    @PreAuthorize("@ss.hasPermi('data:comment:list')")
    @GetMapping("/list")
    public AjaxResult list(HongdaComment hongdaComment)
    {
        // 直接将 Service 返回的 Map 作为成功数据
        Map<String, Object> result = hongdaCommentService.selectHongdaCommentList(hongdaComment);
        return success(result);
    }

    /**
     * 导出评论管理列表
     */
    @PreAuthorize("@ss.hasPermi('data:comment:export')")
    @Log(title = "评论管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HongdaComment hongdaComment)
    {
        // 【修改】调用新创建的、专用于导出的服务方法
        List<HongdaCommentVo> list = hongdaCommentService.selectHongdaCommentListForExport(hongdaComment);

        // 后续的 ExcelUtil 逻辑保持不变
        ExcelUtil<HongdaCommentVo> util = new ExcelUtil<HongdaCommentVo>(HongdaCommentVo.class);
        util.exportExcel(response, list, "评论管理数据");
    }

    /**
     * 获取评论管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('data:comment:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(hongdaCommentService.selectHongdaCommentById(id));
    }

    /**
     * 新增评论管理
     */
    @PreAuthorize("@ss.hasPermi('data:comment:add')")
    @Log(title = "评论管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HongdaComment hongdaComment)
    {
        return toAjax(hongdaCommentService.insertHongdaComment(hongdaComment));
    }

    /**
     * 修改评论管理
     */
    @PreAuthorize("@ss.hasPermi('data:comment:edit')")
    @Log(title = "评论管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HongdaComment hongdaComment)
    {
        return toAjax(hongdaCommentService.updateHongdaComment(hongdaComment));
    }

    /**
     * 删除评论管理
     */
    @PreAuthorize("@ss.hasPermi('data:comment:remove')")
    @Log(title = "评论管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(hongdaCommentService.deleteHongdaCommentByIds(ids));
    }
}
