<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongda.platform.mapper.HongdaConsultantMapper">
    
    <resultMap type="HongdaConsultant" id="HongdaConsultantResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="avatarUrl"    column="avatar_url"    />
        <result property="introduction"    column="introduction"    />
        <result property="qrCodeUrl"    column="qr_code_url"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectHongdaConsultantVo">
        select id, name, avatar_url, introduction, qr_code_url, sort_order, status, create_by, create_time, update_by, update_time, remark from hongda_consultant
    </sql>

    <select id="selectHongdaConsultantList" parameterType="HongdaConsultant" resultMap="HongdaConsultantResult">
        <include refid="selectHongdaConsultantVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
        </where>
    </select>
    
    <select id="selectHongdaConsultantById" parameterType="Long" resultMap="HongdaConsultantResult">
        <include refid="selectHongdaConsultantVo"/>
        where id = #{id}
    </select>

    <insert id="insertHongdaConsultant" parameterType="HongdaConsultant" useGeneratedKeys="true" keyProperty="id">
        insert into hongda_consultant
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="avatarUrl != null">avatar_url,</if>
            <if test="introduction != null">introduction,</if>
            <if test="qrCodeUrl != null">qr_code_url,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="avatarUrl != null">#{avatarUrl},</if>
            <if test="introduction != null">#{introduction},</if>
            <if test="qrCodeUrl != null">#{qrCodeUrl},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateHongdaConsultant" parameterType="HongdaConsultant">
        update hongda_consultant
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="avatarUrl != null">avatar_url = #{avatarUrl},</if>
            <if test="introduction != null">introduction = #{introduction},</if>
            <if test="qrCodeUrl != null">qr_code_url = #{qrCodeUrl},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHongdaConsultantById" parameterType="Long">
        delete from hongda_consultant where id = #{id}
    </delete>

    <delete id="deleteHongdaConsultantByIds" parameterType="String">
        delete from hongda_consultant where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectDisplayConsultant" resultMap="HongdaConsultantResult">
        SELECT * FROM hongda_consultant
        WHERE status = '1'
        ORDER BY sort_order ASC
        LIMIT 1
    </select>
</mapper>