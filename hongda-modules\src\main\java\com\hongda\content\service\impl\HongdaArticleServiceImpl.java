package com.hongda.content.service.impl;

import java.util.ArrayList;
import java.util.List;
import com.hongda.common.utils.DateUtils;
import com.hongda.content.domain.HongdaArticleTagRelation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.hongda.content.mapper.HongdaArticleMapper;
import com.hongda.content.domain.HongdaArticle;
import com.hongda.content.service.IHongdaArticleService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 资讯文章Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
@Service
public class HongdaArticleServiceImpl implements IHongdaArticleService 
{
    @Autowired
    private HongdaArticleMapper hongdaArticleMapper;

    /**
     * 查询资讯文章
     * 
     * @param id 资讯文章主键
     * @return 资讯文章
     */
    @Override
    public HongdaArticle selectHongdaArticleById(Long id)
    {
        HongdaArticle article = hongdaArticleMapper.selectHongdaArticleById(id);
        // 【新增】查询并设置关联的标签ID，用于前端编辑时回显
        if (article != null) {
            article.setTagIds(hongdaArticleMapper.selectTagIdsByArticleId(id));
        }
        return article;
    }

    /**
     * 查询资讯文章列表
     * 
     * @param hongdaArticle 资讯文章
     * @return 资讯文章
     */
    @Override
    public List<HongdaArticle> selectHongdaArticleList(HongdaArticle hongdaArticle)
    {
        return hongdaArticleMapper.selectHongdaArticleList(hongdaArticle);
    }

    /**
     * 新增资讯文章
     * 
     * @param hongdaArticle 资讯文章
     * @return 结果
     */
    @Override
    @Transactional
    public int insertHongdaArticle(HongdaArticle hongdaArticle)
    {
        hongdaArticle.setCreateTime(DateUtils.getNowDate());
        int rows = hongdaArticleMapper.insertHongdaArticle(hongdaArticle);
        insertArticleTag(hongdaArticle); // 调用我们新增的辅助方法
        return rows;
    }

    /**
     * 修改资讯文章
     *
     * @param hongdaArticle 资讯文章
     * @return 结果
     */
    @Override
    @Transactional
    public int updateHongdaArticle(HongdaArticle hongdaArticle)
    {
        hongdaArticle.setUpdateTime(DateUtils.getNowDate());
        hongdaArticleMapper.deleteArticleTagByArticleId(hongdaArticle.getId());
        insertArticleTag(hongdaArticle); // 调用我们新增的辅助方法
        return hongdaArticleMapper.updateHongdaArticle(hongdaArticle);
    }

    /**
     * 【【【 新增的私有辅助方法 】】】
     * 这就是您之前找不到的 insertArticleTag 方法。
     * 它的作用是处理文章与标签的关联信息。
     * @param hongdaArticle 包含文章ID和标签ID列表的文章对象
     */
    private void insertArticleTag(HongdaArticle hongdaArticle)
    {
        List<Long> tagIds = hongdaArticle.getTagIds();
        if (tagIds != null && !tagIds.isEmpty())
        {
            // 创建关联关系列表
            List<HongdaArticleTagRelation> list = new ArrayList<>();
            for (Long tagId : tagIds)
            {
                HongdaArticleTagRelation relation = new HongdaArticleTagRelation();
                relation.setArticleId(hongdaArticle.getId());
                relation.setTagId(tagId);
                list.add(relation);
            }
            // 批量插入到中间关系表
            if (!list.isEmpty())
            {
                hongdaArticleMapper.batchArticleTag(list);
            }
        }
    }

    /**
     * 批量删除资讯文章
     * 
     * @param ids 需要删除的资讯文章主键
     * @return 结果
     */
    @Override
    public int deleteHongdaArticleByIds(Long[] ids)
    {
        return hongdaArticleMapper.deleteHongdaArticleByIds(ids);
    }

    /**
     * 删除资讯文章信息
     * 
     * @param id 资讯文章主键
     * @return 结果
     */
    @Override
    public int deleteHongdaArticleById(Long id)
    {
        return hongdaArticleMapper.deleteHongdaArticleById(id);
    }

    /**
     * 实现增加文章阅读量的方法
     * <p>
     * 直接调用Mapper层执行数据库更新操作。
     * 在高并发场景下，可以考虑使用Redis缓存阅读数，然后通过定时任务批量更新回数据库，以减轻数据库写入压力。
     * 对于当前场景，直接更新已足够。
     * </p>
     *
     * @param articleId 文章ID
     */
    @Override
    public void incrementArticleViewCount(Long articleId) {
        if (articleId != null && articleId > 0) {
            hongdaArticleMapper.incrementViewCount(articleId);
        }
    }
}
