package com.hongda.content.domain;

import com.hongda.common.annotation.OssUrl;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.hongda.common.annotation.Excel;
import com.hongda.common.annotation.OssUrl;
import com.hongda.common.core.domain.BaseEntity;

/**
 * 园区信息对象 hongda_park
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
public class HongdaPark extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 园区ID */
    private Long id;

    /** 所属国家ID */
    @Excel(name = "所属国家ID")
    private Long countryId;

    /** 园区名称 */
    @Excel(name = "园区名称")
    private String name;

    /** 地理位置 */
    @Excel(name = "地理位置")
    private String location;

    /** 主要产业 */
    @Excel(name = "主要产业")
    private String mainIndustries;

    /** 园区简介/核心优势 */
    @Excel(name = "园区简介/核心优势")
    private String summary;

    /** 园区列表封面图URL */
    @Excel(name = "园区列表封面图URL")
    @OssUrl
    private String coverImageUrl;

    /** 园区详情介绍 */
    @Excel(name = "园区详情介绍")
    private String content;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    /** 显示顺序 */
    @Excel(name = "显示顺序")
    private Long sortOrder;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setCountryId(Long countryId) 
    {
        this.countryId = countryId;
    }

    public Long getCountryId() 
    {
        return countryId;
    }

    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }

    public void setLocation(String location) 
    {
        this.location = location;
    }

    public String getLocation() 
    {
        return location;
    }

    public void setMainIndustries(String mainIndustries) 
    {
        this.mainIndustries = mainIndustries;
    }

    public String getMainIndustries() 
    {
        return mainIndustries;
    }

    public void setSummary(String summary) 
    {
        this.summary = summary;
    }

    public String getSummary() 
    {
        return summary;
    }

    public void setCoverImageUrl(String coverImageUrl) 
    {
        this.coverImageUrl = coverImageUrl;
    }

    public String getCoverImageUrl() 
    {
        return coverImageUrl;
    }

    public void setContent(String content) 
    {
        this.content = content;
    }

    public String getContent() 
    {
        return content;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setSortOrder(Long sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Long getSortOrder() 
    {
        return sortOrder;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("countryId", getCountryId())
            .append("name", getName())
            .append("location", getLocation())
            .append("mainIndustries", getMainIndustries())
            .append("summary", getSummary())
            .append("coverImageUrl", getCoverImageUrl())
            .append("content", getContent())
            .append("status", getStatus())
            .append("sortOrder", getSortOrder())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
