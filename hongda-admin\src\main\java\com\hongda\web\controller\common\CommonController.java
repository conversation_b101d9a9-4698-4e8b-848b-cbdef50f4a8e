package com.hongda.web.controller.common;

import com.hongda.common.config.RuoYiConfig;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.common.core.service.OssFileStorageService;
import com.hongda.common.utils.ServletUtils;
import com.hongda.common.utils.StringUtils;
import com.hongda.common.utils.file.FileUtils;
import com.hongda.common.utils.uuid.IdUtils;
import com.hongda.framework.config.ServerConfig;
import com.hongda.framework.web.service.TokenService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通用请求处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/common")
public class CommonController
{
    private static final Logger log = LoggerFactory.getLogger(CommonController.class);

    @Autowired
    private ServerConfig serverConfig;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private OssFileStorageService ossFileStorageService;

    /**
     * 通用下载请求 (此部分与OSS无关，保持不变)
     *
     * @param fileName 文件名称
     * @param delete 是否删除
     */
    @GetMapping("/download")
    public void fileDownload(String fileName, Boolean delete, HttpServletResponse response, HttpServletRequest request)
    {
        try
        {
            if (!FileUtils.checkAllowDownload(fileName))
            {
                throw new Exception(StringUtils.format("文件名称({})非法，不允许下载。 ", fileName));
            }
            String realFileName = System.currentTimeMillis() + fileName.substring(fileName.indexOf("_") + 1);
            String filePath = RuoYiConfig.getDownloadPath() + fileName;

            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, realFileName);
            FileUtils.writeBytes(filePath, response.getOutputStream());
            if (delete)
            {
                FileUtils.deleteFile(filePath);
            }
        }
        catch (Exception e)
        {
            log.error("下载文件失败", e);
        }
    }

    /**
     * 【已修改】通用上传请求（单个）- 返回 objectName 和用于即时预览的签名URL
     */
    @PostMapping("/upload")
    public AjaxResult uploadFile(MultipartFile file) throws Exception {
        // 验证用户是否已登录
        HttpServletRequest request = ServletUtils.getRequest();
        Long wxUserId = tokenService.getWxUserId(request);

        if (wxUserId == null) {
            if (tokenService.getLoginUser(request) == null) {
                return AjaxResult.error(401, "请求访问：/common/upload，认证失败，无法访问系统资源");
            }
        }

        try {
            // 1. 生成唯一文件名
            String originalFilename = file.getOriginalFilename();
            String fileExtension = "";
            if (originalFilename != null && originalFilename.contains(".")) {
                fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
            }
            String objectName = IdUtils.fastUUID() + fileExtension;

            // 2. 使用 try-with-resources 自动关闭输入流并上传
            try (InputStream inputStream = file.getInputStream()) {
                ossFileStorageService.upload(objectName, inputStream);
            }

            // 3. 为刚刚上传的文件生成一个临时的签名URL，用于即时预览 (有效期15分钟)
            String signedUrl = ossFileStorageService.getSignedUrl(objectName, 15);

            // 4. 准备返回给前端的数据
            AjaxResult ajax = AjaxResult.success();
            ajax.put("objectName", objectName);
            ajax.put("url", signedUrl); // 新增返回的签名URL
            ajax.put("originalFilename", originalFilename);

            return ajax;
        } catch (Exception e) {
            log.error("上传文件到OSS失败", e);
            return AjaxResult.error("上传失败：" + e.getMessage());
        }
    }

    /**
     * 【已修改】通用上传请求（多个）- 返回包含每个文件信息的对象列表
     */
    @PostMapping("/uploads")
    public AjaxResult uploadFiles(List<MultipartFile> files) throws Exception
    {
        // 验证用户是否已登录
        HttpServletRequest request = ServletUtils.getRequest();
        Long wxUserId = tokenService.getWxUserId(request);

        if (wxUserId == null) {
            if (tokenService.getLoginUser(request) == null) {
                return AjaxResult.error(401, "请求访问：/common/uploads，认证失败，无法访问系统资源");
            }
        }

        try
        {
            // 创建一个列表来存储每个上传成功文件的结果
            List<Map<String, String>> successList = new ArrayList<>();

            for (MultipartFile file : files)
            {
                // 1. 生成唯一文件名
                String originalFilename = file.getOriginalFilename();
                String fileExtension = "";
                if (originalFilename != null && originalFilename.contains(".")) {
                    fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
                }
                String objectName = IdUtils.fastUUID() + fileExtension;

                // 2. 上传到OSS
                ossFileStorageService.upload(objectName, file.getInputStream());

                // 3. 生成签名URL
                String signedUrl = ossFileStorageService.getSignedUrl(objectName, 15);

                // 4. 将每个文件的结果封装成Map，并添加到列表中
                Map<String, String> fileResult = new HashMap<>();
                fileResult.put("objectName", objectName);
                fileResult.put("url", signedUrl);

                fileResult.put("originalFilename", originalFilename);
                successList.add(fileResult);
            }

            // 返回成功信息和文件结果列表
            AjaxResult ajax = AjaxResult.success("批量上传成功");
            ajax.put("files", successList);
            return ajax;
        }
        catch (Exception e)
        {
            log.error("批量上传文件到OSS失败", e);
            return AjaxResult.error("批量上传失败：" + e.getMessage());
        }
    }

    /**
     * 本地资源通用下载 (此部分与OSS无关，保持不变)
     */
    @GetMapping("/download/resource")
    public void resourceDownload(String resource, HttpServletRequest request, HttpServletResponse response)
            throws Exception
    {
        try
        {
            if (!FileUtils.checkAllowDownload(resource))
            {
                throw new Exception(StringUtils.format("资源文件({})非法，不允许下载。 ", resource));
            }
            // 本地资源路径
            String localPath = RuoYiConfig.getProfile();
            // 数据库资源地址
            String downloadPath = localPath + FileUtils.stripPrefix(resource);
            // 下载名称
            String downloadName = StringUtils.substringAfterLast(downloadPath, "/");
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, downloadName);
            FileUtils.writeBytes(downloadPath, response.getOutputStream());
        }
        catch (Exception e)
        {
            log.error("下载文件失败", e);
        }
    }
}