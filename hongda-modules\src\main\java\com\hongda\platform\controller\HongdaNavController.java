package com.hongda.platform.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hongda.common.annotation.Log;
import com.hongda.common.core.controller.BaseController;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.common.enums.BusinessType;
import com.hongda.platform.domain.HongdaNav;
import com.hongda.platform.service.IHongdaNavService;
import com.hongda.common.utils.poi.ExcelUtil;
import com.hongda.common.core.page.TableDataInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * 导航配置Controller
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Tag(name = "导航配置", description = "导航配置管理相关接口")
@RestController
@RequestMapping("/platform/nav")
public class HongdaNavController extends BaseController
{
    @Autowired
    private IHongdaNavService hongdaNavService;

    /**
     * 查询导航配置列表
     */
    @Operation(summary = "查询导航配置列表", description = "分页查询导航配置列表")
    @PreAuthorize("@ss.hasPermi('platform:nav:list')")
    @GetMapping("/list")
    public TableDataInfo list(HongdaNav hongdaNav)
    {
        startPage();
        List<HongdaNav> list = hongdaNavService.selectHongdaNavList(hongdaNav);
        return getDataTable(list);
    }

    /**
     * 导出导航配置列表
     */
    @Operation(summary = "导出导航配置列表", description = "导出导航配置数据到Excel")
    @PreAuthorize("@ss.hasPermi('platform:nav:export')")
    @Log(title = "导航配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HongdaNav hongdaNav)
    {
        List<HongdaNav> list = hongdaNavService.selectHongdaNavList(hongdaNav);
        ExcelUtil<HongdaNav> util = new ExcelUtil<HongdaNav>(HongdaNav.class);
        util.exportExcel(response, list, "导航配置数据");
    }

    /**
     * 获取导航配置详细信息
     */
    @Operation(summary = "获取导航配置详情", description = "根据ID获取导航配置详细信息")
    @PreAuthorize("@ss.hasPermi('platform:nav:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@Parameter(description = "导航配置ID") @PathVariable("id") Long id)
    {
        return success(hongdaNavService.selectHongdaNavById(id));
    }

    /**
     * 新增导航配置
     */
    @Operation(summary = "新增导航配置", description = "创建新的导航配置")
    @PreAuthorize("@ss.hasPermi('platform:nav:add')")
    @Log(title = "导航配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HongdaNav hongdaNav)
    {
        return toAjax(hongdaNavService.insertHongdaNav(hongdaNav));
    }

    /**
     * 修改导航配置
     */
    @Operation(summary = "修改导航配置", description = "更新导航配置信息")
    @PreAuthorize("@ss.hasPermi('platform:nav:edit')")
    @Log(title = "导航配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HongdaNav hongdaNav)
    {
        return toAjax(hongdaNavService.updateHongdaNav(hongdaNav));
    }

    /**
     * 删除导航配置
     */
    @Operation(summary = "删除导航配置", description = "批量删除导航配置")
    @PreAuthorize("@ss.hasPermi('platform:nav:remove')")
    @Log(title = "导航配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@Parameter(description = "导航配置ID数组") @PathVariable Long[] ids)
    {
        return toAjax(hongdaNavService.deleteHongdaNavByIds(ids));
    }
}
