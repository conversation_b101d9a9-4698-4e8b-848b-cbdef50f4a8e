package com.hongda.wxapp.interceptor;

import com.alibaba.fastjson2.JSONObject;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.data.domain.HongdaUser;
import com.hongda.data.mapper.HongdaUserMapper;
import com.hongda.framework.web.service.TokenService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.io.IOException;
import java.io.PrintWriter;

@Component
public class WxUserStatusInterceptor implements HandlerInterceptor {

    @Autowired
    private TokenService tokenService;

    @Autowired
    private HongdaUserMapper userMapper;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        Long userId = tokenService.getWxUserId(request);
        if (userId == null) {
            // 无小程序token，放行（公开接口或未登录态）
            return true;
        }

        HongdaUser user = userMapper.selectHongdaUserById(userId);
        if (user == null) {
            writeError(response, 401, "用户不存在或登录已过期");
            return false;
        }

        // 自主注销：isDeleted=1，可恢复，但当前会话不可用
        if (Integer.valueOf(1).equals(user.getIsDeleted())) {
            writeError(response, 403, "账号已注销，请重新登录");
            return false;
        }

        // 后台禁用：status=1，彻底封禁
        if (Integer.valueOf(1).equals(user.getStatus())) {
            writeError(response, 403, "账号已被禁用，请联系客服");
            return false;
        }

        return true;
    }

    private void writeError(HttpServletResponse response, int code, String msg) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        AjaxResult result = AjaxResult.error(code, msg);
        try (PrintWriter out = response.getWriter()) {
            out.write(JSONObject.toJSONString(result));
            out.flush();
        }
    }
}
