package com.hongda.content.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 活动定时任务服务类 - v2.0 版本
 * 用于处理活动相关的定时任务，专注于固化状态的更新
 * 
 * - 定时任务：负责固化状态更新（如"已结束"），确保数据库数据的最终一致性
 * - API实时计算：负责临时状态展示（如"进行中"），提供实时的用户体验
 * 
 * <AUTHOR>
 * @date 2025-01-22
 */
@Service("eventScheduledTaskService")
public class EventScheduledTaskService {

    @Autowired
    private IHongdaEventService hongdaEventService;

    /**
     * 定时任务方法：自动更新活动固化状态
     * 该方法专注于将需要永久固化的状态写入数据库
     * 
     * 主要职责：
     * 1. 将已过结束时间的活动状态更新为"已结束"（状态码2）
     * 2. 不处理临时状态（如"进行中"），该状态由API实时计算
     * 
     * 调用目标字符串：eventScheduledTaskService.updateEventStatusJob()
     * 推荐执行时间：每天凌晨1点（0 0 1 * * ?）
     */
    public void updateEventStatusJob() {
        try {
            System.out.println("========== 开始执行活动状态固化定时任务 (v2.0) ==========");
            System.out.println("任务职责：仅更新需要固化的状态（如：已结束）");
            System.out.println("临时状态（如：进行中）由API实时计算，不在此处理");
            
            int updatedCount = hongdaEventService.updateEventStatusAutomatically();
            
            if (updatedCount > 0) {
                System.out.println("定时任务执行成功！共固化了 " + updatedCount + " 条活动状态记录");
            } else {
                System.out.println("ℹ定时任务执行完成，当前没有需要固化状态的活动");
            }
            
            System.out.println("========== 活动状态固化定时任务执行完成 ==========");
            
        } catch (Exception e) {
            System.err.println(" 执行活动状态固化定时任务时发生错误：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 手动触发活动状态固化（可用于测试）
     * 
     * @return 更新的记录数
     */
    public int manualUpdateEventStatus() {
        System.out.println("手动触发活动状态固化更新...");
        return hongdaEventService.updateEventStatusAutomatically();
    }
} 