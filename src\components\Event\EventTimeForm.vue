<template>
  <el-card shadow="never" class="form-card">
    <template #header>
      <div class="card-header">
        <span class="card-title">时间设置</span>
      </div>
    </template>
    
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="活动时间" prop="startTime">
          <el-row :gutter="12">
            <el-col :span="12">
              <el-date-picker 
                clearable 
                v-model="form.startTime" 
                type="datetime" 
                value-format="YYYY-MM-DD HH:mm:ss" 
                placeholder="开始时间"
                style="width: 100%"
              />
            </el-col>
            <el-col :span="12">
              <el-date-picker 
                clearable 
                v-model="form.endTime" 
                type="datetime" 
                value-format="YYYY-MM-DD HH:mm:ss" 
                placeholder="结束时间"
                style="width: 100%"
              />
            </el-col>
          </el-row>
        </el-form-item>
      </el-col>
      
      <el-col :span="12">
        <el-form-item label="报名时间" prop="registrationStartTime">
          <el-row :gutter="12">
            <el-col :span="12">
              <el-date-picker 
                clearable 
                v-model="form.registrationStartTime" 
                type="datetime" 
                value-format="YYYY-MM-DD HH:mm:ss" 
                placeholder="报名开始"
                style="width: 100%"
              />
            </el-col>
            <el-col :span="12">
              <el-date-picker 
                clearable 
                v-model="form.registrationEndTime" 
                type="datetime" 
                value-format="YYYY-MM-DD HH:mm:ss" 
                placeholder="报名截止"
                style="width: 100%"
              />
            </el-col>
          </el-row>
        </el-form-item>
      </el-col>
    </el-row>
  </el-card>
</template>

<script setup>
// Props
const props = defineProps({
  form: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['update:form'])
</script>

<style scoped>
/* 表单卡片样式 */
.form-card {
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.form-card :deep(.el-card__header) {
  padding: 16px 20px;
  background-color: #fafafa;
  border-bottom: 1px solid #e4e7ed;
}

.form-card :deep(.el-card__body) {
  padding: 24px 20px;
}

/* 卡片标题样式 */
.card-header {
  display: flex;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

/* 时间选择器组合样式 */
.el-date-picker .el-input__wrapper {
  border-radius: 6px;
}
</style>
