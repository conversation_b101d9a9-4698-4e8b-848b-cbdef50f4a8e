import request from '@/utils/request'

// 获取指定活动的表单定义
export function getFormDefinition(eventId) {
  // 后端Controller的路径应为: /content/form-definition/event/{eventId}
  return request({
    url: '/content/form-definition/event/' + eventId,
    method: 'get'
  })
}

// 保存或更新活动的表单定义
export function saveFormDefinition(data) {
  // 后端Controller的路径应为: /content/form-definition
  return request({
    url: '/content/form-definition',
    method: 'post',
    data: data
  })
}

// 获取指定活动的所有报名记录
export function getRegistrationList(eventId) {
  // 后端Controller的路径应为: /data/registration/list/{eventId}
  return request({
    url: '/data/registration/list/' + eventId,
    method: 'get'
  })
}