package com.hongda.wxapp.service;

import com.hongda.data.domain.HongdaEventRegistration;

/**
 * 微信小程序报名服务接口
 * 
 * <AUTHOR>
 */
public interface IWxRegistrationService
{
    /**
     * 检查用户是否已报名指定活动
     * 
     * @param eventId 活动ID
     * @param userId 用户ID
     * @return 是否已报名
     */
    boolean checkUserRegistration(Long eventId, Long userId);

    /**
     * 根据活动ID和用户ID获取报名记录
     * 
     * @param eventId 活动ID
     * @param userId 用户ID
     * @return 报名记录，未报名则返回null
     */
    HongdaEventRegistration getRegistrationByEventAndUser(Long eventId, Long userId);

    /**
     * 用户报名活动
     * 
     * @param eventId 活动ID
     * @param userId 用户ID
     * @param formData 表单数据
     * @return 报名记录
     */
    HongdaEventRegistration registerEvent(Long eventId, Long userId, String formData);

    /**
     * 取消报名
     * 
     * @param eventId 活动ID
     * @param userId 用户ID
     * @return 操作结果
     */
    boolean cancelRegistration(Long eventId, Long userId);

    /**
     * 获取用户的报名记录列表
     * 
     * @param userId 用户ID
     * @return 报名记录列表
     */
    java.util.List<HongdaEventRegistration> getUserRegistrations(Long userId);
} 