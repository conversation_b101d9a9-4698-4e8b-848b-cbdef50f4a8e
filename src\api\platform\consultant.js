import request from '@/utils/request'

// 查询顾问管理列表
export function listConsultant(query) {
  return request({
    url: '/platform/consultant/list',
    method: 'get',
    params: query
  })
}

// 查询顾问管理详细
export function getConsultant(id) {
  return request({
    url: '/platform/consultant/' + id,
    method: 'get'
  })
}

// 新增顾问管理
export function addConsultant(data) {
  return request({
    url: '/platform/consultant',
    method: 'post',
    data: data
  })
}

// 修改顾问管理
export function updateConsultant(data) {
  return request({
    url: '/platform/consultant',
    method: 'put',
    data: data
  })
}

// 删除顾问管理
export function delConsultant(id) {
  return request({
    url: '/platform/consultant/' + id,
    method: 'delete'
  })
}
