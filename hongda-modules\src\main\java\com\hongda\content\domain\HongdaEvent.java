package com.hongda.content.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hongda.common.annotation.Excel;
import com.hongda.common.annotation.OssUrl;
import com.hongda.common.core.domain.BaseEntity;
import com.hongda.content.enums.ActivityStatus;
import com.hongda.content.enums.RegistrationStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 活动管理对象 hongda_event
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@Data
@Schema(description = "活动管理")
public class HongdaEvent extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 活动ID, 主键 */
    @Schema(description = "活动ID", example = "1")
    private Long id;

    /** 活动名称 */
    @Excel(name = "活动名称")
    @Schema(description = "活动名称", example = "红大走出去交流会")
    private String title;

    /** 活动卡片图标 */
    @Excel(name = "活动卡片图标")
    @Schema(description = "活动卡片左侧图标的URL", example = "https://example.com/icon.png")
    @OssUrl
    private String iconUrl;

    /** 活动封面图片 */
    @Excel(name = "活动封面图片")
    @Schema(description = "活动封面图片", example = "https://example.com/cover.jpg")
    @OssUrl
    private String coverImageUrl;

    /** 活动图文详情 (富文本) */
    @Excel(name = "活动图文详情 (富文本)")
    @Schema(description = "活动图文详情", example = "<p>活动详细介绍...</p>")
    private String details;

    /** 活动简介 */
    @Excel(name = "活动简介")
    @Schema(description = "活动简介", example = "这是一场精彩的交流活动")
    private String summary;

    /** 卖点 */
    @Excel(name = "卖点")
    @Schema(description = "活动卖点", example = "专业导师指导，实战经验分享")
    private String sellPoint;

    /** 活动开始时间 (用于列表排序) */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "活动开始时间 (用于列表排序)", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "活动开始时间", example = "2025-08-01 09:00:00")
    private Date startTime;

    /** 活动结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "活动结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "活动结束时间", example = "2025-08-02 18:00:00")
    private Date endTime;

    /** 活动地点 (完整地址，冗余字段) */
    @Excel(name = "活动地点")
    @Schema(description = "活动地点", example = "北京市朝阳区会议中心")
    private String location;

    /** 省份 */
    @Excel(name = "省份")
    @Schema(description = "省份", example = "北京市")
    private String province;

    /** 城市 */
    @Excel(name = "城市")
    @Schema(description = "城市", example = "北京市")
    private String city;

    /** 区/县 */
    @Excel(name = "区/县")
    @Schema(description = "区/县", example = "朝阳区")
    private String district;

    /** 详细地址 */
    @Excel(name = "详细地址")
    @Schema(description = "详细地址", example = "XX街道XX号XX大厦")
    private String addressDetail;

    /** 报名截止时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "报名截止时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "报名截止时间", example = "2025-07-30 23:59:59")
    private Date registrationDeadline;

    /** 最大报名人数 (0为不限制) */
    @Excel(name = "最大报名人数 (0为不限制)")
    @Schema(description = "最大报名人数", example = "100")
    private Long maxParticipants;

    /** 当前已报名人数 (冗余字段, 用于高效计算剩余名额) */
    @Excel(name = "当前已报名人数 (冗余字段, 用于高效计算剩余名额)")
    @Schema(description = "当前已报名人数", example = "50")
    private Long registeredCount;

    /** 状态 (0=未开始, 1=报名中, 2=已结束, 3=已取消) */
    @Excel(name = "状态 (0=未开始, 1=报名中, 2=已结束, 3=已取消)")
    @Schema(description = "活动状态", example = "1", allowableValues = {"0", "1", "2", "3"})
    private Integer status;

    /** 是否为热门活动 (0=否, 1=是) */
    @Excel(name = "是否热门", readConverterExp = "0=否,1=是")
    @Schema(description = "是否为热门活动", example = "0", allowableValues = {"0", "1"})
    private Integer isHot;

    /** 是否推广到首页 (0=否, 1=是) */
    @Excel(name = "是否推广", readConverterExp = "0=否,1=是")
    @Schema(description = "是否推广到首页", example = "0", allowableValues = {"0", "1"})
    private Integer isPromoted;

    /** 推广标题 */
    @Excel(name = "推广标题")
    @Schema(description = "推广标题", example = "精彩活动推荐")
    private String promotionTitle;

    /** 推广图片URL */
    @Excel(name = "推广图片")
    @Schema(description = "推广图片URL", example = "https://example.com/promo.jpg")
    @OssUrl
    private String promotionImageUrl;

    /** 推广开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "推广开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "推广开始时间", example = "2025-08-01 00:00:00")
    private Date promotionStartTime;

    /** 推广结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "推广结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "推广结束时间", example = "2025-08-31 23:59:59")
    private Date promotionEndTime;

    /** 推广排序 */
    @Excel(name = "推广排序")
    @Schema(description = "推广排序", example = "1")
    private Integer promotionSortOrder;

    /** 报名开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "报名开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "报名开始时间", example = "2025-07-20 09:00:00")
    private Date registrationStartTime;

    /** 报名结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "报名结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "报名结束时间", example = "2025-07-30 23:59:59")
    private Date registrationEndTime;
    /**
     * 动态计算活动状态
     * 基于活动开始时间和结束时间计算
     * 
     * @return 活动状态码：0=未开始，1=进行中，2=已结束
     */
    public Integer getActivityStatus() {
        return ActivityStatus.calculateStatus(this.startTime, this.endTime).getCode();
    }

    /**
     * 动态计算报名状态
     * 基于报名开始时间和结束时间计算
     * 
     * @return 报名状态码：0=未开始，1=报名中，2=已结束
     */
    public Integer getRegistrationStatus() {
        return RegistrationStatus.calculateStatus(this.registrationStartTime, this.registrationEndTime).getCode();
    }

    /**
     * 获取活动状态的中文描述
     * 
     * @return 活动状态的中文描述
     */
    public String getActivityStatusText() {
        return ActivityStatus.calculateStatus(this.startTime, this.endTime).getDesc();
    }

    /**
     * 获取报名状态的中文描述
     * 
     * @return 报名状态的中文描述
     */
    public String getRegistrationStatusText() {
        return RegistrationStatus.calculateStatus(this.registrationStartTime, this.registrationEndTime).getDesc();
    }

    /**
     * 判断是否可以报名
     * 
     * @return true表示可以报名，false表示不可以报名
     */
    public boolean canRegister() {
        return getRegistrationStatus() == 1; // 报名状态为"报名中"
    }



    /**
     * 根据省市区和详细地址自动生成完整地址
     * 用于保存时自动填充location字段
     * 
     * @return 拼接后的完整地址
     */
    public String generateFullLocation() {
        StringBuilder fullLocation = new StringBuilder();
        
        if (province != null && !province.trim().isEmpty()) {
            fullLocation.append(province.trim());
        }
        
        if (city != null && !city.trim().isEmpty()) {
            // 避免重复添加省份信息（如北京市既是省份也是城市）
            String cityTrimmed = city.trim();
            if (!cityTrimmed.equals(province)) {
                fullLocation.append(cityTrimmed);
            }
        }
        
        if (district != null && !district.trim().isEmpty()) {
            fullLocation.append(district.trim());
        }
        
        if (addressDetail != null && !addressDetail.trim().isEmpty()) {
            fullLocation.append(addressDetail.trim());
        }
        
        return fullLocation.toString();
    }

    /**
     * 自动更新location字段
     * 在保存前调用此方法，确保location字段与结构化地址字段保持一致
     */
    public void updateLocationFromStructuredAddress() {
        this.location = generateFullLocation();
    }

    public Date getRegistrationStartTime() {
        return registrationStartTime;
    }

    public void setRegistrationStartTime(Date registrationStartTime) {
        this.registrationStartTime = registrationStartTime;
    }

    public Date getRegistrationEndTime() {
        return registrationEndTime;
    }

    public void setRegistrationEndTime(Date registrationEndTime) {
        this.registrationEndTime = registrationEndTime;
    }

}
