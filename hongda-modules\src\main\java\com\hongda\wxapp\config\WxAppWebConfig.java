package com.hongda.wxapp.config;

import com.hongda.wxapp.interceptor.WxUserStatusInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WxAppWebConfig implements WebMvcConfigurer {

    @Autowired
    private WxUserStatusInterceptor wxUserStatusInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(wxUserStatusInterceptor)
                .addPathPatterns("/api/v1/**", "/common/**");
    }
}