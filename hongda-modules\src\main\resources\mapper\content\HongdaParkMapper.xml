<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongda.content.mapper.HongdaParkMapper">
    
    <resultMap type="HongdaPark" id="HongdaParkResult">
        <result property="id"    column="id"    />
        <result property="countryId"    column="country_id"    />
        <result property="name"    column="name"    />
        <result property="location"    column="location"    />
        <result property="mainIndustries"    column="main_industries"    />
        <result property="summary"    column="summary"    />
        <result property="coverImageUrl"    column="cover_image_url"    />
        <result property="content"    column="content"    />
        <result property="status"    column="status"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectHongdaParkVo">
        select id, country_id, name, location, main_industries, summary, cover_image_url, content, status, sort_order, create_by, create_time, update_by, update_time from hongda_park
    </sql>

    <select id="selectHongdaParkList" parameterType="HongdaPark" resultMap="HongdaParkResult">
        <include refid="selectHongdaParkVo"/>
        <where>  
            <if test="countryId != null "> and country_id = #{countryId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectHongdaParkById" parameterType="Long" resultMap="HongdaParkResult">
        <include refid="selectHongdaParkVo"/>
        where id = #{id}
    </select>

    <insert id="insertHongdaPark" parameterType="HongdaPark" useGeneratedKeys="true" keyProperty="id">
        insert into hongda_park
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="countryId != null">country_id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="location != null">location,</if>
            <if test="mainIndustries != null">main_industries,</if>
            <if test="summary != null">summary,</if>
            <if test="coverImageUrl != null">cover_image_url,</if>
            <if test="content != null">content,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="countryId != null">#{countryId},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="location != null">#{location},</if>
            <if test="mainIndustries != null">#{mainIndustries},</if>
            <if test="summary != null">#{summary},</if>
            <if test="coverImageUrl != null">#{coverImageUrl},</if>
            <if test="content != null">#{content},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateHongdaPark" parameterType="HongdaPark">
        update hongda_park
        <trim prefix="SET" suffixOverrides=",">
            <if test="countryId != null">country_id = #{countryId},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="location != null">location = #{location},</if>
            <if test="mainIndustries != null">main_industries = #{mainIndustries},</if>
            <if test="summary != null">summary = #{summary},</if>
            <if test="coverImageUrl != null">cover_image_url = #{coverImageUrl},</if>
            <if test="content != null">content = #{content},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHongdaParkById" parameterType="Long">
        delete from hongda_park where id = #{id}
    </delete>

    <delete id="deleteHongdaParkByIds" parameterType="String">
        delete from hongda_park where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>