import request from '@/utils/request'

// 查询活动报名记录 (对应“报名数据”管理)列表
export function listRegistration(query) {
  return request({
    url: '/data/registration/list',
    method: 'get',
    params: query
  })
}

// 查询活动报名记录 (对应“报名数据”管理)详细
export function getRegistration(id) {
  return request({
    url: '/data/registration/' + id,
    method: 'get'
  })
}

// 新增活动报名记录 (对应“报名数据”管理)
export function addRegistration(data) {
  return request({
    url: '/data/registration',
    method: 'post',
    data: data
  })
}

// 修改活动报名记录 (对应“报名数据”管理)
export function updateRegistration(data) {
  return request({
    url: '/data/registration',
    method: 'put',
    data: data
  })
}

// 删除活动报名记录 (对应“报名数据”管理)
export function delRegistration(id) {
  return request({
    url: '/data/registration/' + id,
    method: 'delete'
  })
}

// 根据活动ID获取报名列表（用于活动管理页面的查看报名弹窗）
export function getRegistrationList(eventId) {
  return request({
    url: '/data/registration/list/' + eventId,
    method: 'get'
  })
}

// 根据活动ID获取包含用户信息的报名列表（支持分页）
export function getRegistrationListWithUser(query) {
  return request({
    url: '/data/registration/list-with-user',
    method: 'get',
    params: query
  })
}

// 根据活动ID导出报名数据
export function exportRegistrationByEvent(query) {
  return request({
    url: '/data/registration/export-by-event',
    method: 'post',
    params: query
  })
}
