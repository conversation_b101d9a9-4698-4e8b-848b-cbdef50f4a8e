package com.hongda.platform.service;

import java.util.List;
import com.hongda.platform.domain.HongdaStaticAsset;

/**
 * 小程序静态资源配置Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
public interface IHongdaStaticAssetService 
{
    /**
     * 查询小程序静态资源配置
     * 
     * @param id 小程序静态资源配置主键
     * @return 小程序静态资源配置
     */
    public HongdaStaticAsset selectHongdaStaticAssetById(Long id);

    /**
     * 查询小程序静态资源配置列表
     * 
     * @param hongdaStaticAsset 小程序静态资源配置
     * @return 小程序静态资源配置集合
     */
    public List<HongdaStaticAsset> selectHongdaStaticAssetList(HongdaStaticAsset hongdaStaticAsset);

    /**
     * 新增小程序静态资源配置
     * 
     * @param hongdaStaticAsset 小程序静态资源配置
     * @return 结果
     */
    public int insertHongdaStaticAsset(HongdaStaticAsset hongdaStaticAsset);

    /**
     * 修改小程序静态资源配置
     * 
     * @param hongdaStaticAsset 小程序静态资源配置
     * @return 结果
     */
    public int updateHongdaStaticAsset(HongdaStaticAsset hongdaStaticAsset);

    /**
     * 批量删除小程序静态资源配置
     * 
     * @param ids 需要删除的小程序静态资源配置主键集合
     * @return 结果
     */
    public int deleteHongdaStaticAssetByIds(Long[] ids);

    /**
     * 删除小程序静态资源配置信息
     * 
     * @param id 小程序静态资源配置主键
     * @return 结果
     */
    public int deleteHongdaStaticAssetById(Long id);
}
