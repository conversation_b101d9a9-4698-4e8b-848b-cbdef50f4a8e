package com.hongda.wxapp.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.hongda.common.core.controller.BaseController;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.common.utils.ServletUtils;
import com.hongda.data.domain.HongdaEventRegistration;
import com.hongda.framework.web.service.TokenService;
import com.hongda.wxapp.service.IWxRegistrationService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * 微信小程序报名接口控制器
 * 
 * <AUTHOR>
 */
@Tag(name = "小程序报名接口", description = "小程序端报名相关接口")
@RestController
@RequestMapping("/api/v1/registration")
public class WxRegistrationController extends BaseController
{
    @Autowired
    private IWxRegistrationService wxRegistrationService;
    
    @Autowired
    private TokenService tokenService;

    /**
     * 检查用户报名状态
     */
    @Operation(summary = "检查报名状态", description = "检查当前用户是否已报名指定活动")
    @GetMapping("/check")
    public AjaxResult checkRegistrationStatus(
        @Parameter(description = "活动ID", required = true) @RequestParam Long eventId
    )
    {
        try
        {
            // 使用TokenService统一获取用户ID
            Long userId = tokenService.getWxUserId(ServletUtils.getRequest());
            if (userId == null)
            {
                return AjaxResult.error("用户未登录");
            }
            
            boolean isRegistered = wxRegistrationService.checkUserRegistration(eventId, userId);
            
            return AjaxResult.success()
                .put("isRegistered", isRegistered)
                .put("eventId", eventId)
                .put("userId", userId);
        }
        catch (Exception e)
        {
            logger.error("检查报名状态失败", e);
            return AjaxResult.error("检查报名状态失败: " + e.getMessage());
        }
    }

    /**
     * 用户报名活动
     */
    @Operation(summary = "报名活动", description = "用户报名参加活动")
    @PostMapping("/register")
    public AjaxResult registerEvent(@RequestBody RegistrationRequest request)
    {
        try
        {
            // 使用TokenService统一获取用户ID
            Long userId = tokenService.getWxUserId(ServletUtils.getRequest());
            if (userId == null)
            {
                return AjaxResult.error("用户未登录");
            }
            
            HongdaEventRegistration registration = wxRegistrationService.registerEvent(
                request.getEventId(), 
                userId, 
                request.getFormData()
            );
            
            return AjaxResult.success(registration);
        }
        catch (RuntimeException e)
        {
            return AjaxResult.error(e.getMessage());
        }
        catch (Exception e)
        {
            logger.error("报名失败", e);
            return AjaxResult.error("报名失败: " + e.getMessage());
        }
    }

    /**
     * 取消报名
     */
    @Operation(summary = "取消报名", description = "用户取消活动报名")
    @PostMapping("/cancel")
    public AjaxResult cancelRegistration(@RequestBody CancelRequest request)
    {
        try
        {
            // 使用TokenService统一获取用户ID
            Long userId = tokenService.getWxUserId(ServletUtils.getRequest());
            if (userId == null)
            {
                return AjaxResult.error("用户未登录");
            }
            
            boolean result = wxRegistrationService.cancelRegistration(request.getEventId(), userId);
            
            if (result)
            {
                return AjaxResult.success("取消报名成功");
            }
            else
            {
                return AjaxResult.error("取消报名失败，可能您未报名此活动");
            }
        }
        catch (Exception e)
        {
            logger.error("取消报名失败", e);
            return AjaxResult.error("取消报名失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户报名记录列表
     */
    @Operation(summary = "获取用户报名记录", description = "获取当前用户的所有报名记录")
    @GetMapping("/my-registrations")
    public AjaxResult getMyRegistrations()
    {
        try
        {
            // 使用TokenService统一获取用户ID
            Long userId = tokenService.getWxUserId(ServletUtils.getRequest());
            if (userId == null)
            {
                return AjaxResult.error("用户未登录");
            }
            
            List<HongdaEventRegistration> registrations = wxRegistrationService.getUserRegistrations(userId);
            
            return AjaxResult.success(registrations);
        }
        catch (Exception e)
        {
            logger.error("获取报名记录失败", e);
            return AjaxResult.error("获取报名记录失败: " + e.getMessage());
        }
    }

    /**
     * 报名请求对象
     */
    public static class RegistrationRequest
    {
        private Long eventId;
        private String formData;

        public Long getEventId() { return eventId; }
        public void setEventId(Long eventId) { this.eventId = eventId; }
        
        public String getFormData() { return formData; }
        public void setFormData(String formData) { this.formData = formData; }
    }

    /**
     * 取消报名请求对象
     */
    public static class CancelRequest
    {
        private Long eventId;

        public Long getEventId() { return eventId; }
        public void setEventId(Long eventId) { this.eventId = eventId; }
    }
}