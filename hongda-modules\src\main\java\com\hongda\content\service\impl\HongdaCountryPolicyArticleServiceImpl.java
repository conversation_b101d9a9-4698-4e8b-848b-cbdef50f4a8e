package com.hongda.content.service.impl;

import java.util.List;
import com.hongda.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.hongda.content.mapper.HongdaCountryPolicyArticleMapper;
import com.hongda.content.domain.HongdaCountryPolicyArticle;
import com.hongda.content.service.IHongdaCountryPolicyArticleService;

/**
 * 国别政策管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@Service
public class HongdaCountryPolicyArticleServiceImpl implements IHongdaCountryPolicyArticleService 
{
    @Autowired
    private HongdaCountryPolicyArticleMapper hongdaCountryPolicyArticleMapper;

    /**
     * 查询国别政策管理
     * 
     * @param articleId 国别政策管理主键
     * @return 国别政策管理
     */
    @Override
    public HongdaCountryPolicyArticle selectHongdaCountryPolicyArticleByArticleId(Long articleId)
    {
        return hongdaCountryPolicyArticleMapper.selectHongdaCountryPolicyArticleByArticleId(articleId);
    }

    /**
     * 查询国别政策管理列表
     * 
     * @param hongdaCountryPolicyArticle 国别政策管理
     * @return 国别政策管理
     */
    @Override
    public List<HongdaCountryPolicyArticle> selectHongdaCountryPolicyArticleList(HongdaCountryPolicyArticle hongdaCountryPolicyArticle)
    {
        return hongdaCountryPolicyArticleMapper.selectHongdaCountryPolicyArticleList(hongdaCountryPolicyArticle);
    }

    /**
     * 新增国别政策管理
     * 
     * @param hongdaCountryPolicyArticle 国别政策管理
     * @return 结果
     */
    @Override
    public int insertHongdaCountryPolicyArticle(HongdaCountryPolicyArticle hongdaCountryPolicyArticle)
    {
        hongdaCountryPolicyArticle.setCreateTime(DateUtils.getNowDate());
        return hongdaCountryPolicyArticleMapper.insertHongdaCountryPolicyArticle(hongdaCountryPolicyArticle);
    }

    /**
     * 修改国别政策管理
     * 
     * @param hongdaCountryPolicyArticle 国别政策管理
     * @return 结果
     */
    @Override
    public int updateHongdaCountryPolicyArticle(HongdaCountryPolicyArticle hongdaCountryPolicyArticle)
    {
        hongdaCountryPolicyArticle.setUpdateTime(DateUtils.getNowDate());
        return hongdaCountryPolicyArticleMapper.updateHongdaCountryPolicyArticle(hongdaCountryPolicyArticle);
    }

    /**
     * 批量删除国别政策管理
     * 
     * @param articleIds 需要删除的国别政策管理主键
     * @return 结果
     */
    @Override
    public int deleteHongdaCountryPolicyArticleByArticleIds(Long[] articleIds)
    {
        return hongdaCountryPolicyArticleMapper.deleteHongdaCountryPolicyArticleByArticleIds(articleIds);
    }

    /**
     * 删除国别政策管理信息
     * 
     * @param articleId 国别政策管理主键
     * @return 结果
     */
    @Override
    public int deleteHongdaCountryPolicyArticleByArticleId(Long articleId)
    {
        return hongdaCountryPolicyArticleMapper.deleteHongdaCountryPolicyArticleByArticleId(articleId);
    }
}
