package com.hongda.content.service;

import java.util.List;
import com.hongda.content.domain.HongdaCountry;

/**
 * 国别信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface IHongdaCountryService 
{
    /**
     * 查询国别信息
     * 
     * @param id 国别信息主键
     * @return 国别信息
     */
    public HongdaCountry selectHongdaCountryById(Long id);

    /**
     * 查询国别信息列表
     * 
     * @param hongdaCountry 国别信息
     * @return 国别信息集合
     */
    public List<HongdaCountry> selectHongdaCountryList(HongdaCountry hongdaCountry);

    /**
     * 新增国别信息
     * 
     * @param hongdaCountry 国别信息
     * @return 结果
     */
    public int insertHongdaCountry(HongdaCountry hongdaCountry);

    /**
     * 修改国别信息
     * 
     * @param hongdaCountry 国别信息
     * @return 结果
     */
    public int updateHongdaCountry(HongdaCountry hongdaCountry);

    /**
     * 批量删除国别信息
     * 
     * @param ids 需要删除的国别信息主键集合
     * @return 结果
     */
    public int deleteHongdaCountryByIds(Long[] ids);

    /**
     * 删除国别信息信息
     * 
     * @param id 国别信息主键
     * @return 结果
     */
    public int deleteHongdaCountryById(Long id);
}
