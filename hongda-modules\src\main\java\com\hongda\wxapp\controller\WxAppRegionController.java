package com.hongda.wxapp.controller;

import com.hongda.common.core.controller.BaseController;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.content.domain.HongdaRegion;
import com.hongda.content.service.IHongdaRegionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 小程序端 - 地区接口控制器
 * <p>
 * 专为小程序客户端提供公开的、只读的地区数据服务。
 * </p>
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/v1/content") // 与文章接口保持统一的API根路径
public class WxAppRegionController extends BaseController {

    @Autowired
    private IHongdaRegionService regionService;

    /**
     * 获取所有可用的内容地区列表
     * <p>
     * 用于前端筛选面板中动态展示地区选项。
     * 只返回状态为“正常”的地区，并按排序值排序。
     * </p>
     *
     * @return 地区列表
     */
    @GetMapping("/regions")
    public AjaxResult getRegions() {
        // 1. 创建查询参数对象
        HongdaRegion queryParams = new HongdaRegion();

        // 2. 设置筛选条件：只查询状态为“正常”的地区
        queryParams.setStatus("0");

        // 3. 执行查询 (若依的Service层会自动处理排序)
        List<HongdaRegion> list = regionService.selectHongdaRegionList(queryParams);

        // 4. 返回成功结果
        return AjaxResult.success(list);
    }
}