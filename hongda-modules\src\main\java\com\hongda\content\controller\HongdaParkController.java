package com.hongda.content.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hongda.common.annotation.Log;
import com.hongda.common.core.controller.BaseController;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.common.enums.BusinessType;
import com.hongda.content.domain.HongdaPark;
import com.hongda.content.service.IHongdaParkService;
import com.hongda.common.utils.poi.ExcelUtil;
import com.hongda.common.core.page.TableDataInfo;

/**
 * 园区信息Controller
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
@RestController
@RequestMapping("/content/park")
public class HongdaParkController extends BaseController
{
    @Autowired
    private IHongdaParkService hongdaParkService;

    /**
     * 查询园区信息列表
     */
    @PreAuthorize("@ss.hasPermi('content:park:list')")
    @GetMapping("/list")
    public TableDataInfo list(HongdaPark hongdaPark)
    {
        startPage();
        List<HongdaPark> list = hongdaParkService.selectHongdaParkList(hongdaPark);
        return getDataTable(list);
    }

    /**
     * 导出园区信息列表
     */
    @PreAuthorize("@ss.hasPermi('content:park:export')")
    @Log(title = "园区信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HongdaPark hongdaPark)
    {
        List<HongdaPark> list = hongdaParkService.selectHongdaParkList(hongdaPark);
        ExcelUtil<HongdaPark> util = new ExcelUtil<HongdaPark>(HongdaPark.class);
        util.exportExcel(response, list, "园区信息数据");
    }

    /**
     * 获取园区信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('content:park:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(hongdaParkService.selectHongdaParkById(id));
    }

    /**
     * 新增园区信息
     */
    @PreAuthorize("@ss.hasPermi('content:park:add')")
    @Log(title = "园区信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HongdaPark hongdaPark)
    {
        return toAjax(hongdaParkService.insertHongdaPark(hongdaPark));
    }

    /**
     * 修改园区信息
     */
    @PreAuthorize("@ss.hasPermi('content:park:edit')")
    @Log(title = "园区信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HongdaPark hongdaPark)
    {
        return toAjax(hongdaParkService.updateHongdaPark(hongdaPark));
    }

    /**
     * 删除园区信息
     */
    @PreAuthorize("@ss.hasPermi('content:park:remove')")
    @Log(title = "园区信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(hongdaParkService.deleteHongdaParkByIds(ids));
    }
}
