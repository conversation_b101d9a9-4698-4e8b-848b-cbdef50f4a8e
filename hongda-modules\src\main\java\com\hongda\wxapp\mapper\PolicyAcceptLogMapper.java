package com.hongda.wxapp.mapper;

import java.util.List;
import com.hongda.wxapp.domain.PolicyAcceptLog;

/**
 * 协议同意留痕记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-22
 */
public interface PolicyAcceptLogMapper 
{
    /**
     * 查询协议同意留痕记录
     * 
     * @param id 协议同意留痕记录主键
     * @return 协议同意留痕记录
     */
    public PolicyAcceptLog selectPolicyAcceptLogById(Long id);

    /**
     * 查询协议同意留痕记录列表
     * 
     * @param policyAcceptLog 协议同意留痕记录
     * @return 协议同意留痕记录集合
     */
    public List<PolicyAcceptLog> selectPolicyAcceptLogList(PolicyAcceptLog policyAcceptLog);

    /**
     * 新增协议同意留痕记录
     * 
     * @param policyAcceptLog 协议同意留痕记录
     * @return 结果
     */
    public int insertPolicyAcceptLog(PolicyAcceptLog policyAcceptLog);

    /**
     * 修改协议同意留痕记录
     * 
     * @param policyAcceptLog 协议同意留痕记录
     * @return 结果
     */
    public int updatePolicyAcceptLog(PolicyAcceptLog policyAcceptLog);

    /**
     * 删除协议同意留痕记录
     * 
     * @param id 协议同意留痕记录主键
     * @return 结果
     */
    public int deletePolicyAcceptLogById(Long id);

    /**
     * 批量删除协议同意留痕记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePolicyAcceptLogByIds(Long[] ids);

    /**
     * 根据用户ID和协议类型查询最新的同意记录
     * 
     * @param userId 用户ID
     * @param policyType 协议类型
     * @return 协议同意记录
     */
    public PolicyAcceptLog selectLatestByUserIdAndType(Long userId, String policyType);

    /**
     * 根据用户ID、协议类型和版本查询同意记录
     * 
     * @param userId 用户ID
     * @param policyType 协议类型
     * @param policyVersion 协议版本
     * @return 协议同意记录
     */
    public PolicyAcceptLog selectByUserIdAndTypeAndVersion(Long userId, String policyType, String policyVersion);
}
