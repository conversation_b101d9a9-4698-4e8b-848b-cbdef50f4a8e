<template>
  <el-card shadow="never" class="form-card">
    <template #header>
      <div class="card-header">
        <span class="card-title">报名设置</span>
      </div>
    </template>
    
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="最大报名人数" prop="maxParticipants">
          <div class="participant-setting">
            <el-input-number
              :model-value="form.maxParticipants"
              @update:model-value="updateField('maxParticipants', $event)"
              :min="0"
              placeholder="输入人数"
              style="width: 200px"
              controls-position="right"
            />
            <span class="participant-tip">（设为0表示不限制人数）</span>
          </div>
        </el-form-item>
      </el-col>
    </el-row>
  </el-card>
</template>

<script setup>
// Props
const props = defineProps({
  form: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['update:form'])

// Methods
const updateField = (field, value) => {
  const updatedForm = { ...props.form, [field]: value }
  emit('update:form', updatedForm)
}
</script>

<style scoped>
/* 表单卡片样式 */
.form-card {
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.form-card :deep(.el-card__header) {
  padding: 16px 20px;
  background-color: #fafafa;
  border-bottom: 1px solid #e4e7ed;
}

.form-card :deep(.el-card__body) {
  padding: 24px 20px;
}

/* 卡片标题样式 */
.card-header {
  display: flex;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

/* 报名人数设置样式 */
.participant-setting {
  display: flex;
  align-items: center;
  gap: 12px;
}

.participant-tip {
  font-size: 12px;
  color: #909399;
}
</style>
