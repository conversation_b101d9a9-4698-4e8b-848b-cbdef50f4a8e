package com.hongda.wxapp.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.common.core.domain.dto.UserProfileDTO;
import com.hongda.common.core.domain.dto.WxLoginDTO;
import com.hongda.framework.web.service.TokenService;
import com.hongda.common.utils.ServletUtils;
import com.hongda.wxapp.service.IWxUserService;
import com.hongda.wxapp.service.IWxEventService;
import com.hongda.wxapp.config.WxConfigValidator;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * 微信小程序接口控制器
 *
 * <AUTHOR>
 */
@Tag(name = "微信小程序接口", description = "微信小程序用户登录和信息管理")
@RestController
@RequestMapping("/api/v1")
public class WxAppController
{
    @Autowired
    private IWxUserService wxUserService;

    @Autowired
    private IWxEventService wxEventService;

    @Autowired
    private TokenService tokenService;
    
    @Autowired
    private WxConfigValidator wxConfigValidator;

    /**
     * 小程序登录
     */
    @Operation(summary = "小程序登录", description = "通过微信授权码登录小程序")
    @PostMapping("/wxlogin")
    public AjaxResult wxLogin(@RequestBody WxLoginDTO loginDTO)
    {
        if (loginDTO.getCode() == null || loginDTO.getCode().trim().isEmpty())
        {
            return AjaxResult.error("登录凭证不能为空");
        }
        return wxUserService.wxLogin(loginDTO.getCode());
    }

    /**
     * 获取用户手机号
     */
    @Operation(summary = "获取用户手机号", description = "通过微信授权码获取用户手机号")
    @PostMapping("/getPhoneNumber")
    public AjaxResult getPhoneNumber(@RequestBody WxLoginDTO loginDTO)
    {
        if (loginDTO.getCode() == null || loginDTO.getCode().trim().isEmpty())
        {
            return AjaxResult.error("授权码不能为空");
        }
        return wxUserService.getPhoneNumber(loginDTO.getCode());
    }

    /**
     * 获取用户信息
     */
    @Operation(summary = "获取用户信息", description = "获取当前登录用户的详细信息")
    @PostMapping("/user/info")
    public AjaxResult getUserInfo()
    {
        // 从Token中获取当前登录用户的ID
        Long userId = tokenService.getWxUserId(ServletUtils.getRequest());
        if (userId == null)
        {
            return AjaxResult.error("用户未登录或登录已过期");
        }
        
        return wxUserService.getUserInfo(userId);
    }

    /**
     * 更新用户信息
     */
    @Operation(summary = "更新用户资料", description = "更新用户昵称和头像")
    @PostMapping("/user/update")
    public AjaxResult updateUserProfile(@RequestBody UserProfileDTO profileDTO)
    {
        // 从Token中获取当前登录用户的ID
        Long userId = tokenService.getWxUserId(ServletUtils.getRequest());
        if (userId == null)
        {
            return AjaxResult.error("用户未登录或登录已过期");
        }
        
        return wxUserService.updateProfile(userId, profileDTO);
    }

    /**
     * 绑定手机号
     */
    @Operation(summary = "绑定手机号", description = "为已登录用户绑定手机号")
    @PostMapping("/user/bind-phone")
    public AjaxResult bindPhone(@RequestBody WxLoginDTO loginDTO)
    {
        if (loginDTO.getCode() == null || loginDTO.getCode().trim().isEmpty())
        {
            return AjaxResult.error("授权码不能为空");
        }
        
        // 绑定手机号和获取手机号使用相同的逻辑
        return wxUserService.getPhoneNumber(loginDTO.getCode());
    }

    /**
     * 删除用户账号
     */
    @Operation(summary = "删除用户账号", description = "注销当前登录用户的账号")
    @DeleteMapping("/user/delete")
    public AjaxResult deleteAccount()
    {
        // 从Token中获取当前登录用户的ID
        Long userId = tokenService.getWxUserId(ServletUtils.getRequest());
        if (userId == null)
        {
            return AjaxResult.error("用户未登录或登录已过期");
        }
        
        return wxUserService.deleteAccount(userId);
    }
    
    /**
     * 验证微信配置
     */
    @Operation(summary = "验证微信配置", description = "检查AppID和AppSecret配置是否正确")
    @GetMapping("/wx/validate-config")
    public AjaxResult validateWxConfig()
    {
        try {
            wxConfigValidator.validateConfig();
            return AjaxResult.success("配置验证完成，请查看控制台日志");
        } catch (Exception e) {
            return AjaxResult.error("配置验证失败：" + e.getMessage());
        }
    }

    /**
     * 获取活动表单定义 - 小程序专用（无需权限验证）
     */
    @Operation(summary = "获取活动表单定义", description = "获取指定活动的报名表单配置")
    @GetMapping("/content/form-definition/event/{eventId}")
    public AjaxResult getEventFormDefinition(@PathVariable("eventId") Long eventId)
    {
        try {
            Object formDefinition = wxEventService.getEventFormDefinition(eventId);
            return AjaxResult.success(formDefinition);
        } catch (Exception e) {
            return AjaxResult.error("获取表单定义失败：" + e.getMessage());
        }
    }
} 