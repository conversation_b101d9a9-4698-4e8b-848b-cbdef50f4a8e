package com.hongda.wxapp.service;

import com.hongda.common.core.domain.AjaxResult;
import com.hongda.common.core.domain.dto.PolicyAcceptDTO;

/**
 * 协议服务接口
 *
 * <AUTHOR>
 */
public interface IPolicyService
{
    /**
     * 获取最新协议内容
     * 
     * @param type 协议类型 (user_agreement / privacy_policy)
     * @return 协议内容
     */
    AjaxResult getLatestPolicy(String type);

    /**
     * 获取指定版本协议内容
     * 
     * @param type 协议类型
     * @param version 协议版本
     * @return 协议内容
     */
    AjaxResult getPolicyByVersion(String type, String version);

    /**
     * 记录用户同意协议
     * 
     * @param userId 用户ID
     * @param acceptDTO 同意信息
     * @param ip 用户IP
     * @param userAgent 用户代理
     * @return 记录结果
     */
    AjaxResult acceptPolicy(Long userId, PolicyAcceptDTO acceptDTO, String ip, String userAgent);

    /**
     * 获取用户已同意的协议版本
     * 
     * @param userId 用户ID
     * @param type 协议类型
     * @return 已同意的版本信息
     */
    AjaxResult getUserAcceptedPolicy(Long userId, String type);
}
