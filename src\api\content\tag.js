import request from '@/utils/request'

// 查询资讯分类标签列表
export function listTag(query) {
  return request({
    url: '/content/tag/list',
    method: 'get',
    params: query
  })
}

// 查询资讯分类标签详细
export function getTag(id) {
  return request({
    url: '/content/tag/' + id,
    method: 'get'
  })
}

// 新增资讯分类标签
export function addTag(data) {
  return request({
    url: '/content/tag',
    method: 'post',
    data: data
  })
}

// 修改资讯分类标签
export function updateTag(data) {
  return request({
    url: '/content/tag',
    method: 'put',
    data: data
  })
}

// 删除资讯分类标签
export function delTag(id) {
  return request({
    url: '/content/tag/' + id,
    method: 'delete'
  })
}

// 【新增】查询所有资讯分类标签列表（不分页）
export function listAllTag() {
  return request({
    url: '/content/tag/listAll',
    method: 'get'
  })
}
