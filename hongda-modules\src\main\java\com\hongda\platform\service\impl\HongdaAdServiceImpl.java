package com.hongda.platform.service.impl;

import java.util.List;
import com.hongda.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.hongda.platform.mapper.HongdaAdMapper;
import com.hongda.platform.domain.HongdaAd;
import com.hongda.platform.service.IHongdaAdService;

/**
 * 广告管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Service
public class HongdaAdServiceImpl implements IHongdaAdService 
{
    @Autowired
    private HongdaAdMapper hongdaAdMapper;

    /**
     * 查询广告管理
     * 
     * @param id 广告管理主键
     * @return 广告管理
     */
    @Override
    public HongdaAd selectHongdaAdById(Long id)
    {
        return hongdaAdMapper.selectHongdaAdById(id);
    }

    /**
     * 查询广告管理列表
     * 
     * @param hongdaAd 广告管理
     * @return 广告管理
     */
    @Override
    public List<HongdaAd> selectHongdaAdList(HongdaAd hongdaAd)
    {
        return hongdaAdMapper.selectHongdaAdList(hongdaAd);
    }

    /**
     * 新增广告管理
     * 
     * @param hongdaAd 广告管理
     * @return 结果
     */
    @Override
    public int insertHongdaAd(HongdaAd hongdaAd)
    {
        hongdaAd.setCreateTime(DateUtils.getNowDate());
        return hongdaAdMapper.insertHongdaAd(hongdaAd);
    }

    /**
     * 修改广告管理
     * 
     * @param hongdaAd 广告管理
     * @return 结果
     */
    @Override
    public int updateHongdaAd(HongdaAd hongdaAd)
    {
        hongdaAd.setUpdateTime(DateUtils.getNowDate());
        return hongdaAdMapper.updateHongdaAd(hongdaAd);
    }

    /**
     * 批量删除广告管理
     * 
     * @param ids 需要删除的广告管理主键
     * @return 结果
     */
    @Override
    public int deleteHongdaAdByIds(Long[] ids)
    {
        return hongdaAdMapper.deleteHongdaAdByIds(ids);
    }

    /**
     * 删除广告管理信息
     * 
     * @param id 广告管理主键
     * @return 结果
     */
    @Override
    public int deleteHongdaAdById(Long id)
    {
        return hongdaAdMapper.deleteHongdaAdById(id);
    }


}
