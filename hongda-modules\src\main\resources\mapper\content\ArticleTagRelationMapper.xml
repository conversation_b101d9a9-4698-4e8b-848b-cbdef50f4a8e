<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongda.content.mapper.ArticleTagRelationMapper">

    <!-- 批量插入关联数据 -->
    <insert id="batchArticleTag">
        insert into hongda_article_tag_relation(article_id, tag_id) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.articleId},#{item.tagId})
        </foreach>
    </insert>

    <!-- 根据文章ID删除关联数据 -->
    <delete id="deleteArticleTagByArticleId" parameterType="Long">
        delete from hongda_article_tag_relation where article_id = #{articleId}
    </delete>

    <!-- 【关键】根据文章ID查询关联的标签ID列表 -->
    <select id="selectTagIdsByArticleId" parameterType="Long" resultType="Long">
        select tag_id from hongda_article_tag_relation where article_id = #{articleId}
    </select>

</mapper>