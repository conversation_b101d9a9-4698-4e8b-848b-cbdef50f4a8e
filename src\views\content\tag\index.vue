<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px" class="search-form">
      <el-form-item label="标签名称" prop="name">
        <el-input
            v-model="queryParams.name"
            placeholder="请输入标签名称"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮行 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['content:tag:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['content:tag:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['content:tag:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['content:tag:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="tagList" @selection-change="handleSelectionChange" :height="tableHeight">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="标签ID" align="center" prop="id" width="100" />
      <el-table-column label="标签名称" align="center" prop="name" :show-overflow-tooltip="true" />
      <el-table-column label="显示顺序" align="center" prop="sortOrder" width="120" sortable />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" min-width="150">
        <template #default="scope">
          <el-button
              link
              type="primary"
              icon="Edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['content:tag:edit']"
          >修改</el-button>
          <el-button
              link
              type="danger"
              icon="Delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['content:tag:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <!-- 添加或修改资讯分类标签对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body :close-on-click-modal="false">
      <el-form ref="tagRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="标签名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入标签名称" maxlength="50" show-word-limit />
        </el-form-item>
        <el-form-item label="显示顺序" prop="sortOrder">
          <el-input-number v-model="form.sortOrder" controls-position="right" :min="0" style="width: 100%;" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitting">{{ submitting ? '提交中...' : '确 定' }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Tag">
import { listTag, getTag, delTag, addTag, updateTag } from "@/api/content/tag";
import { ref, reactive, toRefs, onMounted, onUnmounted, getCurrentInstance } from 'vue';

const { proxy } = getCurrentInstance();

// --- 响应式状态定义 ---
const tagList = ref([]);
const open = ref(false);
const loading = ref(true);
const submitting = ref(false);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const tableHeight = ref(window.innerHeight - 220);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: null
  },
  rules: {
    name: [
      { required: true, message: "标签名称不能为空", trigger: "blur" }
    ],
    sortOrder: [
      { required: true, message: "显示顺序不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

// --- 窗口大小监听 ---
const updateTableHeight = () => {
  tableHeight.value = window.innerHeight - 220;
};
onMounted(() => {
  window.addEventListener('resize', updateTableHeight);
});
onUnmounted(() => {
  window.removeEventListener('resize', updateTableHeight);
});

// --- 核心业务方法 ---

/** 查询资讯分类标签列表 */
async function getList() {
  loading.value = true;
  try {
    const response = await listTag(queryParams.value);
    tagList.value = response.rows;
    total.value = response.total;
  } catch (error) {
    proxy.$modal.msgError("获取列表失败");
  } finally {
    loading.value = false;
  }
}

/** 表单重置 */
function reset() {
  form.value = {
    id: null,
    name: null,
    sortOrder: 0 // 【修复】确保重置时有默认值
  };
  proxy.resetForm("tagRef");
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加资讯分类标签";
}

/** 修改按钮操作 */
async function handleUpdate(row) {
  reset();
  const _id = row?.id || ids.value[0];
  try {
    const response = await getTag(_id);
    form.value = response.data;
    open.value = true;
    title.value = "修改资讯分类标签";
  } catch (error) {
    proxy.$modal.msgError("获取标签信息失败");
  }
}

/** 提交按钮 */
async function submitForm() {
  try {
    await proxy.$refs["tagRef"].validate();
    submitting.value = true;
    if (form.value.id != null) {
      await updateTag(form.value);
      proxy.$modal.msgSuccess("修改成功");
    } else {
      await addTag(form.value);
      proxy.$modal.msgSuccess("新增成功");
    }
    open.value = false;
    getList();
  } catch (error) {
    // validate promise rejects on validation error
  } finally {
    submitting.value = false;
  }
}

/** 删除按钮操作 */
async function handleDelete(row) {
  const _ids = row?.id || ids.value;
  const names = row ? row.name : tagList.value.filter(t => ids.value.includes(t.id)).map(t => t.name).join('、');
  try {
    await proxy.$modal.confirm('是否确认删除资讯分类标签"' + names + '"？');
    await delTag(_ids);
    getList();
    proxy.$modal.msgSuccess("删除成功");
  } catch (error) {
    // modal.confirm promise rejects on cancel
  }
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('content/tag/export', {
    ...queryParams.value
  }, `tag_${new Date().getTime()}.xlsx`);
}

// --- 生命周期钩子 ---
onMounted(() => {
  getList();
});
</script>

<style scoped>
/* === 全局样式 === */
.app-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* === 搜索表单样式 === */
.search-form {
  background: white;
  padding: 20px 20px 0;
  border-radius: 8px;
  margin-bottom: 16px;
}

/* === 表格样式优化 === */
.el-table {
  border-radius: 8px;
  overflow: hidden;
  background: white;
}

/* === 分页样式 === */
.pagination-container {
  background: white;
  padding: 16px;
  border-radius: 0 0 8px 8px;
}

/* === 对话框样式 === */
.dialog-footer {
  text-align: right;
}
</style>
