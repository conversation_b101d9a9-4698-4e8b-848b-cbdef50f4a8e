package com.hongda.data.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.hongda.common.annotation.Excel;
import com.hongda.common.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 活动报名记录 (对应"报名数据"管理)对象 hongda_event_registration
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@Schema(description = "活动报名记录")
public class HongdaEventRegistration extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 报名ID, 主键 */
    @Schema(description = "报名ID", example = "1")
    private Long id;

    /** 活动ID */
    @Schema(description = "活动ID", example = "1")
    private Long eventId;

    /** 用户ID */
    @Schema(description = "用户ID", example = "1")
    private Long userId;

    /** 报名时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "报名时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", sort = 4)
    @Schema(description = "报名时间", example = "2025-07-20 14:30:25")
    private Date registrationTime;

    /** 用户提交的自定义表单数据 (JSON格式) */
    @Schema(description = "用户提交的自定义表单数据", example = "{\"name\":\"张三\",\"phone\":\"13800138000\"}")
    private String formData;

    /** 状态 (0=已报名, 1=已取消) */
    @Excel(name = "状态", readConverterExp = "0=已报名,1=已取消", sort = 5)
    @Schema(description = "报名状态", example = "0", allowableValues = {"0", "1"})
    private Integer status;

    /** 关联的用户信息 */
    @Schema(description = "关联的用户信息")
    private HongdaUser hongdaUser;

    /** 活动名称 (用于列表显示，替代活动ID) */
    @Excel(name = "活动名称", width = 30, sort = 1)
    @Schema(description = "活动名称", example = "红大走出去交流会")
    private String eventName;

    /** 用户昵称 (用于列表显示，替代用户ID) */
    @Excel(name = "报名用户", sort = 2)
    @Schema(description = "用户昵称", example = "张三")
    private String userNickname;

    /** 用户联系方式 (用于导出显示) */
    @Excel(name = "联系方式", sort = 3)
    @Schema(description = "用户联系方式", example = "13800138000")
    private String userPhone;

    /** 临时字段：用于新增报名时通过手机号查找用户 */
    @Schema(description = "报名手机号", example = "13800138000")
    private String registrationPhone;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setEventId(Long eventId) 
    {
        this.eventId = eventId;
    }

    public Long getEventId() 
    {
        return eventId;
    }

    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    public void setRegistrationTime(Date registrationTime) 
    {
        this.registrationTime = registrationTime;
    }

    public Date getRegistrationTime() 
    {
        return registrationTime;
    }

    public void setFormData(String formData) 
    {
        this.formData = formData;
    }

    public String getFormData() 
    {
        return formData;
    }

    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }

    public void setHongdaUser(HongdaUser hongdaUser) 
    {
        this.hongdaUser = hongdaUser;
    }

    public HongdaUser getHongdaUser() 
    {
        return hongdaUser;
    }

    public void setEventName(String eventName) 
    {
        this.eventName = eventName;
    }

    public String getEventName() 
    {
        return eventName;
    }

    public void setUserNickname(String userNickname) 
    {
        this.userNickname = userNickname;
    }

    public String getUserNickname() 
    {
        return userNickname;
    }

    public void setUserPhone(String userPhone) 
    {
        this.userPhone = userPhone;
    }

    public String getUserPhone() 
    {
        return userPhone;
    }

    public void setRegistrationPhone(String registrationPhone) 
    {
        this.registrationPhone = registrationPhone;
    }

    public String getRegistrationPhone() 
    {
        return registrationPhone;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("eventId", getEventId())
            .append("userId", getUserId())
            .append("registrationTime", getRegistrationTime())
            .append("formData", getFormData())
            .append("status", getStatus())
            .toString();
    }
}
