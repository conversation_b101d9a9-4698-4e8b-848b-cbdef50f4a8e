package com.hongda.data.domain.vo;
import com.hongda.common.annotation.OssUrl;
import com.hongda.data.domain.HongdaComment;
import java.util.List;

/**
 * 评论管理 视图对象
 */
public class HongdaCommentVo extends HongdaComment {
    private static final long serialVersionUID = 1L;

    // 关联查询出的用户昵称
    private String nickname;

    // 关联查询出的内容标题
    private String relatedTitle;

    // 被回复用户的昵称
    private String replyToNickname;

    // 子评论列表，用于构建树形结构
    private List<HongdaCommentVo> children;

    // 用户头像URL
    @OssUrl
    private String avatarUrl;

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getRelatedTitle() {
        return relatedTitle;
    }

    public void setRelatedTitle(String relatedTitle) {
        this.relatedTitle = relatedTitle;
    }

    public List<HongdaCommentVo> getChildren() {
        return children;
    }

    public void setChildren(List<HongdaCommentVo> children) {
        this.children = children;
    }

    public String getReplyToNickname() {
        return replyToNickname;
    }

    public void setReplyToNickname(String replyToNickname) {
        this.replyToNickname = replyToNickname;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }
}