package com.hongda.data.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.hongda.data.mapper.StatisticMapper;
import com.hongda.data.service.IStatisticService;

/**
 * 数据统计Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-22
 */
@Service
public class StatisticServiceImpl implements IStatisticService 
{
    @Autowired
    private StatisticMapper statisticMapper;

    /**
     * 获取核心指标统计数据
     * 
     * @return 核心指标数据
     */
    @Override
    public Map<String, Object> getSummaryStatistics()
    {
        Map<String, Object> result = new HashMap<>();
        
        // 获取总报名人次
        Long totalRegistrations = statisticMapper.getTotalRegistrations();
        result.put("totalRegistrations", totalRegistrations != null ? totalRegistrations : 0);
        
        // 获取今日新增报名人次
        Long todayNewRegistrations = statisticMapper.getTodayNewRegistrations();
        result.put("todayNewRegistrations", todayNewRegistrations != null ? todayNewRegistrations : 0);
        
        // 获取总用户数
        Long totalUsers = statisticMapper.getTotalUsers();
        result.put("totalUsers", totalUsers != null ? totalUsers : 0);
        
        // 获取正在进行中的活动数
        Long ongoingEvents = statisticMapper.getOngoingEvents();
        result.put("ongoingEvents", ongoingEvents != null ? ongoingEvents : 0);
        
        return result;
    }

    /**
     * 获取热门活动报名排行TOP5
     * 
     * @return 热门活动排行数据
     */
    @Override
    public Map<String, Object> getTopEventsStatistics()
    {
        Map<String, Object> result = new HashMap<>();
        
        List<Map<String, Object>> topEvents = statisticMapper.getTopEvents();
        
        // 提取活动名称和报名数量
        String[] eventNames = new String[topEvents.size()];
        Integer[] registrationCounts = new Integer[topEvents.size()];
        
        for (int i = 0; i < topEvents.size(); i++) {
            Map<String, Object> event = topEvents.get(i);
            eventNames[i] = (String) event.get("title");
            registrationCounts[i] = ((Number) event.get("registration_count")).intValue();
        }
        
        result.put("eventNames", eventNames);
        result.put("registrationCounts", registrationCounts);
        
        return result;
    }

    /**
     * 获取近7日报名趋势
     * 
     * @return 每日报名趋势数据
     */
    @Override
    public Map<String, Object> getDailyTrendStatistics()
    {
        Map<String, Object> result = new HashMap<>();
        
        List<Map<String, Object>> dailyTrend = statisticMapper.getDailyTrend();
        
        // 提取日期和数量
        String[] dates = new String[dailyTrend.size()];
        Integer[] counts = new Integer[dailyTrend.size()];
        
        for (int i = 0; i < dailyTrend.size(); i++) {
            Map<String, Object> day = dailyTrend.get(i);
            dates[i] = (String) day.get("date");
            counts[i] = ((Number) day.get("count")).intValue();
        }
        
        result.put("dates", dates);
        result.put("counts", counts);
        
        return result;
    }
}