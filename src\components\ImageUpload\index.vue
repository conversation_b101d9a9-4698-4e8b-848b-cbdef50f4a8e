<template>
  <div class="component-upload-image">
    <el-upload
        multiple
        :disabled="disabled"
        :action="uploadImgUrl"
        list-type="picture-card"
        :on-success="handleUploadSuccess"
        :before-upload="handleBeforeUpload"
        :data="data"
        :limit="limit"
        :on-error="handleUploadError"
        :on-exceed="handleExceed"
        ref="imageUpload"
        :before-remove="handleDelete"
        :show-file-list="true"
        :headers="headers"
        :file-list="fileList"
        :on-preview="handlePictureCardPreview"
        :class="{ hide: fileList.length >= limit }"
    >
      <el-icon class="avatar-uploader-icon"><plus /></el-icon>
    </el-upload>
    <div class="el-upload__tip" v-if="showTip && !disabled">
      请上传
      <template v-if="fileSize">
        大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b>
      </template>
      <template v-if="fileType">
        格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b>
      </template>
      的文件
    </div>

    <el-dialog
        v-model="dialogVisible"
        title="预览"
        width="800px"
        append-to-body
    >
      <img
          :src="dialogImageUrl"
          style="display: block; max-width: 100%; margin: 0 auto"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { getToken } from "@/utils/auth";
import { isExternal } from "@/utils/validate";
import Sortable from 'sortablejs';
import { getCurrentInstance, ref, computed, watch, nextTick, onMounted } from "vue";

const { proxy } = getCurrentInstance();

const props = defineProps({
  modelValue: [String, Object, Array],
  action: {
    type: String,
    default: "/common/upload"
  },
  data: {
    type: Object
  },
  limit: {
    type: Number,
    default: 5
  },
  fileSize: {
    type: Number,
    default: 25
  },
  fileType: {
    type: Array,
    default: () => ["png", "jpg", "jpeg", "webp"]
  },
  isShowTip: {
    type: Boolean,
    default: true
  },
  disabled: {
    type: Boolean,
    default: false
  },
  drag: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['update:modelValue']);

// --- 响应式状态定义 ---
const number = ref(0);
const dialogImageUrl = ref("");
const dialogVisible = ref(false);
const fileList = ref([]);
const objectNames = ref([]);

// --- 计算属性和常量 ---
const baseUrl = import.meta.env.VITE_APP_BASE_API;
const uploadImgUrl = ref(baseUrl + props.action);
const headers = ref({ Authorization: "Bearer " + getToken() });
const showTip = computed(
    () => props.isShowTip && (props.fileType || props.fileSize)
);

// --- 核心方法 ---

// 从URL中安全地提取objectName
function extractObjectNameFromUrl(url) {
  if (!url || typeof url !== 'string') return null;
  try {
    if (isExternal(url)) {
      const urlObject = new URL(url);
      return urlObject.pathname.substring(1); // 移除开头的'/'
    }
    return url;
  } catch (e) {
    console.warn('解析URL失败，返回原始值:', url, e);
    return url;
  }
}

// 向上帝父组件提交数据
function emitValue() {
  const value = objectNames.value.filter(name => name && name.trim()).join(",");
  emit("update:modelValue", value);
}

// **最终版 Watcher**
watch(() => props.modelValue, (newVal) => {
  const internalVal = objectNames.value.join(',');
  if (newVal === internalVal) {
    return;
  }
  if (newVal && newVal !== "") {
    const list = Array.isArray(newVal) ? newVal : String(newVal).split(",").filter(item => item.trim());
    objectNames.value = list.map(item => extractObjectNameFromUrl(item)).filter(name => name);
    fileList.value = list.map((item, index) => {
      const objectName = extractObjectNameFromUrl(item);
      return {
        uid: Date.now() + index,
        name: objectName || `image_${index}`,
        url: item,
        objectName: objectName,
        status: 'success'
      };
    });

    // 【关键修复】: 初始化或外部数据变更后，立即将解析出的 objectName 更新回父组件
    emitValue();

  } else {
    objectNames.value = [];
    fileList.value = [];
  }
}, { deep: true, immediate: true });


// 上传成功回调
function handleUploadSuccess(res, file) {
  if (res.code === 200) {
    file.url = res.url;
    file.objectName = res.objectName;
    objectNames.value.push(res.objectName);
    uploadedSuccessfully();
  } else {
    number.value--;
    proxy.$modal.closeLoading();
    proxy.$modal.msgError(res.msg);
    proxy.$refs.imageUpload.handleRemove(file);
    uploadedSuccessfully();
  }
}

// 删除图片回调
function handleDelete(file) {
  const objectNameToDelete = file.objectName;
  const fileUidToDelete = file.uid;

  if (objectNameToDelete) {
    const objectIndex = objectNames.value.indexOf(objectNameToDelete);
    if (objectIndex > -1) {
      objectNames.value.splice(objectIndex, 1);
    }
  }

  const fileIndex = fileList.value.findIndex(f => f.uid === fileUidToDelete);
  if (fileIndex > -1) {
    fileList.value.splice(fileIndex, 1);
  }

  emitValue();
  return true;
}

// --- 辅助与生命周期方法 ---

function uploadedSuccessfully() {
  if (number.value > 0) {
    number.value--;
  }
  if (number.value === 0) {
    proxy.$modal.closeLoading();
    emitValue();
  }
}

function handleBeforeUpload(file) {
  let isImg = false;
  if (props.fileType.length) {
    let fileExtension = "";
    if (file.name.lastIndexOf(".") > -1) {
      fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
    }
    isImg = props.fileType.some(type => {
      if (file.type.indexOf(type) > -1) return true;
      if (fileExtension && fileExtension.indexOf(type) > -1) return true;
      return false;
    });
  } else {
    isImg = file.type.indexOf("image") > -1;
  }

  if (!isImg) {
    proxy.$modal.msgError(`文件格式不正确，请上传${props.fileType.join("/")}图片格式文件!`);
    return false;
  }

  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 < props.fileSize;
    if (!isLt) {
      proxy.$modal.msgError(`上传头像图片大小不能超过 ${props.fileSize} MB!`);
      return false;
    }
  }

  proxy.$modal.loading("正在上传图片，请稍候...");
  number.value++;
  return true;
}

function handleExceed() {
  proxy.$modal.msgError(`上传文件数量不能超过 ${props.limit} 个!`);
}

function handleUploadError() {
  proxy.$modal.msgError("上传图片失败");
  proxy.$modal.closeLoading();
}

function handlePictureCardPreview(file) {
  dialogImageUrl.value = file.url;
  dialogVisible.value = true;
}

onMounted(() => {
  if (props.drag && !props.disabled) {
    nextTick(() => {
      const element = proxy.$refs.imageUpload?.$el?.querySelector('.el-upload-list');
      if (element) {
        Sortable.create(element, {
          onEnd: (evt) => {
            const movedItem = objectNames.value.splice(evt.oldIndex, 1)[0];
            objectNames.value.splice(evt.newIndex, 0, movedItem);
            const movedFile = fileList.value.splice(evt.oldIndex, 1)[0];
            fileList.value.splice(evt.newIndex, 0, movedFile);
            emitValue();
          }
        });
      }
    });
  }
});
</script>

<style scoped lang="scss">
:deep(.hide .el-upload--picture-card) {
  display: none;
}
:deep(.el-upload.el-upload--picture-card.is-disabled) {
  display: none !important;
}
</style>