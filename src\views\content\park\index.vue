<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px" class="search-form">
      <el-form-item label="园区名称" prop="name">
        <el-input
            v-model="queryParams.name"
            placeholder="请输入园区名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属国家" prop="countryId">
        <el-select
            v-model="queryParams.countryId"
            placeholder="请选择所属国家"
            clearable
            filterable
            style="width: 200px"
        >
          <el-option
              v-for="country in countryOptions"
              :key="country.id"
              :label="country.nameCn"
              :value="country.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 200px">
          <el-option
              v-for="dict in sys_show_hide"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮行 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['content:park:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate" v-hasPermi="['content:park:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete" v-hasPermi="['content:park:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['content:park:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="parkList" @selection-change="handleSelectionChange" :height="tableHeight">
      <el-table-column type="selection" width="55" align="center" fixed />
      <el-table-column label="显示顺序" align="center" prop="sortOrder" width="100" sortable />
      <el-table-column label="园区信息" align="center" min-width="250">
        <template #default="scope">
          <div class="info-cell">
            <image-preview :src="scope.row.coverImageUrl" :width="80" :height="50" class="cover-image"/>
            <div class="info-text">
              <div class="name">{{ scope.row.name }}</div>
              <div class="sub-text">{{ scope.row.location }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="所属国家" align="center" prop="countryName" min-width="150" />
      <el-table-column label="主要产业" align="center" prop="mainIndustries" min-width="200" :show-overflow-tooltip="true" />
      <el-table-column label="园区详情介绍" align="center" min-width="280">
        <template #default="scope">
          <div class="details-preview-container">
            <div class="preview-item">
              <span class="preview-label">园区简介:</span>
              <div v-html="generateSmartPreview(scope.row.summary)"></div>
            </div>
            <div class="preview-item">
              <span class="preview-label">详细介绍:</span>
              <div v-html="generateSmartPreview(scope.row.content)"></div>
            </div>
            <el-button link type="primary" @click="handleViewDetails(scope.row)" style="margin-top: 8px;">查看全部详情</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template #default="scope">
          <dict-tag :options="sys_show_hide" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180" fixed="right">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleViewDetails(scope.row)">预览</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['content:park:edit']">修改</el-button>
          <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['content:park:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination v-show="total>0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList"/>

    <!-- 新增/修改弹窗 -->
    <el-dialog :title="title" v-model="open" width="80%" top="5vh" append-to-body :close-on-click-modal="false">
      <el-form ref="parkRef" :model="form" :rules="rules" label-width="100px">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="核心信息" name="core">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="园区名称" prop="name">
                  <el-input v-model="form.name" placeholder="请输入园区名称" maxlength="100" show-word-limit />
                </el-form-item>
              </el-col>
              <el-form-item label="所属国家" prop="countryId">
                <el-select
                    v-model="form.countryId"
                    placeholder="请选择所属国家"
                    filterable
                    style="width: 100%;"
                >
                  <el-option
                      v-for="item in countryOptions"
                      :key="item.id"
                      :label="item.nameCn"
                      :value="item.id"
                  />
                </el-select>
              </el-form-item>
              <el-col :span="12">
                <el-form-item label="地理位置" prop="location">
                  <el-input v-model="form.location" placeholder="例如：新加坡西部裕廊区" maxlength="100" show-word-limit />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="状态" prop="status">
                  <el-radio-group v-model="form.status">
                    <el-radio v-for="dict in sys_show_hide" :key="dict.value" :label="dict.value">{{ dict.label }}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="主要产业" prop="mainIndustries">
                  <el-input v-model="form.mainIndustries" type="textarea" :rows="2" placeholder="请输入主要产业，用逗号分隔" maxlength="200" show-word-limit />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="园区封面图" prop="coverImageUrl">
                  <image-upload v-model="form.coverImageUrl" :limit="1"/>
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="详细内容" name="details">
            <el-form-item label="园区简介" prop="summary">
              <editor v-model="form.summary" :min-height="200"/>
            </el-form-item>
            <el-form-item label="详细介绍" prop="content">
              <editor v-model="form.content" :min-height="200"/>
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitting">{{ submitting ? '提交中...' : '确 定' }}</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 详情查看弹窗 -->
    <el-dialog :title="detailViewTitle" v-model="detailViewVisible" width="85%" top="5vh" append-to-body class="rich-text-dialog">
      <el-tabs v-model="activeContentField" tab-position="left" style="min-height: 60vh;">
        <el-tab-pane v-for="field in richTextFields" :key="field.name" :label="field.label" :name="field.name">
          <el-tabs v-model="activeFormatTab">
            <el-tab-pane label="富文本预览" name="preview">
              <div class="rich-text-content" v-html="sanitizedPreviewContent"></div>
            </el-tab-pane>
            <el-tab-pane label="源码查看" name="source">
              <pre class="source-code-pre"><code class="language-html" v-html="highlightedSourceHtml"></code></pre>
            </el-tab-pane>
            <el-tab-pane label="纯文本" name="text">
              <div class="plain-text-content">{{ plainTextContent }}</div>
            </el-tab-pane>
          </el-tabs>
        </el-tab-pane>
      </el-tabs>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="copyContent" type="primary" icon="CopyDocument">复制内容</el-button>
          <el-button @click="detailViewVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Park">
import { listPark, getPark, delPark, addPark, updatePark } from "@/api/content/park";
import { listAllCountry } from "@/api/content/country";
import { ref, reactive, toRefs, onMounted, computed, watch, nextTick, getCurrentInstance, onUnmounted } from 'vue';
import he from 'he';
import DOMPurify from 'dompurify';
import hljs from 'highlight.js/lib/core';
import xml from 'highlight.js/lib/languages/xml';
import 'highlight.js/styles/atom-one-dark.css';

hljs.registerLanguage('html', xml);

const { proxy } = getCurrentInstance();
const { sys_show_hide } = proxy.useDict('sys_show_hide');

// --- 响应式状态定义 ---
const parkList = ref([]);
const countryOptions = ref([]);
const countryMap = ref(new Map());
const open = ref(false);
const loading = ref(true);
const submitting = ref(false);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const activeTab = ref("core");
const tableHeight = ref(window.innerHeight - 260);
const countrySearchLoading = ref(false);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: null,
    countryId: null,
    status: null,
  },
  rules: {
    name: [{ required: true, message: "园区名称不能为空", trigger: "blur" }],
    countryId: [{ required: true, message: "所属国家不能为空", trigger: "change" }],
    status: [{ required: true, message: "状态不能为空", trigger: "change" }],
  }
});

const { queryParams, form, rules } = toRefs(data);

// --- 窗口大小监听 ---
const updateTableHeight = () => {
  tableHeight.value = window.innerHeight - 260;
};
onMounted(() => {
  window.addEventListener('resize', updateTableHeight);
});
onUnmounted(() => {
  window.removeEventListener('resize', updateTableHeight);
});

// --- 核心业务方法 ---

/** 加载所有国家用于筛选和映射 */
async function loadAllCountries() {
  try {
    // 【核心修复点】listAllCountry返回的是AjaxResult对象
    const response = await listAllCountry();

    // 我们需要的是 response 对象中的 data 属性，它才是真正的国家数组
    countryOptions.value = response.data;

    // 使用正确的数据来构建Map
    countryMap.value = new Map(response.data.map(c => [c.id, c.nameCn]));
  } catch (error) {
    proxy.$modal.msgError("获取国家列表失败");
    console.error("加载国家列表时出错:", error); // 打印更详细的错误
  }
}

/** 查询园区列表 */
async function getList() {
  loading.value = true;
  try {
    const response = await listPark(queryParams.value);
    response.rows.forEach(park => {
      park.countryName = countryMap.value.get(park.countryId) || '未知国家';
    });
    parkList.value = response.rows;
    total.value = response.total;
  } catch (error) {
    proxy.$modal.msgError("获取园区列表失败");
  } finally {
    loading.value = false;
  }
}

/** 表单重置 */
function reset() {
  form.value = {
    id: null,
    countryId: null,
    name: null,
    location: null,
    mainIndustries: null,
    summary: '',
    coverImageUrl: null,
    content: '',
    status: "0",
    sortOrder: 0,
  };
  proxy.resetForm("parkRef");
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  activeTab.value = "core";
  open.value = true;
  title.value = "添加园区信息";
}

/** 修改按钮操作 */
async function handleUpdate(row) {
  reset();
  activeTab.value = "core";
  const _id = row?.id || ids.value[0];
  try {
    const response = await getPark(_id);
    form.value = response.data;
    // 如果当前国家选项里没有这个国家，远程搜索一下加载进来
    if (!countryOptions.value.some(c => c.id === form.value.countryId)) {
      await searchCountries(response.data.countryName || '');
    }
    open.value = true;
    title.value = "修改园区信息";
  } catch (error) {
    proxy.$modal.msgError("获取园区详情失败");
  }
}

/** 远程搜索国家 */
/** 远程搜索国家列表 */
async function searchCountries(query) {
  if (query) {
    countrySearchLoading.value = true;
    try {
      // 调用国别模块的API，根据输入的query进行模糊查询
      const response = await listAllCountry({ nameCn: query });

      // 【【【 核心修复点 】】】
      // 正确地从返回的 AjaxResult 对象中提取 data 属性（即国家数组）
      countryOptions.value = response.data;

    } catch (error) {
      countryOptions.value = [];
      console.error("搜索国家失败:", error);
    } finally {
      countrySearchLoading.value = false;
    }
  } else {
    // 如果查询为空，则清空选项
    countryOptions.value = [];
  }
}

/** 提交按钮 */
async function submitForm() {
  try {
    await proxy.$refs["parkRef"].validate();
    submitting.value = true;
    if (form.value.id != null) {
      await updatePark(form.value);
      proxy.$modal.msgSuccess("修改成功");
    } else {
      await addPark(form.value);
      proxy.$modal.msgSuccess("新增成功");
    }
    open.value = false;
    getList();
  } catch (error) {
    // validate promise rejects on validation error, no need for extra msg
  } finally {
    submitting.value = false;
  }
}

/** 删除按钮操作 */
async function handleDelete(row) {
  const _ids = row?.id || ids.value;
  const names = row ? row.name : parkList.value.filter(item => ids.value.includes(item.id)).map(item => item.name).join('、');
  try {
    await proxy.$modal.confirm(`是否确认删除园区"${names}"？`);
    await delPark(_ids);
    getList();
    proxy.$modal.msgSuccess("删除成功");
  } catch (error) {
    // modal.confirm promise rejects on cancel, no need for extra msg
  }
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('content/park/export', { ...queryParams.value }, `park_${new Date().getTime()}.xlsx`);
}

// --- 详情预览相关 ---
const detailViewVisible = ref(false);
const detailViewTitle = ref('');
const selectedPark = ref(null);
const activeContentField = ref('summary');
const activeFormatTab = ref('preview');
const highlightedSourceHtml = ref('');

const richTextFields = [
  { name: 'summary', label: '园区简介' },
  { name: 'content', label: '详细介绍' },
];

const rawContentForView = computed(() => {
  if (!selectedPark.value) return '';
  return selectedPark.value[activeContentField.value] || '';
});

const decodedContent = computed(() => he.decode(rawContentForView.value));

const sanitizedPreviewContent = computed(() => DOMPurify.sanitize(decodedContent.value, { USE_PROFILES: { html: true } }));

const plainTextContent = computed(() => {
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = decodedContent.value;
  return tempDiv.textContent || '';
});

watch([activeFormatTab, decodedContent], ([tab, content]) => {
  if (tab === 'source' && content) {
    nextTick(() => {
      try {
        highlightedSourceHtml.value = hljs.highlight(content, { language: 'html' }).value;
      } catch (e) {
        highlightedSourceHtml.value = content;
      }
    });
  }
}, { immediate: true });

watch(activeContentField, () => activeFormatTab.value = 'preview');

async function copyContent() {
  try {
    await navigator.clipboard.writeText(plainTextContent.value);
    proxy.$modal.msgSuccess('内容已复制到剪贴板');
  } catch (err) {
    proxy.$modal.msgError('复制失败');
  }
}

function handleViewDetails(row) {
  selectedPark.value = row;
  detailViewTitle.value = `查看详情: ${row.name}`;
  activeContentField.value = 'summary';
  detailViewVisible.value = true;
}

function generateSmartPreview(html = '') {
  if (!html) return '<span class="preview-text-empty">（无）</span>';
  const plainText = he.decode(html.replace(/<[^>]+>/g, ''));
  const truncatedText = plainText.substring(0, 30) + (plainText.length > 30 ? '...' : '');
  return `<span class="preview-text-content">${truncatedText || '（空）'}</span>`;
}

// --- 生命周期钩子 ---
onMounted(async () => {
  await loadAllCountries();
  getList();
});
</script>

<style scoped>
/* === 全局与布局 === */
.app-container { padding: 20px; background-color: #f5f5f5; }
.search-form { background: white; padding: 20px 20px 0; border-radius: 8px; margin-bottom: 16px; }
.el-table { border-radius: 8px; overflow: hidden; }

/* === 表格单元格 === */
.info-cell { display: flex; align-items: center; gap: 12px; padding: 6px 0; }
.cover-image { flex-shrink: 0; border-radius: 4px; border: 1px solid #e4e7ed; }
.info-text { flex: 1; min-width: 0; }
.name { font-weight: 600; color: #303133; }
.sub-text { font-size: 12px; color: #909399; margin-top: 2px; }

/* === 详情预览列 === */
.details-preview-container { text-align: left; font-size: 13px; }
.preview-item { display: flex; align-items: flex-start; margin-bottom: 4px; }
.preview-label { font-weight: bold; margin-right: 5px; white-space: nowrap; color: #333; }
.preview-text-content, .preview-text-empty { color: #666; }
.preview-text-empty { font-style: italic; }

/* === 弹窗 === */
.dialog-footer { text-align: right; padding-top: 20px; }
:deep(.el-dialog__body) { padding: 20px 30px; }

/* === 动态表单项 === */
.dynamic-item { display: flex; align-items: center; gap: 12px; margin-bottom: 12px; }
.dynamic-item-key { width: 150px; flex-shrink: 0; }
.dynamic-item-value { flex: 1; }

/* === 详情查看弹窗 === */
.rich-text-dialog .el-dialog__body { padding: 10px 20px; }
.rich-text-content { height: 55vh; overflow-y: auto; padding: 20px; background-color: #fdfdfd; border: 1px solid #e4e7ed; border-radius: 4px; line-height: 1.6; }
.source-code-pre { height: 55vh; overflow-y: auto; background: #282c34; padding: 1.5em; margin: 0; border-radius: 4px; }
.language-html { white-space: pre-wrap; word-break: break-all; color: #abb2bf; }
.plain-text-content { height: 55vh; overflow-y: auto; padding: 20px; background-color: #fafafa; border: 1px solid #e4e7ed; border-radius: 4px; line-height: 1.6; white-space: pre-wrap; word-wrap: break-word; }
/* === 【核心修复】富文本内容样式增强 === */
/* 使用 :deep() 选择器穿透scoped限制，为v-html渲染的内容注入样式 */
:deep(.rich-text-content) {
  /* -- 基础排版 -- */
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
  font-size: 14px;
  line-height: 1.7;
  color: #303133; /* 提升正文颜色对比度 */
}

:deep(.rich-text-content p) {
  margin-bottom: 1em; /* 为段落添加下间距 */
}

:deep(.rich-text-content strong),
:deep(.rich-text-content b) {
  font-weight: 600; /* 显著增强加粗效果 */
  color: #000;
}

:deep(.rich-text-content a) {
  color: #409EFF;
  text-decoration: none;
  font-weight: 500;
}
:deep(.rich-text-content a:hover) {
  text-decoration: underline;
}

/* -- 标题样式 -- */
:deep(.rich-text-content h1),
:deep(.rich-text-content h2),
:deep(.rich-text-content h3),
:deep(.rich-text-content h4),
:deep(.rich-text-content h5),
:deep(.rich-text-content h6) {
  font-weight: 600;
  color: #303133;
  margin-top: 1.5em;
  margin-bottom: 0.8em;
  line-height: 1.4;
}
:deep(.rich-text-content h1) { font-size: 2em; }
:deep(.rich-text-content h2) { font-size: 1.5em; }
:deep(.rich-text-content h3) { font-size: 1.25em; }

/* -- 列表样式 -- */
:deep(.rich-text-content ul),
:deep(.rich-text-content ol) {
  padding-left: 2em;
  margin-bottom: 1em;
}

/* -- 表格样式 -- */
:deep(.rich-text-content table) {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5em 0;
  border: 1px solid #e4e7ed;
}
:deep(.rich-text-content th),
:deep(.rich-text-content td) {
  border: 1px solid #e4e7ed;
  padding: 10px 12px;
  text-align: left;
}
:deep(.rich-text-content th) {
  background-color: #f5f7fa;
  font-weight: 600;
}
</style>
