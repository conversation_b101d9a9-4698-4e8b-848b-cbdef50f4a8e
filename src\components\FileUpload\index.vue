<template>
  <div class="upload-file">
    <el-upload
        multiple
        :action="uploadFileUrl"
        :before-upload="handleBeforeUpload"
        :file-list="fileList"
        :data="data"
        :limit="limit"
        :on-error="handleUploadError"
        :on-exceed="handleExceed"
        :on-success="handleUploadSuccess"
        :show-file-list="false"
        :headers="headers"
        class="upload-file-uploader"
        ref="fileUpload"
        v-if="!disabled"
    >
      <!-- 上传按钮 -->
      <el-button type="primary">选取文件</el-button>
    </el-upload>
    <!-- 上传提示 -->
    <div class="el-upload__tip" v-if="showTip && !disabled">
      请上传
      <template v-if="fileSize"> 大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b> </template>
      <template v-if="fileType"> 格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b> </template>
      的文件
    </div>
    <!-- 文件列表 -->
    <transition-group ref="uploadFileList" class="upload-file-list el-upload-list el-upload-list--text" name="el-fade-in-linear" tag="ul">
      <li :key="file.uid" class="el-upload-list__item ele-upload-list__item-content" v-for="(file, index) in fileList">
        <el-link :href="file.url" :underline="false" target="_blank">
          <span class="el-icon-document"> {{ file.name }} </span>
        </el-link>
        <div class="ele-upload-list__item-content-action">
          <el-link :underline="false" @click="handleDelete(index)" type="danger" v-if="!disabled">&nbsp;删除</el-link>
        </div>
      </li>
    </transition-group>
  </div>
</template>

<script setup>
import { getToken } from "@/utils/auth";

const props = defineProps({
  modelValue: [String, Object, Array],
  action: {
    type: String,
    default: "/common/upload"
  },
  data: {
    type: Object
  },
  limit: {
    type: Number,
    default: 5
  },
  fileSize: {
    type: Number,
    default: 5
  },
  // 【关键修改】在这里添加 'woff2'
  fileType: {
    type: Array,
    default: () => ["doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt", "pdf", "jpg", "png", "woff2"]
  },
  isShowTip: {
    type: Boolean,
    default: true
  },
  disabled: {
    type: Boolean,
    default: false
  },
  drag: {
    type: Boolean,
    default: true
  }
});

const { proxy } = getCurrentInstance();
const emit = defineEmits(["update:modelValue"]);

// --- 状态定义 ---
const number = ref(0);
const uploadFileUrl = ref(import.meta.env.VITE_APP_BASE_API + props.action);
const headers = ref({ Authorization: "Bearer " + getToken() });
const fileList = ref([]); // 用于视图渲染
const objectNames = ref([]); // 用于存储 objectName，作为组件的实际值

const showTip = computed(
    () => props.isShowTip && (props.fileType || props.fileSize)
);

// --- 核心方法 ---

// 从完整的签名URL中提取文件名
function extractFileNameFromUrl(url) {
  if (!url) return '未知文件';
  try {
    const mainUrl = url.split('?')[0];
    const segments = mainUrl.split('/');
    return segments[segments.length - 1];
  } catch (e) {
    return url;
  }
}

// 向父组件提交数据
function emitValue() {
  const value = objectNames.value.join(',');
  emit("update:modelValue", value);
}

// 智能 Watcher，用于回显
watch(() => props.modelValue, (newVal) => {
  const internalVal = objectNames.value.join(',');
  if (newVal === internalVal) {
    return;
  }

  if (newVal) {
    const list = Array.isArray(newVal) ? newVal : String(newVal).split(',');
    fileList.value = list.map((url, index) => {
      if (!url) return null;
      const objectName = new URL(url).pathname.substring(1);
      return {
        uid: Date.now() + index,
        name: extractFileNameFromUrl(url),
        url: url,
        objectName: objectName,
      };
    }).filter(Boolean);

    objectNames.value = fileList.value.map(f => f.objectName);

  } else {
    fileList.value = [];
    objectNames.value = [];
  }
}, { deep: true, immediate: true });


// 上传成功回调
function handleUploadSuccess(res, uploadFile) {
  if (res.code === 200) {
    const newFile = {
      uid: uploadFile.uid,
      name: res.originalFilename,
      url: res.url,
      objectName: res.objectName
    };
    fileList.value.push(newFile);
    objectNames.value.push(res.objectName);
    uploadedSuccessfully();
  } else {
    number.value--;
    proxy.$modal.closeLoading();
    proxy.$modal.msgError(res.msg);
    proxy.$refs.fileUpload.handleRemove(uploadFile);
    uploadedSuccessfully();
  }
}

// 删除文件
function handleDelete(index) {
  fileList.value.splice(index, 1);
  objectNames.value.splice(index, 1);
  emitValue();
}

// 上传结束统一处理
function uploadedSuccessfully() {
  if (number.value > 0) {
    number.value--;
  }
  if (number.value === 0) {
    emitValue();
    proxy.$modal.closeLoading();
  }
}

// --- 若依原有辅助方法 ---

function handleBeforeUpload(file) {
  if (props.fileType.length) {
    const fileExt = file.name.substring(file.name.lastIndexOf('.') + 1);
    const isTypeOk = props.fileType.includes(fileExt.toLowerCase());
    if (!isTypeOk) {
      proxy.$modal.msgError(`文件格式不正确，请上传${props.fileType.join("/")}格式文件!`);
      return false;
    }
  }
  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 < props.fileSize;
    if (!isLt) {
      proxy.$modal.msgError(`上传文件大小不能超过 ${props.fileSize} MB!`);
      return false;
    }
  }
  proxy.$modal.loading("正在上传文件，请稍候...");
  number.value++;
  return true;
}

function handleExceed() {
  proxy.$modal.msgError(`上传文件数量不能超过 ${props.limit} 个!`);
}

function handleUploadError(err) {
  proxy.$modal.msgError("上传文件失败");
  proxy.$modal.closeLoading();
}
</script>

<style scoped lang="scss">
.upload-file-uploader {
  margin-bottom: 5px;
}
.upload-file-list .el-upload-list__item {
  border: 1px solid #e4e7ed;
  line-height: 2;
  margin-bottom: 10px;
  position: relative;
  padding: 0 10px;
}
.upload-file-list .ele-upload-list__item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: inherit;
}
.ele-upload-list__item-content-action .el-link {
  margin-left: 15px;
}
</style>
