package com.hongda.wxapp.domain.vo;

import com.hongda.common.annotation.OssUrl;
import lombok.Data;

import java.util.List;

/**
 * 国别详情页聚合视图对象
 */
@Data
public class CountryDetailVO {

    /** 国家ID */
    private Long id;

    /** 中文名称 */
    private String nameCn;

    /** 英文名称 */
    private String nameEn;

    /** 详情页顶部大图URL */
    @OssUrl
    private String detailsCoverUrl;

    /** 一句话简介 */
    private String summary;

    /** 国家简介 (富文本) */
    private String introduction;

    /** 招商政策 (富文本) */
    private String investmentPolicy;

    /** 海关政策 (富文本) */
    private String customsPolicy;

    /** 税务政策 (富文本) */
    private String taxPolicy;

    /** 劳工政策 (富文本) */
    private String laborPolicy;

    /** 基本信息 (可以是String或解析后的JSON对象) */
    private Object basicInfoJson;

    /** 聚合的工业园区列表 */
    private List<IndustrialParkVO> industrialParks;
}