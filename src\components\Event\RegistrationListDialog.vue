<template>
  <el-dialog
    :title="dialogTitle"
    :model-value="open"
    @update:model-value="$emit('update:open', $event)"
    width="1200px"
    append-to-body
  >
    <!-- 搜索区域 -->
    <RegistrationSearchForm
      :queryParams="queryParams"
      @search="handleSearch"
      @reset="handleReset"
      @update:queryParams="$emit('update:queryParams', $event)"
    />

    <!-- 操作按钮行 -->
    <RegistrationActionButtons
      :single="single"
      :multiple="multiple"
      @add="handleAdd"
      @edit="handleEdit"
      @delete="handleDelete"
      @export="handleExport"
    />

    <!-- 报名列表表格 -->
    <RegistrationTable
      :loading="loading"
      :registrationList="registrationList"
      :dynamicTableHeaders="dynamicTableHeaders"
      @selection-change="handleSelectionChange"
      @edit="handleEdit"
      @delete="handleDelete"
    />
    
    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="handleSearch"
    /> 
  </el-dialog>
</template>

<script setup>
import { ref, computed } from 'vue'
import RegistrationSearchForm from './RegistrationSearchForm.vue'
import RegistrationActionButtons from './RegistrationActionButtons.vue'
import RegistrationTable from './RegistrationTable.vue'

// Props
const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  dialogTitle: {
    type: String,
    default: ''
  },
  loading: {
    type: Boolean,
    default: false
  },
  registrationList: {
    type: Array,
    default: () => []
  },
  total: {
    type: Number,
    default: 0
  },
  queryParams: {
    type: Object,
    required: true
  },
  currentFormDefinition: {
    type: Array,
    default: () => []
  },
  selectedIds: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits([
  'update:open',
  'update:queryParams',
  'search',
  'reset',
  'add',
  'edit',
  'delete',
  'export',
  'selection-change'
])

// Computed
const single = computed(() => props.selectedIds.length !== 1)
const multiple = computed(() => props.selectedIds.length === 0)

const dynamicTableHeaders = computed(() => {
  if (!props.currentFormDefinition || !Array.isArray(props.currentFormDefinition)) {
    return [];
  }
  return props.currentFormDefinition.map(rule => ({
    prop: rule.field,
    label: rule.title
  }));
})

// Methods
const handleSearch = () => {
  emit('search')
}

const handleReset = () => {
  emit('reset')
}

const handleAdd = () => {
  emit('add')
}

const handleEdit = (row = null) => {
  emit('edit', row)
}

const handleDelete = (row = null) => {
  emit('delete', row)
}

const handleExport = () => {
  emit('export')
}

const handleSelectionChange = (selection) => {
  emit('selection-change', selection)
}
</script>
