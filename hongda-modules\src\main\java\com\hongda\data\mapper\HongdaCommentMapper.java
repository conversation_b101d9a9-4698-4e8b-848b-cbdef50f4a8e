package com.hongda.data.mapper;

import com.hongda.data.domain.HongdaComment;
import com.hongda.data.domain.vo.HongdaCommentVo;

import java.util.List;

/**
 * 评论管理Mapper接口
 * * <AUTHOR>
 * @date 2025-07-28
 */
public interface HongdaCommentMapper
{
    /**
     * 查询评论管理
     * @param id 评论管理主键
     * @return 评论管理
     */
    public HongdaCommentVo selectHongdaCommentById(Long id);

    /**
     * 查询评论管理列表
     * @param hongdaComment 评论管理
     * @return 评论管理集合
     */
    public List<HongdaCommentVo> selectHongdaCommentList(HongdaComment hongdaComment);

    /**
     * 新增评论管理
     * @param hongdaComment 评论管理
     * @return 结果
     */
    public int insertHongdaComment(HongdaComment hongdaComment);

    /**
     * 修改评论管理
     * @param hongdaComment 评论管理
     * @return 结果
     */
    public int updateHongdaComment(HongdaComment hongdaComment);

    /**
     * 删除评论管理
     * @param id 评论管理主键
     * @return 结果
     */
    public int deleteHongdaCommentById(Long id);

    /**
     * 批量删除评论管理
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHongdaCommentByIds(Long[] ids);

    // 已删除所有与点赞相关的方法
}