/* ========================================================== */
/* == 根据您小程序 tagStyle 对象生成的最终、最准确的样式 == */
/* ========================================================== */

/* 全局字体与段落 */
.mp-html p {
    line-height: 1.8;
    margin: 1.2em 0;
    color: #2c3e50;
}

/* 各级标题 */
.mp-html ._h1 {
    font-size: 1.8em;
    font-weight: bold;
    margin: 1.5em 0 1em 0;
    color: #2c3e50;
    border-bottom: 3px solid #6c757d;
    padding-bottom: 0.5em;
}

.mp-html ._h2 {
    font-size: 1.5em;
    font-weight: bold;
    margin: 1.4em 0 1em 0;
    color: #2c3e50;
    border-bottom: 2px solid #6c757d;
    padding-bottom: 0.3em;
}

.mp-html ._h3 {
    font-size: 1.25em;
    font-weight: bold;
    margin: 1.3em 0 0.8em 0;
    color: #2c3e50;
    border-left: 4px solid #6c757d;
    padding-left: 15px;
}

/* 引用块 */
.mp-html blockquote {
    border-left: 4px solid #6c757d;
    padding: 15px 20px;
    margin: 20px 0;
    background: #f8f9fa;
    border-radius: 8px;
    color: #495057;
    font-style: italic;
    position: relative;
}

/* 代码块 */
.mp-html pre {
    background: #2d3748;
    color: #e2e8f0;
    padding: 20px;
    margin: 20px 0;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    font-family: "Fira Code", monospace;
    position: relative;
    overflow-x: auto;
}

.mp-html code {
    font-family: "Fira Code", monospace;
    background: #f7fafc;
    color: #e53e3e;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.9em;
    border: 1px solid #e2e8f0;
}

/* 列表 */
.mp-html ._ul {
    padding-left: 30px;
    margin: 15px 0;
}

.mp-html ._ol {
    padding-left: 30px;
    margin: 15px 0;
}

.mp-html ._li {
    margin-bottom: 8px;
    line-height: 1.6;
    color: #2c3e50;
}

/* 表格 */
.mp-html table {
    width: 100% !important;
    border-collapse: collapse;
    margin: 20px 0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    display: table; /* 确保 display 生效 */
}

.mp-html ._th {
    background: #f8f9fa;
    color: #495057;
    padding: 12px 15px;
    text-align: left;
    font-weight: bold;
    border: 1px solid #dee2e6;
}

.mp-html ._td {
    border: 1px solid #dee2e6;
    padding: 12px 15px;
    background: #fff;
    color: #2c3e50;
}

/* 图片 */
.mp-html ._img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    display: block;
    margin: 20px auto;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}


/* ================================================= */
/* ===== 以下是来自 node.vue 的基础结构样式 ===== */
/* ================================================= */

._a {
    padding: 1.5px 0 1.5px 0;
    color: #366092;
    word-break: break-all;
}
._hover {
    text-decoration: underline;
    opacity: 0.7;
}
._block {
    display: block;
}
._b, ._strong {
    font-weight: bold;
}
._del {
    text-decoration: line-through;
}
._em, ._i {
    font-style: italic;
}
._h5 {
    font-size: 0.83em;
}
._h6 {
    font-size: 0.67em;
}
._ins {
    text-decoration: underline;
}
._li {
    display: list-item;
}
._ol {
    list-style-type: decimal;
}
._q::before {
    content: '"';
}
._q::after {
    content: '"';
}
._sub {
    font-size: smaller;
    vertical-align: sub;
}
._sup {
    font-size: smaller;
    vertical-align: super;
}
._thead, ._tbody, ._tfoot {
    display: table-row-group;
}
._tr {
    display: table-row;
}
._td, ._th {
    display: table-cell;
    vertical-align: middle;
}
._ul {
    list-style-type: disc;
}
._ul ._ul {
    margin: 0;
    list-style-type: circle;
}
._ul ._ul ._ul {
    list-style-type: square;
}
._abbr, ._b, ._code, ._del, ._em, ._i, ._ins, ._label, ._q, ._span, ._strong, ._sub, ._sup {
    display: inline;
}