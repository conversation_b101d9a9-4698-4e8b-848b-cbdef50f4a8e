package com.hongda.content.service.impl;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import com.hongda.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.hongda.content.mapper.HongdaEventMapper;
import com.hongda.content.mapper.HongdaFormDefinitionMapper;
import com.hongda.data.mapper.HongdaEventRegistrationMapper;
import com.hongda.content.domain.HongdaEvent;
import com.hongda.content.service.IHongdaEventService;
import com.hongda.content.enums.ActivityStatus;
import com.hongda.content.enums.RegistrationStatus;
// 移除广告相关导入，活动推广功能独立于广告管理
// import com.hongda.platform.domain.HongdaAd;
// import com.hongda.platform.service.IHongdaAdService;
import java.util.Date;

/**
 * 活动管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@Service
public class HongdaEventServiceImpl implements IHongdaEventService 
{
    @Autowired
    private HongdaEventMapper hongdaEventMapper;
    
    @Autowired
    private HongdaFormDefinitionMapper hongdaFormDefinitionMapper;
    
    @Autowired
    private HongdaEventRegistrationMapper hongdaEventRegistrationMapper;
    
    // 移除广告服务依赖，活动推广功能独立于广告管理
    // @Autowired
    // private IHongdaAdService hongdaAdService;

    /**
     * 查询活动管理
     * 
     * @param id 活动管理主键
     * @return 活动管理
     */
    @Override
    public HongdaEvent selectHongdaEventById(Long id)
    {
        return hongdaEventMapper.selectHongdaEventById(id);
    }

    /**
     * 查询活动管理列表
     * 
     * @param hongdaEvent 活动管理
     * @return 活动管理
     */
    @Override
    public List<HongdaEvent> selectHongdaEventList(HongdaEvent hongdaEvent)
    {
        return hongdaEventMapper.selectHongdaEventList(hongdaEvent);
    }

    /**
     * 新增活动管理
     * 
     * @param hongdaEvent 活动管理
     * @return 结果
     */
    @Override
    public int insertHongdaEvent(HongdaEvent hongdaEvent)
    {
        // 自动处理地址字段：根据省市区和详细地址生成完整地址
        hongdaEvent.updateLocationFromStructuredAddress();
        
        hongdaEvent.setCreateTime(DateUtils.getNowDate());
        
        System.out.println("新增活动 - 地址信息处理完成：" + 
            "省份=" + hongdaEvent.getProvince() + 
            ", 城市=" + hongdaEvent.getCity() + 
            ", 区县=" + hongdaEvent.getDistrict() + 
            ", 详细地址=" + hongdaEvent.getAddressDetail() + 
            ", 完整地址=" + hongdaEvent.getLocation());
            
        return hongdaEventMapper.insertHongdaEvent(hongdaEvent);
    }

    /**
     * 修改活动管理
     * 
     * @param hongdaEvent 活动管理
     * @return 结果
     */
    @Override
    public int updateHongdaEvent(HongdaEvent hongdaEvent)
    {
        hongdaEvent.setUpdateTime(DateUtils.getNowDate());
        
        // 如果包含地址相关字段，则自动拼接完整地址到location字段
        if (hongdaEvent.getProvince() != null || hongdaEvent.getCity() != null || 
            hongdaEvent.getDistrict() != null || hongdaEvent.getAddressDetail() != null) {
            hongdaEvent.updateLocationFromStructuredAddress();
            
            System.out.println("修改活动 - 地址信息处理完成：" + 
                "省份=" + hongdaEvent.getProvince() + 
                ", 城市=" + hongdaEvent.getCity() + 
                ", 区县=" + hongdaEvent.getDistrict() + 
                ", 详细地址=" + hongdaEvent.getAddressDetail() + 
                ", 完整地址=" + hongdaEvent.getLocation());
        }
        
        return hongdaEventMapper.updateHongdaEvent(hongdaEvent);
    }

    /**
     * 批量删除活动管理
     * 
     * @param ids 需要删除的活动管理主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteHongdaEventByIds(Long[] ids)
    {
        int totalDeleted = 0;
        
        // 遍历每个活动ID，执行级联删除
        for (Long eventId : ids) {
            // 1. 删除该活动的报名记录
            hongdaEventRegistrationMapper.deleteRegistrationByEventId(eventId);
            
            // 2. 删除该活动的表单定义
            hongdaFormDefinitionMapper.deleteFormDefinitionByEventId(eventId);
            
            // 3. 删除活动主表记录
            int deleted = hongdaEventMapper.deleteHongdaEventById(eventId);
            totalDeleted += deleted;
        }
        
        return totalDeleted;
    }

    /**
     * 删除活动管理信息
     * 
     * @param id 活动管理主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteHongdaEventById(Long id)
    {
        // 1. 删除该活动的报名记录
        hongdaEventRegistrationMapper.deleteRegistrationByEventId(id);
        
        // 2. 删除该活动的表单定义
        hongdaFormDefinitionMapper.deleteFormDefinitionByEventId(id);
        
        // 3. 删除活动主表记录
        return hongdaEventMapper.deleteHongdaEventById(id);
    }

    /**
     * 定时任务：自动更新活动状态
     * 注：基于双维度重构，状态现在基于时间动态计算，不再需要定时更新
     * 保留接口用于兼容性，但不执行任何操作
     * 
     * @return 更新的活动数量（始终返回0）
     * @deprecated 状态现在基于时间动态计算，不再需要定时更新
     */
    @Override
    @Deprecated
    public int updateEventStatusAutomatically()
    {
        // 基于时间的动态状态计算，不再需要定时更新状态
        System.out.println("定时任务已废弃：状态现在基于时间动态计算");
        return 0;
    }

    /**
     * 切换活动推广状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int changeEventPromotionStatus(Long eventId, HongdaEvent promotionData) {
        HongdaEvent event = hongdaEventMapper.selectHongdaEventById(eventId);
        if (event == null) {
            throw new RuntimeException("活动不存在");
        }
        
        if (promotionData.getIsPromoted() == 1) {
            // 开启推广 
            event.setIsPromoted(1);
            event.setPromotionTitle(promotionData.getPromotionTitle());
            event.setPromotionImageUrl(promotionData.getPromotionImageUrl());
            event.setPromotionStartTime(promotionData.getPromotionStartTime());
            event.setPromotionEndTime(promotionData.getPromotionEndTime());
            event.setPromotionSortOrder(promotionData.getPromotionSortOrder());
        } else {
            // 关闭推广
            event.setIsPromoted(0);
            event.setPromotionTitle(null);
            event.setPromotionImageUrl(null);
            event.setPromotionStartTime(null);
            event.setPromotionEndTime(null);
            event.setPromotionSortOrder(null);
        }
        
        return hongdaEventMapper.updateHongdaEvent(event);
    }

    /**
     * 获取推广活动列表
     */
    @Override
    public List<HongdaEvent> selectPromotionEventList() {
        return hongdaEventMapper.selectPromotionEventList();
    }

    /**
     * 同步推广信息到广告表
     * @deprecated 活动推广功能独立于广告管理，此方法已废弃
     */
    @Override
    @Deprecated
    public Long syncPromotionToAd(HongdaEvent event) {
        // 活动推广功能已独立，不再同步到广告表
        return null;
    }

    /**
     * 从广告表移除推广信息
     * @deprecated 活动推广功能独立于广告管理，此方法已废弃
     */
    @Override
    @Deprecated
    public int removePromotionFromAd(Long eventId) {
        // 活动推广功能已独立，不再操作广告表
        return 0;
    }

    /**
     * 根据活动状态查询活动列表
     */
    @Override
    public List<HongdaEvent> selectEventListByActivityStatus(Integer activityStatus) {
        List<HongdaEvent> allEvents = hongdaEventMapper.selectHongdaEventList(new HongdaEvent());
        return allEvents.stream()
                .filter(event -> activityStatus.equals(event.getActivityStatus()))
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 根据报名状态查询活动列表
     */
    @Override
    public List<HongdaEvent> selectEventListByRegistrationStatus(Integer registrationStatus) {
        List<HongdaEvent> allEvents = hongdaEventMapper.selectHongdaEventList(new HongdaEvent());
        return allEvents.stream()
                .filter(event -> registrationStatus.equals(event.getRegistrationStatus()))
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 获取活动状态统计
     */
    @Override
    public Map<String, Integer> getEventStatusStatistics() {
        List<HongdaEvent> allEvents = hongdaEventMapper.selectHongdaEventList(new HongdaEvent());
        
        Map<String, Integer> statistics = new HashMap<>();
        statistics.put("activityNotStarted", 0);
        statistics.put("activityInProgress", 0);
        statistics.put("activityEnded", 0);
        statistics.put("registrationNotStarted", 0);
        statistics.put("registrationOpen", 0);
        statistics.put("registrationClosed", 0);
        
        for (HongdaEvent event : allEvents) {
            // 统计活动状态
            Integer activityStatus = event.getActivityStatus();
            switch (activityStatus) {
                case 0:
                    statistics.put("activityNotStarted", statistics.get("activityNotStarted") + 1);
                    break;
                case 1:
                    statistics.put("activityInProgress", statistics.get("activityInProgress") + 1);
                    break;
                case 2:
                    statistics.put("activityEnded", statistics.get("activityEnded") + 1);
                    break;
            }
            
            // 统计报名状态
            Integer registrationStatus = event.getRegistrationStatus();
            switch (registrationStatus) {
                case 0:
                    statistics.put("registrationNotStarted", statistics.get("registrationNotStarted") + 1);
                    break;
                case 1:
                    statistics.put("registrationOpen", statistics.get("registrationOpen") + 1);
                    break;
                case 2:
                    statistics.put("registrationClosed", statistics.get("registrationClosed") + 1);
                    break;
            }
        }
        
        return statistics;
    }

    /**
     * 查询即将开始的活动（1小时内）
     */
    @Override
    public List<HongdaEvent> selectUpcomingEvents() {
        Date now = new Date();
        Date oneHourLater = new Date(now.getTime() + 60 * 60 * 1000); // 1小时后
        
        List<HongdaEvent> allEvents = hongdaEventMapper.selectHongdaEventList(new HongdaEvent());
        return allEvents.stream()
                .filter(event -> event.getStartTime() != null)
                .filter(event -> event.getStartTime().after(now) && event.getStartTime().before(oneHourLater))
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 查询报名即将截止的活动（24小时内）
     */
    @Override
    public List<HongdaEvent> selectRegistrationDeadlineEvents() {
        Date now = new Date();
        Date twentyFourHoursLater = new Date(now.getTime() + 24 * 60 * 60 * 1000); // 24小时后
        
        List<HongdaEvent> allEvents = hongdaEventMapper.selectHongdaEventList(new HongdaEvent());
        return allEvents.stream()
                .filter(event -> event.getRegistrationEndTime() != null)
                .filter(event -> event.getRegistrationEndTime().after(now) && event.getRegistrationEndTime().before(twentyFourHoursLater))
                .collect(java.util.stream.Collectors.toList());
    }


}
