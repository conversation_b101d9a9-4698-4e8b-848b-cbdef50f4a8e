<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongda.content.mapper.HongdaEventMapper">
    
    <resultMap type="HongdaEvent" id="HongdaEventResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="iconUrl"    column="icon_url"    />
        <result property="coverImageUrl"    column="cover_image_url"    />
        <result property="details"    column="details"    />
        <result property="summary"    column="summary"    />
        <result property="sellPoint"    column="sell_point"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="location"    column="location"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="district"    column="district"    />
        <result property="addressDetail"    column="address_detail"    />
        <result property="registrationDeadline"    column="registration_deadline"    />
        <result property="registrationStartTime"    column="registration_start_time"    />
        <result property="registrationEndTime"    column="registration_end_time"    />
        <result property="maxParticipants"    column="max_participants"    />
        <result property="registeredCount"    column="registered_count"    />
        <result property="status"    column="status"    />
        <result property="isHot"    column="is_hot"    />
        <result property="isPromoted"    column="is_promoted"    />
        <result property="promotionTitle"    column="promotion_title"    />
        <result property="promotionImageUrl"    column="promotion_image_url"    />
        <result property="promotionStartTime"    column="promotion_start_time"    />
        <result property="promotionEndTime"    column="promotion_end_time"    />
        <result property="promotionSortOrder"    column="promotion_sort_order"    />
        <!-- 移除广告关联字段映射，活动推广功能独立于广告管理 -->
        <!-- <result property="promotionAdId"    column="promotion_ad_id"    /> -->
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectHongdaEventVo">
        select id, title, icon_url, cover_image_url, details, summary, sell_point, start_time, end_time, location, province, city, district, address_detail, registration_deadline, registration_start_time, registration_end_time, max_participants, registered_count, status, is_hot, is_promoted, promotion_title, promotion_image_url, promotion_start_time, promotion_end_time, promotion_sort_order, create_by, create_time, update_time from hongda_event
    </sql>

    <select id="selectHongdaEventList" parameterType="HongdaEvent" resultMap="HongdaEventResult">
        <include refid="selectHongdaEventVo"/>
        <where>  
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="iconUrl != null  and iconUrl != ''"> and icon_url = #{iconUrl}</if>
            <if test="coverImageUrl != null  and coverImageUrl != ''"> and cover_image_url = #{coverImageUrl}</if>
            <if test="details != null  and details != ''"> and details = #{details}</if>
            <if test="summary != null  and summary != ''"> and summary = #{summary}</if>
            <if test="sellPoint != null  and sellPoint != ''"> and sell_point = #{sellPoint}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="location != null  and location != ''"> and location = #{location}</if>
            <if test="city != null  and city != ''"> and (city = #{city} OR city = CONCAT(#{city}, '市'))</if>
            <if test="registrationDeadline != null "> and registration_deadline = #{registrationDeadline}</if>
            <if test="maxParticipants != null "> and max_participants = #{maxParticipants}</if>
            <if test="registeredCount != null "> and registered_count = #{registeredCount}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="isHot != null "> and is_hot = #{isHot}</if>
        </where>
    </select>
    
    <select id="selectHongdaEventById" parameterType="Long" resultMap="HongdaEventResult">
        <include refid="selectHongdaEventVo"/>
        where id = #{id}
    </select>

    <insert id="insertHongdaEvent" parameterType="HongdaEvent" useGeneratedKeys="true" keyProperty="id">
        insert into hongda_event
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="iconUrl != null">icon_url,</if>
            <if test="coverImageUrl != null">cover_image_url,</if>
            <if test="details != null">details,</if>
            <if test="summary != null">summary,</if>
            <if test="sellPoint != null">sell_point,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="location != null and location != ''">location,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="district != null">district,</if>
            <if test="addressDetail != null and addressDetail != ''">address_detail,</if>
            <if test="registrationDeadline != null">registration_deadline,</if>
            <if test="registrationStartTime != null">registration_start_time,</if>
            <if test="registrationEndTime != null">registration_end_time,</if>
            <if test="maxParticipants != null">max_participants,</if>
            <if test="registeredCount != null">registered_count,</if>
            <if test="status != null">status,</if>
            <if test="isHot != null">is_hot,</if>
            <if test="isPromoted != null">is_promoted,</if>
            <if test="promotionTitle != null">promotion_title,</if>
            <if test="promotionImageUrl != null">promotion_image_url,</if>
            <if test="promotionStartTime != null">promotion_start_time,</if>
            <if test="promotionEndTime != null">promotion_end_time,</if>
            <if test="promotionSortOrder != null">promotion_sort_order,</if>
            <!-- 移除广告关联字段，活动推广功能独立于广告管理 -->
            <!-- <if test="promotionAdId != null">promotion_ad_id,</if> -->
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="iconUrl != null">#{iconUrl},</if>
            <if test="coverImageUrl != null">#{coverImageUrl},</if>
            <if test="details != null">#{details},</if>
            <if test="summary != null">#{summary},</if>
            <if test="sellPoint != null">#{sellPoint},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="location != null and location != ''">#{location},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="district != null">#{district},</if>
            <if test="addressDetail != null and addressDetail != ''">#{addressDetail},</if>
            <if test="registrationDeadline != null">#{registrationDeadline},</if>
            <if test="registrationStartTime != null">#{registrationStartTime},</if>
            <if test="registrationEndTime != null">#{registrationEndTime},</if>
            <if test="maxParticipants != null">#{maxParticipants},</if>
            <if test="registeredCount != null">#{registeredCount},</if>
            <if test="status != null">#{status},</if>
            <if test="isHot != null">#{isHot},</if>
            <if test="isPromoted != null">#{isPromoted},</if>
            <if test="promotionTitle != null">#{promotionTitle},</if>
            <if test="promotionImageUrl != null">#{promotionImageUrl},</if>
            <if test="promotionStartTime != null">#{promotionStartTime},</if>
            <if test="promotionEndTime != null">#{promotionEndTime},</if>
            <if test="promotionSortOrder != null">#{promotionSortOrder},</if>
            <!-- 移除广告关联字段，活动推广功能独立于广告管理 -->
            <!-- <if test="promotionAdId != null">#{promotionAdId},</if> -->
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateHongdaEvent" parameterType="HongdaEvent">
        update hongda_event
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="iconUrl != null">icon_url = #{iconUrl},</if>
            <if test="coverImageUrl != null">cover_image_url = #{coverImageUrl},</if>
            <if test="details != null">details = #{details},</if>
            <if test="summary != null">summary = #{summary},</if>
            <if test="sellPoint != null">sell_point = #{sellPoint},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="location != null and location != ''">location = #{location},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="district != null">district = #{district},</if>
            <if test="addressDetail != null and addressDetail != ''">address_detail = #{addressDetail},</if>
            <if test="registrationDeadline != null">registration_deadline = #{registrationDeadline},</if>
            <if test="registrationStartTime != null">registration_start_time = #{registrationStartTime},</if>
            <if test="registrationEndTime != null">registration_end_time = #{registrationEndTime},</if>
            <if test="maxParticipants != null">max_participants = #{maxParticipants},</if>
            <if test="registeredCount != null">registered_count = #{registeredCount},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isHot != null">is_hot = #{isHot},</if>
            <if test="isPromoted != null">is_promoted = #{isPromoted},</if>
            <if test="promotionTitle != null">promotion_title = #{promotionTitle},</if>
            <if test="promotionImageUrl != null">promotion_image_url = #{promotionImageUrl},</if>
            <if test="promotionStartTime != null">promotion_start_time = #{promotionStartTime},</if>
            <if test="promotionEndTime != null">promotion_end_time = #{promotionEndTime},</if>
            <if test="promotionSortOrder != null">promotion_sort_order = #{promotionSortOrder},</if>
            <!-- 移除广告关联字段，活动推广功能独立于广告管理 -->
            <!-- <if test="promotionAdId != null">promotion_ad_id = #{promotionAdId},</if> -->
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHongdaEventById" parameterType="Long">
        delete from hongda_event where id = #{id}
    </delete>

    <delete id="deleteHongdaEventByIds" parameterType="String">
        delete from hongda_event where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 批量更新已过报名截止时间但未结束的活动状态为"报名中" -->
    <update id="updateStatusToOngoing">
        UPDATE hongda_event
        SET status = 1,
            update_time = NOW()
        WHERE
            status = 0 -- 当前状态是"未开始"
            AND registration_deadline &lt; NOW() -- 已过报名截止时间
            AND end_time &gt; NOW() -- 活动还未结束
    </update>

    <!-- 批量更新已过结束时间的活动状态为"已结束" -->
    <update id="updateStatusToEnded">
        UPDATE hongda_event
        SET status = 2,
            update_time = NOW()
        WHERE
            status != 3 -- 排除已经是"已取消"的活动
            AND status != 2 -- 排除已经是"已结束"的活动
            AND end_time &lt; NOW() -- 关键条件：活动的结束时间已经早于当前时间
    </update>

    <!-- 增加活动报名人数 -->
    <update id="incrementRegisteredCount" parameterType="Long">
        UPDATE hongda_event
        SET registered_count = registered_count + 1,
            update_time = NOW()
        WHERE id = #{eventId}
    </update>

    <!-- 减少活动报名人数 -->
    <update id="decrementRegisteredCount" parameterType="Long">
        UPDATE hongda_event
        SET registered_count = registered_count - 1,
            update_time = NOW()
        WHERE id = #{eventId}
          AND registered_count > 0
    </update>

    <!-- 查询推广活动列表 -->
    <select id="selectPromotionEventList" resultMap="HongdaEventResult">
        <include refid="selectHongdaEventVo"/>
        WHERE is_promoted = 1
        AND status IN (1, 4) <!-- 只显示报名中或进行中的活动 -->
        AND (promotion_start_time IS NULL OR promotion_start_time &lt;= NOW())
        AND (promotion_end_time IS NULL OR promotion_end_time &gt;= NOW())
        ORDER BY promotion_sort_order ASC, create_time DESC
    </select>

</mapper>