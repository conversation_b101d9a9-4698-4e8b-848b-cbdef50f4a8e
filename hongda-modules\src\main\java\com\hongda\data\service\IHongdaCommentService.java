package com.hongda.data.service;

import com.hongda.data.domain.HongdaComment;
import com.hongda.data.domain.vo.HongdaCommentVo;

import java.util.List;
import java.util.Map;

/**
 * 评论管理Service接口
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface IHongdaCommentService
{
    /**
     * 查询评论管理
     * @param id 评论管理主键
     * @return 评论管理
     */
    public HongdaCommentVo selectHongdaCommentById(Long id);

    /**
     * 查询评论管理列表
     * @param hongdaComment 评论管理
     * @return 评论管理集合
     */
    public Map<String, Object> selectHongdaCommentList(HongdaComment hongdaComment);

    /**
     * 新增评论管理
     * @param hongdaComment 评论管理
     * @return 结果
     */
    public int insertHongdaComment(HongdaComment hongdaComment);

    /**
     * 修改评论管理
     * @param hongdaComment 评论管理
     * @return 结果
     */
    public int updateHongdaComment(HongdaComment hongdaComment);

    /**
     * 批量删除评论管理
     * @param ids 需要删除的评论管理主键集合
     * @return 结果
     */
    public int deleteHongdaCommentByIds(Long[] ids);

    /**
     * 删除评论管理信息
     * @param id 评论管理主键
     * @return 结果
     */
    public int deleteHongdaCommentById(Long id);

    /**
     * 为小程序端查询评论列表（只返回树形列表）
     */
    public List<HongdaCommentVo> selectWxAppCommentList(HongdaComment hongdaComment);

    /**
     * 导出评论管理列表
     */
    public List<HongdaCommentVo> selectHongdaCommentListForExport(HongdaComment hongdaComment);

    // 已删除 toggleLike 方法
}