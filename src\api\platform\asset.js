import request from '@/utils/request'

// 查询小程序静态资源配置列表
export function listAsset(query) {
  return request({
    url: '/platform/asset/list',
    method: 'get',
    params: query
  })
}

// 查询小程序静态资源配置详细
export function getAsset(id) {
  return request({
    url: '/platform/asset/' + id,
    method: 'get'
  })
}

// 新增小程序静态资源配置
export function addAsset(data) {
  return request({
    url: '/platform/asset',
    method: 'post',
    data: data
  })
}

// 修改小程序静态资源配置
export function updateAsset(data) {
  return request({
    url: '/platform/asset',
    method: 'put',
    data: data
  })
}

// 删除小程序静态资源配置
export function delAsset(id) {
  return request({
    url: '/platform/asset/' + id,
    method: 'delete'
  })
}
