<template>
  <div class="phone-screen">
    <div class="status-bar">
      <div class="status-time">{{ currentTime }}</div>
    </div>
    <div class="navigation-bar">
      <div class="nav-title">资讯详情</div>
    </div>

    <div class="scrollable-content">
      <div v-if="article" class="article-wrapper">
        <div class="hero-section">
          <img class="hero-image" :src="article.coverImageUrl || defaultCover">
        </div>

        <div class="main-content">
          <div class="article-title">{{ article.title }}</div>

          <div class="article-meta-new">
            <div class="meta-left">
              <span class="meta-text">{{ article.source }}</span>
              <span class="meta-text">{{ formatDate(article.publishTime, 'YYYY-MM-DD') }}</span>
            </div>
            <div class="meta-right">
              <span class="meta-text">{{ article.viewCount }} 次阅读</span>
            </div>
          </div>

          <div class="summary-card" v-if="article.summary">
            <span class="summary-text">{{ article.summary }}</span>
          </div>

          <div class="content-card">
            <div class="content-body" v-html="sanitizedContent"></div>
          </div>
        </div>

        <div class="tags-section-new" v-if="article.tags && article.tags.length > 0">
          <span class="tags-label">分类：</span>
          <span v-for="(tag, index) in article.tags" :key="tag.id || tag.name" class="tag-item-new">
            {{ tag.name || tag }}{{ index < article.tags.length - 1 ? ' / ' : '' }}
          </span>
        </div>

        <div class="comment-container">
          <div class="comment-header-section">
            <span class="comment-main-title">留言 (0)</span>
          </div>
          <div class="comment-input-card">
            <img
                class="comment-input-icon"
                src="http://47.122.155.110:9000/hongda-public/system%2FFrame%201_slices%2F%E5%A1%AB%E5%86%99%402x.png"
            />
            <span class="comment-input-placeholder">写留言</span>
          </div>
          <div class="empty-state">
            <div class="empty-icon">💬</div>
            <span class="empty-title">暂无评论</span>
            <span class="empty-desc">成为第一个发表看法的人吧</span>
          </div>
        </div>
      </div>

      <div v-else class="loading-skeleton">
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import DOMPurify from 'dompurify';

// Props 定义，保持不变
const props = defineProps({
  article: {
    type: Object,
    required: true
  }
});

// 响应式数据
const currentTime = ref('');
const isLoading = ref(true);
const defaultCover = 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=400&h=300&fit=crop';
let timeInterval = null;

// 计算属性，用于安全地渲染HTML，保持不变
const sanitizedContent = computed(() => {
  if (typeof window === 'undefined') return '';
  const htmlContent = props.article ? props.article.content : '';
  return DOMPurify.sanitize(htmlContent || '<p style="color: #999; text-align: center; padding: 40px 0;">暂无内容...</p>', {
    ADD_ATTR: ['style', 'class', 'target'],
    USE_PROFILES: { html: true }
  });
});

// 生命周期，用于更新时钟和加载状态，保持不变
onMounted(() => {
  updateTime();
  startTimeUpdater();
});

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval);
  }
});

watch(() => props.article, () => {
  isLoading.value = true;
  // 模拟加载效果
  setTimeout(() => {
    isLoading.value = false;
  }, 500);
}, {
  immediate: true,
  deep: true
});

// 方法
const updateTime = () => {
  const now = new Date();
  currentTime.value = now.toTimeString().slice(0, 5);
};

const startTimeUpdater = () => {
  timeInterval = setInterval(updateTime, 1000);
};

// 复用 formatDate 方法
const formatDate = (dateString, format = 'YYYY-MM-DD') => {
  if (!dateString) return '';
  const date = new Date(dateString.replace(/-/g, '/'));
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  if (format === 'YYYY-MM-DD') {
    return `${year}-${month}-${day}`;
  }
  return dateString;
};
</script>

<style lang="scss" scoped>
/* 整个样式块替换为 detail.vue 的样式，并稍作调整 */

/* 容器和基础布局 */
.phone-screen {
  width: 100%;
  height: 100%;
  background: #FFFFFF; // 统一页面背景色
  border-radius: 32px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* 保留模拟的状态栏和导航栏样式 */
.status-bar {
  height: 20px; /* 简化模拟，固定高度 */
  background: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 10px;
  font-size: 12px;
  font-weight: 600;
  color: #000;
  flex-shrink: 0;
}
.navigation-bar {
  height: 44px;
  background: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
  flex-shrink: 0;
  border-bottom: 0.5px solid #f0f0f0;
}

/* 滚动区域，采用 detail.vue 的思路 */
.scrollable-content {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  background-color: #FFFFFF;
}

/* --- 以下为从 detail.vue 复制并适配的样式 --- */

.hero-section {
  position: relative;
  width: 100%;
  height: 250px; /* 使用 px 替代 rpx */
  .hero-image {
    width: 100%;
    height: 100%;
    object-fit: cover; /* 增强图片显示效果 */
  }
}

.main-content {
  padding: 0;
  margin-top: 0;
  position: relative;
  z-index: 3;
}

.article-title {
  font-size: 20px; /* 使用 px 替代 rpx */
  color: #23232A;
  font-weight: 700;
  line-height: 1.5;
  padding: 15px 15px 0 15px;
  margin-bottom: 12px;
}

.article-meta-new {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 15px;
  margin-bottom: 15px;
  .meta-left {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  .meta-text {
    font-size: 12px;
    color: #9B9A9A;
  }
}

.summary-card {
  background-image: url('http://47.122.155.110:9000/hongda-public/system%2FFrame%201_slices%2F%E5%BA%95%E5%9B%BE%402x.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  margin: 0 15px 0 15px;
  padding: 15px;
  .summary-text {
    font-size: 15px;
    font-weight: 600;
    color: #495057;
    line-height: 1.8;
  }
}

.content-card {
  background: #FFFFFF;
  border-radius: 12px;
  margin: 0 15px;
  padding: 5px 0; // 给内容一些呼吸空间
  :deep(img) { // 通过 :deep 确保 v-html 内的图片样式正确
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    display: block;
    margin: 15px auto;
  }

  /* ==================== 【本次核心修正】 ==================== */
  /* 新增的表格样式，使用 :deep() 穿透作用域 */
  :deep(table) {
    width: 100%;
    border-collapse: collapse; /* 让边框合并为一条线 */
    margin: 20px 0;
    font-size: 14px;
    text-align: left;
  }

  :deep(th),
  :deep(td) {
    border: 1px solid #dfe6ec; /* 定义单元格边框 */
    padding: 10px 12px; /* 单元格内边距 */
  }

  :deep(th) {
    background-color: #f8f9fa; /* 表头背景色 */
    font-weight: 600;
  }
  /* ======================================================== */
}

.tags-section-new {
  margin: 15px;
  padding: 10px 12px;
  background: #F2F4FA;
  border-radius: 8px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 4px;
  .tags-label {
    font-size: 12px;
    color: #66666E;
    font-weight: 600;
    margin-right: 6px;
  }
  .tag-item-new {
    font-size: 12px;
    color: #66666E;
  }
}

.comment-container {
  margin-top: 10px;
  padding: 0 15px 20px 15px;
  background: #ffffff;
}

.comment-header-section {
  padding: 20px 0 15px 0;
  border-bottom: 1px solid #f1f5f9;
  .comment-main-title {
    font-size: 18px;
    font-weight: 600;
    color: #303133;
  }
}

.comment-input-card {
  width: 100%;
  height: 36px;
  background: #F7F7F7;
  border-radius: 4px;
  margin: 15px 0;
  display: flex;
  align-items: center;
  padding: 0 12px;
  box-sizing: border-box;
  .comment-input-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
  }
  .comment-input-placeholder {
    font-size: 14px;
    color: #9B9A9A;
  }
}

.empty-state {
  padding: 40px 0;
  text-align: center;
  .empty-icon {
    font-size: 40px;
    margin-bottom: 12px;
    opacity: 0.5;
  }
  .empty-title {
    font-size: 15px;
    font-weight: 500;
    color: #606266;
    margin-bottom: 6px;
  }
  .empty-desc {
    font-size: 13px;
    color: #909399;
  }
}

</style>