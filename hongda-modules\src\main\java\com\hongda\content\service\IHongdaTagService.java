package com.hongda.content.service;

import java.util.List;
import com.hongda.content.domain.HongdaTag;

/**
 * 资讯分类标签Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface IHongdaTagService 
{
    /**
     * 查询资讯分类标签
     * 
     * @param id 资讯分类标签主键
     * @return 资讯分类标签
     */
    public HongdaTag selectHongdaTagById(Long id);

    /**
     * 查询资讯分类标签列表
     * 
     * @param hongdaTag 资讯分类标签
     * @return 资讯分类标签集合
     */
    public List<HongdaTag> selectHongdaTagList(HongdaTag hongdaTag);

    /**
     * 新增资讯分类标签
     * 
     * @param hongdaTag 资讯分类标签
     * @return 结果
     */
    public int insertHongdaTag(HongdaTag hongdaTag);

    /**
     * 修改资讯分类标签
     * 
     * @param hongdaTag 资讯分类标签
     * @return 结果
     */
    public int updateHongdaTag(HongdaTag hongdaTag);

    /**
     * 批量删除资讯分类标签
     * 
     * @param ids 需要删除的资讯分类标签主键集合
     * @return 结果
     */
    public int deleteHongdaTagByIds(Long[] ids);

    /**
     * 删除资讯分类标签信息
     * 
     * @param id 资讯分类标签主键
     * @return 结果
     */
    public int deleteHongdaTagById(Long id);
}
