package com.hongda.platform.service;

import java.util.List;
import com.hongda.platform.domain.HongdaNav;

/**
 * 导航配置Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface IHongdaNavService 
{
    /**
     * 查询导航配置
     * 
     * @param id 导航配置主键
     * @return 导航配置
     */
    public HongdaNav selectHongdaNavById(Long id);

    /**
     * 查询导航配置列表
     * 
     * @param hongdaNav 导航配置
     * @return 导航配置集合
     */
    public List<HongdaNav> selectHongdaNavList(HongdaNav hongdaNav);

    /**
     * 新增导航配置
     * 
     * @param hongdaNav 导航配置
     * @return 结果
     */
    public int insertHongdaNav(HongdaNav hongdaNav);

    /**
     * 修改导航配置
     * 
     * @param hongdaNav 导航配置
     * @return 结果
     */
    public int updateHongdaNav(HongdaNav hongdaNav);

    /**
     * 批量删除导航配置
     * 
     * @param ids 需要删除的导航配置主键集合
     * @return 结果
     */
    public int deleteHongdaNavByIds(Long[] ids);

    /**
     * 删除导航配置信息
     * 
     * @param id 导航配置主键
     * @return 结果
     */
    public int deleteHongdaNavById(Long id);
}
