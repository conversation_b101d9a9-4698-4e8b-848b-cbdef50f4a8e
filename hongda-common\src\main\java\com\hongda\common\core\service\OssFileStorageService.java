package com.hongda.common.core.service;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.hongda.common.config.AliOssConfigProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.net.URL;
import java.util.Date;

@Slf4j
@Service
public class OssFileStorageService {

    @Autowired
    private OSS ossClient;

    @Autowired
    private AliOssConfigProperties aliOssConfigProperties;

    /**
     * 上传文件
     * @param objectName  文件名
     * @param inputStream 文件输入流
     * @return 文件的公开访问URL
     */
    public String upload(String objectName, InputStream inputStream) {
        String bucketName = aliOssConfigProperties.getBucketName();
        String endpoint = aliOssConfigProperties.getEndpoint();

        log.info("OSS文件上传开始: {}, bucket: {}", objectName, bucketName);

        try {
            // 上传文件。
            ossClient.putObject(bucketName, objectName, inputStream);
        } catch (Exception e) {
            log.error("OSS文件上传失败", e);
            // 抛出异常或返回null，取决于您的错误处理策略
            throw new RuntimeException("文件上传到OSS失败", e);
        }

        // 构建文件的公开访问URL
        String url = "https://" + bucketName + "." + endpoint + "/" + objectName;
        log.info("OSS文件上传成功, URL: {}", url);
        return url;
    }

    /**
     * 【重要新增方法】为私有文件生成带签名的临时访问URL
     * 支持处理完整URL和文件名两种格式
     *
     * @param input 文件的对象键（文件名）或完整的OSS URL
     * @param expireInMinutes URL的有效时间（分钟）
     * @return 带签名的URL字符串
     */
    public String getSignedUrl(String input, int expireInMinutes) {
        if (input == null || input.trim().isEmpty()) {
            return null;
        }

        String bucketName = aliOssConfigProperties.getBucketName();
        String endpoint = aliOssConfigProperties.getEndpoint();
        String objectName;

        // 判断输入是完整URL还是文件名
        if (input.startsWith("http://") || input.startsWith("https://")) {
            // 如果是完整URL，提取对象名
            try {
                // 检查是否是当前OSS的URL
                if (input.contains(bucketName + "." + endpoint)) {
                    // 提取对象名：从域名后的第一个斜杠开始，到查询参数之前
                    String urlPath = input.substring(input.indexOf("://") + 3);
                    int firstSlash = urlPath.indexOf('/');
                    if (firstSlash != -1) {
                        String pathPart = urlPath.substring(firstSlash + 1);
                        // 移除查询参数（如果有的话）
                        int queryIndex = pathPart.indexOf('?');
                        objectName = queryIndex != -1 ? pathPart.substring(0, queryIndex) : pathPart;
                        log.info("从完整URL提取对象名: {} -> {}", input, objectName);
                    } else {
                        log.warn("完整URL格式不正确，无法提取对象名: {}", input);
                        return input; // 返回原URL
                    }
                } else {
                    // 如果是其他OSS服务或外部URL，直接返回
                    log.info("外部URL，直接返回: {}", input);
                    return input;
                }
            } catch (Exception e) {
                log.error("解析完整URL失败: {}, 错误: {}", input, e.getMessage());
                return input; // 解析失败时返回原URL
            }
        } else {
            // 如果是文件名，直接使用
            objectName = input;
            log.info("使用文件名生成签名URL: {}", objectName);
        }

        try {
            // 设置URL过期时间
            Date expiration = new Date(new Date().getTime() + expireInMinutes * 60 * 1000L);

            // 创建请求
            GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(bucketName, objectName);
            request.setExpiration(expiration);

            // 生成签名URL
            URL signedUrl = ossClient.generatePresignedUrl(request);
            String result = signedUrl.toString();
            log.info("为对象 {} 生成签名URL成功: {}", objectName, result);
            return result;
            
        } catch (Exception e) {
            log.error("生成签名URL失败，对象名: {}, 错误: {}", objectName, e.getMessage(), e);
            // 如果生成签名URL失败，尝试返回原输入
            return input;
        }
    }
}