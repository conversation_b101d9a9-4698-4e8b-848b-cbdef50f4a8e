package com.hongda.content.service;

import java.util.List;
import com.hongda.content.domain.HongdaCountryPolicyArticle;

/**
 * 国别政策管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
public interface IHongdaCountryPolicyArticleService 
{
    /**
     * 查询国别政策管理
     * 
     * @param articleId 国别政策管理主键
     * @return 国别政策管理
     */
    public HongdaCountryPolicyArticle selectHongdaCountryPolicyArticleByArticleId(Long articleId);

    /**
     * 查询国别政策管理列表
     * 
     * @param hongdaCountryPolicyArticle 国别政策管理
     * @return 国别政策管理集合
     */
    public List<HongdaCountryPolicyArticle> selectHongdaCountryPolicyArticleList(HongdaCountryPolicyArticle hongdaCountryPolicyArticle);

    /**
     * 新增国别政策管理
     * 
     * @param hongdaCountryPolicyArticle 国别政策管理
     * @return 结果
     */
    public int insertHongdaCountryPolicyArticle(HongdaCountryPolicyArticle hongdaCountryPolicyArticle);

    /**
     * 修改国别政策管理
     * 
     * @param hongdaCountryPolicyArticle 国别政策管理
     * @return 结果
     */
    public int updateHongdaCountryPolicyArticle(HongdaCountryPolicyArticle hongdaCountryPolicyArticle);

    /**
     * 批量删除国别政策管理
     * 
     * @param articleIds 需要删除的国别政策管理主键集合
     * @return 结果
     */
    public int deleteHongdaCountryPolicyArticleByArticleIds(Long[] articleIds);

    /**
     * 删除国别政策管理信息
     * 
     * @param articleId 国别政策管理主键
     * @return 结果
     */
    public int deleteHongdaCountryPolicyArticleByArticleId(Long articleId);
}
