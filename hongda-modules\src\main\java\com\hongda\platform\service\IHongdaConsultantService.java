package com.hongda.platform.service;

import com.hongda.platform.domain.HongdaConsultant;

import java.util.List;

/**
 * 顾问管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
public interface IHongdaConsultantService 
{
    /**
     * 查询顾问管理
     * 
     * @param id 顾问管理主键
     * @return 顾问管理
     */
    public HongdaConsultant selectHongdaConsultantById(Long id);

    /**
     * 查询顾问管理列表
     * 
     * @param hongdaConsultant 顾问管理
     * @return 顾问管理集合
     */
    public List<HongdaConsultant> selectHongdaConsultantList(HongdaConsultant hongdaConsultant);

    /**
     * 新增顾问管理
     * 
     * @param hongdaConsultant 顾问管理
     * @return 结果
     */
    public int insertHongdaConsultant(HongdaConsultant hongdaConsultant);

    /**
     * 修改顾问管理
     * 
     * @param hongdaConsultant 顾问管理
     * @return 结果
     */
    public int updateHongdaConsultant(HongdaConsultant hongdaConsultant);

    /**
     * 批量删除顾问管理
     * 
     * @param ids 需要删除的顾问管理主键集合
     * @return 结果
     */
    public int deleteHongdaConsultantByIds(Long[] ids);

    /**
     * 删除顾问管理信息
     * 
     * @param id 顾问管理主键
     * @return 结果
     */
    public int deleteHongdaConsultantById(Long id);

    public HongdaConsultant selectDisplayConsultant();
}
