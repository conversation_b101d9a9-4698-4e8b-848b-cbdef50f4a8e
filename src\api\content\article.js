import request from '@/utils/request'

// 查询资讯管理列表
export function listArticle(query) {
  return request({
    url: '/content/article/list',
    method: 'get',
    params: query
  })
}

// 查询资讯管理详细
export function getArticle(id) {
  return request({
    url: '/content/article/' + id,
    method: 'get'
  })
}

// 新增资讯管理
export function addArticle(data) {
  return request({
    url: '/content/article',
    method: 'post',
    data: data
  })
}

// 修改资讯管理
export function updateArticle(data) {
  return request({
    url: '/content/article',
    method: 'put',
    data: data
  })
}

// 删除资讯管理
export function delArticle(id) {
  return request({
    url: '/content/article/' + id,
    method: 'delete'
  })
}
