package com.hongda.content.controller;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hongda.common.annotation.Log;
import com.hongda.common.core.controller.BaseController;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.common.enums.BusinessType;
import com.hongda.content.domain.HongdaFormDefinition;
import com.hongda.content.service.IHongdaFormDefinitionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * 表单定义Controller
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
@Tag(name = "表单定义管理", description = "活动表单定义的管理操作")
@RestController
@RequestMapping("/content/form-definition")
public class HongdaFormDefinitionController extends BaseController
{
    @Autowired
    private IHongdaFormDefinitionService hongdaFormDefinitionService;

    /**
     * 获取指定活动的表单定义
     */
    @Operation(summary = "获取活动表单定义", description = "根据活动ID获取表单定义")
    @PreAuthorize("@ss.hasPermi('content:event:query')")
    @GetMapping("/event/{eventId}")
    public AjaxResult getFormDefinition(@Parameter(description = "活动ID") @PathVariable("eventId") Long eventId)
    {
        HongdaFormDefinition formDefinition = hongdaFormDefinitionService.selectFormDefinitionByEventId(eventId);
        return success(formDefinition);
    }

    /**
     * 保存或更新活动的表单定义
     */
    @Operation(summary = "保存表单定义", description = "保存或更新活动的表单定义")
    @PreAuthorize("@ss.hasPermi('content:event:edit')")
    @Log(title = "表单定义", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult saveFormDefinition(@Parameter(description = "表单定义对象") @RequestBody HongdaFormDefinition formDefinition)
    {
        return toAjax(hongdaFormDefinitionService.saveOrUpdateFormDefinition(formDefinition));
    }
}