<template>
  <el-row :gutter="10" class="mb8">
    <el-col :span="1.5">
      <el-button
        type="primary"
        plain
        icon="Plus"
        @click="handleAdd"
        v-hasPermi="['content:event:add']"
      >新增</el-button>
    </el-col>
    <el-col :span="1.5">
      <el-button
        type="success"
        plain
        icon="Edit"
        :disabled="single"
        @click="handleUpdate"
        v-hasPermi="['content:event:edit']"
      >修改</el-button>
    </el-col>
    <el-col :span="1.5">
      <el-button
        type="danger"
        plain
        icon="Delete"
        :disabled="multiple"
        @click="handleDelete"
        v-hasPermi="['content:event:remove']"
      >删除</el-button>
    </el-col>
    <el-col :span="1.5">
      <el-button
        type="warning"
        plain
        icon="Download"
        @click="handleExport"
        v-hasPermi="['content:event:export']"
      >导出</el-button>
    </el-col>
    
    <right-toolbar v-model:showSearch="showSearch" @queryTable="refreshList"></right-toolbar>
  </el-row>
</template>

<script setup>
// Props
const props = defineProps({
  single: {
    type: Boolean,
    default: true
  },
  multiple: {
    type: Boolean,
    default: true
  },
  showSearch: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits([
  'add', 
  'update', 
  'delete', 
  'export', 
  'refresh',
  'update:showSearch'
])

// Methods
const handleAdd = () => {
  emit('add')
}

const handleUpdate = () => {
  emit('update')
}

const handleDelete = () => {
  emit('delete')
}

const handleExport = () => {
  emit('export')
}

const refreshList = () => {
  emit('refresh')
}
</script>
