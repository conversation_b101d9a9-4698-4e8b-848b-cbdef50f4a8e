<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongda.content.mapper.HongdaRegionMapper">
    
    <resultMap type="HongdaRegion" id="HongdaRegionResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="code"    column="code"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectHongdaRegionVo">
        select id, name, code, sort_order, status, create_by, create_time, update_by, update_time, remark from hongda_region
    </sql>

    <select id="selectHongdaRegionList" parameterType="HongdaRegion" resultMap="HongdaRegionResult">
        <include refid="selectHongdaRegionVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="code != null  and code != ''"> and code like concat('%', #{code}, '%')</if>
        </where>
    </select>
    
    <select id="selectHongdaRegionById" parameterType="Long" resultMap="HongdaRegionResult">
        <include refid="selectHongdaRegionVo"/>
        where id = #{id}
    </select>

    <insert id="insertHongdaRegion" parameterType="HongdaRegion" useGeneratedKeys="true" keyProperty="id">
        insert into hongda_region
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="code != null and code != ''">code,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="code != null and code != ''">#{code},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateHongdaRegion" parameterType="HongdaRegion">
        update hongda_region
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHongdaRegionById" parameterType="Long">
        delete from hongda_region where id = #{id}
    </delete>

    <delete id="deleteHongdaRegionByIds" parameterType="String">
        delete from hongda_region where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>