<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongda.data.mapper.StatisticMapper">

    <!-- 获取总报名人次 -->
    <select id="getTotalRegistrations" resultType="Long">
        SELECT COUNT(*) FROM hongda_event_registration
    </select>

    <!-- 获取今日新增报名人次 -->
    <select id="getTodayNewRegistrations" resultType="Long">
        SELECT COUNT(*) FROM hongda_event_registration 
        WHERE TO_DAYS(registration_time) = TO_DAYS(NOW())
    </select>

    <!-- 获取总用户数 -->
    <select id="getTotalUsers" resultType="Long">
        SELECT COUNT(*) FROM hongda_user
    </select>

    <!-- 获取正在进行中的活动数 -->
    <select id="getOngoingEvents" resultType="Long">
        SELECT COUNT(*) FROM hongda_event 
        WHERE status = 1
    </select>

    <!-- 获取热门活动报名排行TOP5 -->
    <select id="getTopEvents" resultType="Map">
        SELECT 
            e.title,
            COUNT(r.id) as registration_count
        FROM 
            hongda_event e
        LEFT JOIN 
            hongda_event_registration r ON e.id = r.event_id
        GROUP BY 
            e.id, e.title
        ORDER BY 
            registration_count DESC
        LIMIT 5
    </select>

    <select id="getDailyTrend" resultType="map">
        SELECT
            DATE_FORMAT(registration_time, '%m-%d') as `date`,
            COUNT(*) as `count`
        FROM
            hongda_event_registration
        WHERE
            registration_time >= DATE_SUB(CURDATE(), INTERVAL 6 DAY)
        GROUP BY
            `date`
        ORDER BY
            `date` ASC
    </select>

</mapper>