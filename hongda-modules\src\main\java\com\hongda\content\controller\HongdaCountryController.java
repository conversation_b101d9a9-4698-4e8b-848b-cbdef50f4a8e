package com.hongda.content.controller;

import com.hongda.common.annotation.Log;
import com.hongda.common.core.controller.BaseController;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.common.core.page.TableDataInfo;
import com.hongda.common.enums.BusinessType;
import com.hongda.common.utils.poi.ExcelUtil;
import com.hongda.content.domain.HongdaCountry;
import com.hongda.content.service.IHongdaCountryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@Tag(name = "国别信息管理", description = "国别信息的增删改查操作")
@RestController
@RequestMapping("/content/country")
public class HongdaCountryController extends BaseController
{
    @Autowired
    private IHongdaCountryService hongdaCountryService;

    @Operation(summary = "查询国别信息列表", description = "分页查询国别信息列表")
    @PreAuthorize("@ss.hasPermi('content:country:list')")
    @GetMapping("/list")
    public TableDataInfo list(HongdaCountry hongdaCountry)
    {
        startPage();
        List<HongdaCountry> list = hongdaCountryService.selectHongdaCountryList(hongdaCountry);
        return getDataTable(list);
    }

    @Operation(summary = "导出国别信息列表", description = "导出国别信息数据到Excel")
    @PreAuthorize("@ss.hasPermi('content:country:export')")
    @Log(title = "国别信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HongdaCountry hongdaCountry)
    {
        List<HongdaCountry> list = hongdaCountryService.selectHongdaCountryList(hongdaCountry);
        ExcelUtil<HongdaCountry> util = new ExcelUtil<HongdaCountry>(HongdaCountry.class);
        util.exportExcel(response, list, "国别信息数据");
    }

    @Operation(summary = "获取国别信息详细信息", description = "根据ID获取国别信息详细信息")
    @PreAuthorize("@ss.hasPermi('content:country:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@Parameter(description = "国别信息ID") @PathVariable("id") Long id)
    {
        return success(hongdaCountryService.selectHongdaCountryById(id));
    }

    @Operation(summary = "新增国别信息", description = "新增国别信息")
    @PreAuthorize("@ss.hasPermi('content:country:add')")
    @Log(title = "国别信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HongdaCountry hongdaCountry)
    {
        hongdaCountry.setCreateBy(getUsername());
        return toAjax(hongdaCountryService.insertHongdaCountry(hongdaCountry));
    }

    @Operation(summary = "修改国别信息", description = "修改国别信息")
    @PreAuthorize("@ss.hasPermi('content:country:edit')")
    @Log(title = "国别信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HongdaCountry hongdaCountry)
    {
        hongdaCountry.setUpdateBy(getUsername());
        return toAjax(hongdaCountryService.updateHongdaCountry(hongdaCountry));
    }

    @Operation(summary = "删除国别信息", description = "根据ID数组删除国别信息")
    @PreAuthorize("@ss.hasPermi('content:country:remove')")
    @Log(title = "国别信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@Parameter(description = "国别信息ID数组") @PathVariable Long[] ids)
    {
        return toAjax(hongdaCountryService.deleteHongdaCountryByIds(ids));
    }

    /**
     * 获取所有国别信息的精简列表 (用于下拉框)
     */
    @Operation(summary = "获取所有国别信息", description = "获取所有国别信息的精简列表，用于下拉框数据")
    @PreAuthorize("@ss.hasPermi('content:country:list')")
    @GetMapping("/all") // 【【【 核心修复点 】】】
    public AjaxResult listAll(HongdaCountry hongdaCountry) {
        List<HongdaCountry> list = hongdaCountryService.selectHongdaCountryList(hongdaCountry);
        return success(list);
    }
}