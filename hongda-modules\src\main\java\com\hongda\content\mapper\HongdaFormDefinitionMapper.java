package com.hongda.content.mapper;

import com.hongda.content.domain.HongdaFormDefinition;

/**
 * 表单定义Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
public interface HongdaFormDefinitionMapper 
{
    /**
     * 根据活动ID查询表单定义
     * 
     * @param eventId 活动ID
     * @return 表单定义
     */
    public HongdaFormDefinition selectFormDefinitionByEventId(Long eventId);

    /**
     * 新增表单定义
     * 
     * @param formDefinition 表单定义
     * @return 结果
     */
    public int insertFormDefinition(HongdaFormDefinition formDefinition);

    /**
     * 修改表单定义
     * 
     * @param formDefinition 表单定义
     * @return 结果
     */
    public int updateFormDefinition(HongdaFormDefinition formDefinition);

    /**
     * 根据活动ID删除表单定义
     * 
     * @param eventId 活动ID
     * @return 结果
     */
    public int deleteFormDefinitionByEventId(Long eventId);
} 