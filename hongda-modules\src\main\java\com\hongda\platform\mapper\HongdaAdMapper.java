package com.hongda.platform.mapper;

import java.util.List;
import com.hongda.platform.domain.HongdaAd;
import com.hongda.wxapp.domain.vo.AdVO;

/**
 * 广告管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface HongdaAdMapper 
{
    /**
     * 查询广告管理
     * 
     * @param id 广告管理主键
     * @return 广告管理
     */
    public HongdaAd selectHongdaAdById(Long id);

    /**
     * 查询广告管理列表
     * 
     * @param hongdaAd 广告管理
     * @return 广告管理集合
     */
    public List<HongdaAd> selectHongdaAdList(HongdaAd hongdaAd);

    /**
     * 查询活动推广广告列表（带活动信息关联）
     * 
     * @param positionCode 广告位代码
     * @return 活动推广广告集合
     */
    public List<AdVO> selectEventPromoAdList(String positionCode);

    /**
     * 新增广告管理
     * 
     * @param hongdaAd 广告管理
     * @return 结果
     */
    public int insertHongdaAd(HongdaAd hongdaAd);

    /**
     * 修改广告管理
     * 
     * @param hongdaAd 广告管理
     * @return 结果
     */
    public int updateHongdaAd(HongdaAd hongdaAd);

    /**
     * 删除广告管理
     * 
     * @param id 广告管理主键
     * @return 结果
     */
    public int deleteHongdaAdById(Long id);

    /**
     * 批量删除广告管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHongdaAdByIds(Long[] ids);

    /**
     * 根据活动ID删除广告
     * 
     * @param eventId 活动ID
     * @return 结果
     */
    public int deleteAdByEventId(Long eventId);

    /**
     * 根据活动ID查询广告
     * 
     * @param eventId 活动ID
     * @return 广告对象
     */
    public HongdaAd selectAdByEventId(Long eventId);
}
