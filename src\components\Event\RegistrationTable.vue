<template>
  <el-table 
    v-loading="loading" 
    :data="registrationList" 
    border
    @selection-change="handleSelectionChange"
    style="width: 100%;"
  >
    <el-table-column type="selection" width="55" align="center" />
    <el-table-column label="序号" type="index" width="60" align="center" />
    <el-table-column label="用户昵称" width="200" align="center">
      <template #default="scope">
        <div style="display: flex; align-items: center; justify-content: center;">
          <el-avatar 
            :src="scope.row.userInfo?.avatarUrl" 
            :size="32" 
            style="margin-right: 10px;"
          >
            <el-icon><User /></el-icon>
          </el-avatar>
          <div>
            <div style="font-weight: 500;">{{ scope.row.userInfo?.nickname || '未设置昵称' }}</div>
            <div style="font-size: 12px; color: #999;">ID: {{ scope.row.userId }}</div>
          </div>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="联系方式" width="150" align="center">
      <template #default="scope">
        <span>{{ scope.row.userInfo?.phone || '未绑定' }}</span>
      </template>
    </el-table-column>
    <el-table-column label="报名时间" align="center" prop="registrationTime" width="180">
      <template #default="scope">
        <span>{{ parseTime(scope.row.registrationTime, '{y}-{m}-{d}') }}</span>
      </template>
    </el-table-column>
    <el-table-column label="状态" width="100" align="center">
      <template #default="scope">
        <el-tag :type="scope.row.status === 0 ? 'success' : 'info'" size="small">
          {{ scope.row.status === 0 ? '已报名' : '已取消' }}
        </el-tag>
      </template>
    </el-table-column>
      
    <!-- 动态报名表单字段列 -->
    <el-table-column 
      v-for="header in dynamicTableHeaders" 
      :key="header.prop" 
      :prop="header.prop" 
      :label="header.label" 
      :width="getColumnWidth(header.prop)"
      align="center"
      show-overflow-tooltip
    >
      <template #default="scope">
        <span>{{ getFormFieldValue(scope.row, header.prop) }}</span>
      </template>
    </el-table-column>
      
    <el-table-column label="操作" width="150" align="center" fixed="right">
      <template #default="scope">
        <el-button 
          link 
          type="success" 
          size="small" 
          @click="handleEdit(scope.row)" 
          v-hasPermi="['data:registration:edit']"
        >修改</el-button>
        <el-button 
          link 
          type="danger" 
          size="small" 
          @click="handleDelete(scope.row)" 
          v-hasPermi="['data:registration:remove']"
        >删除</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup>
import { User } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  registrationList: {
    type: Array,
    default: () => []
  },
  dynamicTableHeaders: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['selection-change', 'edit', 'delete'])

// Methods
const handleSelectionChange = (selection) => {
  emit('selection-change', selection)
}

const handleEdit = (row) => {
  emit('edit', row)
}

const handleDelete = (row) => {
  emit('delete', row)
}

// Utility functions
const parseTime = (time, pattern) => {
  if (!time) return ''
  const date = new Date(time)
  const format = {
    y: date.getFullYear(),
    m: String(date.getMonth() + 1).padStart(2, '0'),
    d: String(date.getDate()).padStart(2, '0')
  }
  return pattern.replace(/{(y|m|d)}/g, (match, key) => format[key])
}

const getFormFieldValue = (row, fieldName) => {
  try {
    const formData = typeof row.formData === 'string' ? JSON.parse(row.formData) : row.formData;
    const value = formData?.[fieldName];
    
    if (value === null || value === undefined || value === '') {
      return '未填写';
    }
    
    // 处理数组类型（如多选框）
    if (Array.isArray(value)) {
      return value.length > 0 ? value.join(', ') : '未选择';
    }
    
    // 处理布尔类型（如开关）
    if (typeof value === 'boolean') {
      return value ? '是' : '否';
    }
    
    // 处理对象类型
    if (typeof value === 'object') {
      return JSON.stringify(value);
    }
    
    return String(value);
  } catch (error) {
    console.error('解析表单数据失败:', error);
    return '数据错误';
  }
}

const getColumnWidth = (fieldName) => {
  // 根据字段名或类型设置不同的宽度
  const fieldLower = fieldName.toLowerCase();
  
  // 电话、手机号等较长的字段
  if (fieldLower.includes('phone') || fieldLower.includes('电话') || 
      fieldLower.includes('手机') || fieldLower.includes('联系')) {
    return '150';
  }
  
  // 姓名、昵称等较短的字段
  if (fieldLower.includes('name') || fieldLower.includes('姓名') || 
      fieldLower.includes('昵称') || fieldLower.includes('nickname')) {
    return '120';
  }
  
  // 地址、详细信息等较长的字段
  if (fieldLower.includes('address') || fieldLower.includes('地址') || 
      fieldLower.includes('详细') || fieldLower.includes('备注') || 
      fieldLower.includes('remark') || fieldLower.includes('description')) {
    return '200';
  }
  
  // 默认宽度
  return '150';
}
</script>
