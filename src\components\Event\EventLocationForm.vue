<template>
  <el-card shadow="never" class="form-card">
    <template #header>
      <div class="card-header">
        <span class="card-title">地点设置</span>
      </div>
    </template>
    
    <el-form-item label="活动地点" prop="province">
      <el-cascader
        v-model="selectedLocation"
        :options="locationOptions"
        placeholder="请选择省市区"
        style="width: 100%"
        @change="handleLocationChange"
        clearable
      />
    </el-form-item>
    
    <el-form-item label="详细地址" prop="addressDetail">
      <el-input
        v-model="form.addressDetail"
        placeholder="请输入街道、楼栋、门牌号等详细地址"
        maxlength="100"
        show-word-limit
      />
    </el-form-item>
  </el-card>
</template>

<script setup>
// Props
const props = defineProps({
  form: {
    type: Object,
    required: true
  },
  selectedLocation: {
    type: Array,
    default: () => []
  },
  locationOptions: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:form', 'location-change'])

// Methods
const handleLocationChange = (value) => {
  emit('location-change', value)
}
</script>

<style scoped>
/* 表单卡片样式 */
.form-card {
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.form-card :deep(.el-card__header) {
  padding: 16px 20px;
  background-color: #fafafa;
  border-bottom: 1px solid #e4e7ed;
}

.form-card :deep(.el-card__body) {
  padding: 24px 20px;
}

/* 卡片标题样式 */
.card-header {
  display: flex;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}
</style>
