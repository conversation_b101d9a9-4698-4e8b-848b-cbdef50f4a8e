package com.hongda.content.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.hongda.content.mapper.HongdaTagMapper;
import com.hongda.content.domain.HongdaTag;
import com.hongda.content.service.IHongdaTagService;

/**
 * 资讯分类标签Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
@Service
public class HongdaTagServiceImpl implements IHongdaTagService 
{
    @Autowired
    private HongdaTagMapper hongdaTagMapper;

    /**
     * 查询资讯分类标签
     * 
     * @param id 资讯分类标签主键
     * @return 资讯分类标签
     */
    @Override
    public HongdaTag selectHongdaTagById(Long id)
    {
        return hongdaTagMapper.selectHongdaTagById(id);
    }

    /**
     * 查询资讯分类标签列表
     * 
     * @param hongdaTag 资讯分类标签
     * @return 资讯分类标签
     */
    @Override
    public List<HongdaTag> selectHongdaTagList(HongdaTag hongdaTag)
    {
        return hongdaTagMapper.selectHongdaTagList(hongdaTag);
    }

    /**
     * 新增资讯分类标签
     * 
     * @param hongdaTag 资讯分类标签
     * @return 结果
     */
    @Override
    public int insertHongdaTag(HongdaTag hongdaTag)
    {
        return hongdaTagMapper.insertHongdaTag(hongdaTag);
    }

    /**
     * 修改资讯分类标签
     * 
     * @param hongdaTag 资讯分类标签
     * @return 结果
     */
    @Override
    public int updateHongdaTag(HongdaTag hongdaTag)
    {
        return hongdaTagMapper.updateHongdaTag(hongdaTag);
    }

    /**
     * 批量删除资讯分类标签
     * 
     * @param ids 需要删除的资讯分类标签主键
     * @return 结果
     */
    @Override
    public int deleteHongdaTagByIds(Long[] ids)
    {
        return hongdaTagMapper.deleteHongdaTagByIds(ids);
    }

    /**
     * 删除资讯分类标签信息
     * 
     * @param id 资讯分类标签主键
     * @return 结果
     */
    @Override
    public int deleteHongdaTagById(Long id)
    {
        return hongdaTagMapper.deleteHongdaTagById(id);
    }
}
