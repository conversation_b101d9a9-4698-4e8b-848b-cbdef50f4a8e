<template>
  <el-form 
    :model="queryParams" 
    ref="queryRef" 
    :inline="true" 
    v-show="showSearch" 
    label-width="120px"
  >
    <!-- 第一行：活动名称和活动地点 -->
    <el-form-item label="活动名称" prop="title">
      <el-input
        v-model="queryParams.title"
        placeholder="请输入活动名称"
        clearable
        style="width: 240px"
        @keyup.enter="handleQuery"
      />
    </el-form-item>
    <el-form-item label="活动地点" prop="location">
      <el-input
        v-model="queryParams.location"
        placeholder="请输入活动地点"
        clearable
        style="width: 240px"
        @keyup.enter="handleQuery"
      />
    </el-form-item>

    <!-- 第二行：报名状态、搜索和重置按钮 -->
    <el-form-item label="报名状态" prop="registrationStatus">
      <el-select
        v-model="queryParams.registrationStatus"
        placeholder="请选择报名状态"
        clearable
        style="width: 120px"
      >
        <el-option label="未开始" value="0" />
        <el-option label="报名中" value="1" />
        <el-option label="已结束" value="2" />
      </el-select>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
      <el-button icon="Refresh" @click="handleReset">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup>
import { ref, getCurrentInstance } from 'vue'

const { proxy } = getCurrentInstance()

// Props
const props = defineProps({
  queryParams: {
    type: Object,
    required: true
  },
  showSearch: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits(['search', 'reset', 'update:queryParams', 'update:showSearch'])

// Refs
const queryRef = ref()

// Methods
const handleQuery = () => {
  emit('search')
}

const handleReset = () => {
  proxy.resetForm("queryRef")
  emit('reset')
}
</script>
