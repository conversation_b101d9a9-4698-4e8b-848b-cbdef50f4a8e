package com.hongda.content.mapper;

import java.util.List;
import com.hongda.content.domain.HongdaArticle;
import com.hongda.content.domain.HongdaArticleTagRelation;
import org.apache.ibatis.annotations.Param;

/**
 * 资讯文章Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface HongdaArticleMapper 
{
    /**
     * 查询资讯文章
     * 
     * @param id 资讯文章主键
     * @return 资讯文章
     */
    public HongdaArticle selectHongdaArticleById(Long id);

    /**
     * 查询资讯文章列表
     * 
     * @param hongdaArticle 资讯文章
     * @return 资讯文章集合
     */
    public List<HongdaArticle> selectHongdaArticleList(HongdaArticle hongdaArticle);

    /**
     * 新增资讯文章
     * 
     * @param hongdaArticle 资讯文章
     * @return 结果
     */
    public int insertHongdaArticle(HongdaArticle hongdaArticle);

    /**
     * 修改资讯文章
     * 
     * @param hongdaArticle 资讯文章
     * @return 结果
     */
    public int updateHongdaArticle(HongdaArticle hongdaArticle);

    /**
     * 删除资讯文章
     * 
     * @param id 资讯文章主键
     * @return 结果
     */
    public int deleteHongdaArticleById(Long id);

    /**
     * 批量删除资讯文章
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHongdaArticleByIds(Long[] ids);

    /**
     * 根据文章ID查询其关联的所有标签ID
     * @param articleId 文章ID
     * @return 标签ID列表
     */
    public List<Long> selectTagIdsByArticleId(Long articleId);

    /**
     * 批量新增文章与标签的关联
     * @param articleTagList 关联列表
     * @return 结果
     */
    public int batchArticleTag(List<HongdaArticleTagRelation> articleTagList);

    /**
     * 根据文章ID删除文章与标签的关联
     * @param articleId 文章ID
     * @return 结果
     */
    public int deleteArticleTagByArticleId(Long articleId);

    /**
     * 将指定ID文章的阅读量（view_count）字段加1
     *
     * @param articleId 文章ID
     * @return 影响的行数，通常为1
     */
    int incrementViewCount(@Param("articleId") Long articleId);
}
