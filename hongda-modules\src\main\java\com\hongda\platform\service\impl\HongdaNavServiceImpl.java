package com.hongda.platform.service.impl;

import java.util.List;
import com.hongda.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.hongda.platform.mapper.HongdaNavMapper;
import com.hongda.platform.domain.HongdaNav;
import com.hongda.platform.service.IHongdaNavService;

/**
 * 导航配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Service
public class HongdaNavServiceImpl implements IHongdaNavService 
{
    @Autowired
    private HongdaNavMapper hongdaNavMapper;

    /**
     * 查询导航配置
     * 
     * @param id 导航配置主键
     * @return 导航配置
     */
    @Override
    public HongdaNav selectHongdaNavById(Long id)
    {
        return hongdaNavMapper.selectHongdaNavById(id);
    }

    /**
     * 查询导航配置列表
     * 
     * @param hongdaNav 导航配置
     * @return 导航配置
     */
    @Override
    public List<HongdaNav> selectHongdaNavList(HongdaNav hongdaNav)
    {
        return hongdaNavMapper.selectHongdaNavList(hongdaNav);
    }

    /**
     * 新增导航配置
     * 
     * @param hongdaNav 导航配置
     * @return 结果
     */
    @Override
    public int insertHongdaNav(HongdaNav hongdaNav)
    {
        hongdaNav.setCreateTime(DateUtils.getNowDate());
        return hongdaNavMapper.insertHongdaNav(hongdaNav);
    }

    /**
     * 修改导航配置
     * 
     * @param hongdaNav 导航配置
     * @return 结果
     */
    @Override
    public int updateHongdaNav(HongdaNav hongdaNav)
    {
        hongdaNav.setUpdateTime(DateUtils.getNowDate());
        return hongdaNavMapper.updateHongdaNav(hongdaNav);
    }

    /**
     * 批量删除导航配置
     * 
     * @param ids 需要删除的导航配置主键
     * @return 结果
     */
    @Override
    public int deleteHongdaNavByIds(Long[] ids)
    {
        return hongdaNavMapper.deleteHongdaNavByIds(ids);
    }

    /**
     * 删除导航配置信息
     * 
     * @param id 导航配置主键
     * @return 结果
     */
    @Override
    public int deleteHongdaNavById(Long id)
    {
        return hongdaNavMapper.deleteHongdaNavById(id);
    }
}
