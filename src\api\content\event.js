import request from '@/utils/request'

// 查询活动管理列表
export function listEvent(query) {
  return request({
    url: '/content/event/list',
    method: 'get',
    params: query
  })
}

// 查询活动管理详细
export function getEvent(id) {
  return request({
    url: '/content/event/' + id,
    method: 'get'
  })
}

// 新增活动管理
export function addEvent(data) {
  return request({
    url: '/content/event',
    method: 'post',
    data: data
  })
}

// 修改活动管理
export function updateEvent(data) {
  return request({
    url: '/content/event',
    method: 'put',
    data: data
  })
}

// 删除活动管理
export function delEvent(id) {
  return request({
    url: '/content/event/' + id,
    method: 'delete'
  })
}

// 修改活动热门状态
export function changeEventHotStatus(data) {
  return request({
    url: '/content/event/changeHotStatus',
    method: 'put',
    data: data
  }).then(response => {
    return response;
  }).catch(error => {
    throw error;
  });
}

// 切换活动推广状态
export function changeEventPromotionStatus(data) {
  return request({
    url: '/content/event/changePromotionStatus',
    method: 'put',
    data: data
  })
}