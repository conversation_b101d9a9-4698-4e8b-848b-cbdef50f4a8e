<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongda.data.mapper.HongdaEventRegistrationMapper">

    <!--
      ResultMap 1: 用于“报名数据”主页面的关联查询 (扁平化结构)
      这个ResultMap将关联查询出的活动名称和用户昵称，作为HongdaEventRegistration对象的直接属性。
    -->
    <resultMap type="HongdaEventRegistration" id="HongdaEventRegistrationResult">
        <result property="id"    column="id"    />
        <result property="eventId"    column="event_id"    />
        <result property="userId"    column="user_id"    />
        <result property="registrationTime"    column="registration_time"    />
        <result property="formData"    column="form_data"    />
        <result property="status"    column="status"    />
        <!-- 关联查询出的字段 -->
        <result property="eventName"    column="event_name"    />
        <result property="userNickname"    column="user_nickname"    />
    </resultMap>

    <!--
      ResultMap 2: 用于"活动管理"弹窗/展开行的关联查询 (嵌套对象结构)
      【核心修正】: 移除了重复的 registrationTime 映射。
    -->
    <resultMap type="HongdaEventRegistration" id="HongdaEventRegistrationUserResult">
        <id     property="id"       column="id"      />
        <result property="eventId"  column="event_id" />
        <result property="userId"   column="user_id"  />
        <result property="registrationTime" column="registration_time" />
        <result property="formData" column="form_data" />
        <result property="status"   column="status"   />
        <!-- 用于导出的扁平化字段 -->
        <result property="eventName" column="event_name" />
        <result property="userNickname" column="nickname" />
        <result property="userPhone" column="phone" />
        <!-- 关联 hongdaUser 对象 -->
        <association property="hongdaUser" javaType="HongdaUser">
            <result property="nickname"   column="nickname"   />
            <result property="avatarUrl"  column="avatar_url" />
            <result property="phone"      column="phone"      />
        </association>
    </resultMap>

    <!-- SQL片段: 用于“报名数据”主页面的关联查询 -->
    <sql id="selectHongdaEventRegistrationVo">
        select
            r.id, r.event_id, r.user_id, r.registration_time, r.form_data, r.status,
            e.title as event_name,
            u.nickname as user_nickname
        from hongda_event_registration r
                 left join hongda_event e on r.event_id = e.id
                 left join hongda_user u on r.user_id = u.id
    </sql>

    <!-- 查询1: “报名数据”主页面的列表查询 (支持分页和多条件搜索) -->
    <select id="selectHongdaEventRegistrationList" parameterType="HongdaEventRegistration" resultMap="HongdaEventRegistrationResult">
        <include refid="selectHongdaEventRegistrationVo"/>
        <where>
            <if test="eventId != null "> and r.event_id = #{eventId}</if>
            <if test="userId != null "> and r.user_id = #{userId}</if>
            <if test="eventName != null and eventName != ''"> and e.title like concat('%', #{eventName}, '%')</if>
            <if test="userNickname != null and userNickname != ''"> and u.nickname like concat('%', #{userNickname}, '%')</if>
            <if test="status != null "> and r.status = #{status}</if>
        </where>
    </select>

    <!-- 查询2: “报名数据”主页面获取单条详情 (复用SQL片段) -->
    <select id="selectHongdaEventRegistrationById" parameterType="Long" resultMap="HongdaEventRegistrationResult">
        <include refid="selectHongdaEventRegistrationVo"/>
        where r.id = #{id}
    </select>

    <!--
      查询3: “活动管理”弹窗/展开行的列表查询 (支持分页)
      【核心修正】: 移除了 select 语句中重复的 r.registration_time 字段。
    -->
    <select id="selectRegistrationWithUserList" parameterType="HongdaEventRegistration" resultMap="HongdaEventRegistrationUserResult">
        select
        r.id, r.event_id, r.user_id, r.registration_time, r.form_data, r.status,
        u.nickname, u.avatar_url, u.phone,
        e.title as event_name
        from
        hongda_event_registration r
        left join
        hongda_user u on r.user_id = u.id
        left join
        hongda_event e on r.event_id = e.id
        <where>
            <if test="eventId != null">and r.event_id = #{eventId}</if>
        </where>
        order by r.registration_time desc
    </select>

    <!-- 插入操作 -->
    <insert id="insertHongdaEventRegistration" parameterType="HongdaEventRegistration" useGeneratedKeys="true" keyProperty="id">
        insert into hongda_event_registration
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="eventId != null">event_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="registrationTime != null">registration_time,</if>
            <if test="formData != null and formData != ''">form_data,</if>
            <if test="status != null">status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="eventId != null">#{eventId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="registrationTime != null">#{registrationTime},</if>
            <if test="formData != null and formData != ''">#{formData},</if>
            <if test="status != null">#{status},</if>
        </trim>
    </insert>

    <!-- 更新操作 -->
    <update id="updateHongdaEventRegistration" parameterType="HongdaEventRegistration">
        update hongda_event_registration
        <trim prefix="SET" suffixOverrides=",">
            <if test="eventId != null">event_id = #{eventId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="registrationTime != null">registration_time = #{registrationTime},</if>
            <if test="formData != null and formData != ''">form_data = #{formData},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <!-- 删除操作 -->
    <delete id="deleteHongdaEventRegistrationById" parameterType="Long">
        delete from hongda_event_registration where id = #{id}
    </delete>

    <delete id="deleteHongdaEventRegistrationByIds" parameterType="String">
        delete from hongda_event_registration where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据活动ID删除报名记录 -->
    <delete id="deleteRegistrationByEventId" parameterType="Long">
        delete from hongda_event_registration where event_id = #{eventId}
    </delete>

    <!-- 根据手机号查找用户ID -->
    <select id="selectUserIdByPhone" parameterType="String" resultType="Long">
        select id from hongda_user 
        where phone = #{phone} 
        and status = 0 
        and is_deleted = 0
    </select>

</mapper>
