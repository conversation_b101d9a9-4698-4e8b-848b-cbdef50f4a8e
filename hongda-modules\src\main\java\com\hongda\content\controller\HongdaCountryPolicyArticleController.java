package com.hongda.content.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hongda.common.annotation.Log;
import com.hongda.common.core.controller.BaseController;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.common.enums.BusinessType;
import com.hongda.content.domain.HongdaCountryPolicyArticle;
import com.hongda.content.service.IHongdaCountryPolicyArticleService;
import com.hongda.common.utils.poi.ExcelUtil;
import com.hongda.common.core.page.TableDataInfo;

/**
 * 国别政策管理Controller
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@RestController
@RequestMapping("/content/countryPolicy")
public class HongdaCountryPolicyArticleController extends BaseController
{
    @Autowired
    private IHongdaCountryPolicyArticleService hongdaCountryPolicyArticleService;

    /**
     * 查询国别政策管理列表
     */
    @PreAuthorize("@ss.hasPermi('content:countryPolicy:list')")
    @GetMapping("/list")
    public TableDataInfo list(HongdaCountryPolicyArticle hongdaCountryPolicyArticle)
    {
        startPage();
        List<HongdaCountryPolicyArticle> list = hongdaCountryPolicyArticleService.selectHongdaCountryPolicyArticleList(hongdaCountryPolicyArticle);
        return getDataTable(list);
    }

    /**
     * 导出国别政策管理列表
     */
    @PreAuthorize("@ss.hasPermi('content:countryPolicy:export')")
    @Log(title = "国别政策管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HongdaCountryPolicyArticle hongdaCountryPolicyArticle)
    {
        List<HongdaCountryPolicyArticle> list = hongdaCountryPolicyArticleService.selectHongdaCountryPolicyArticleList(hongdaCountryPolicyArticle);
        ExcelUtil<HongdaCountryPolicyArticle> util = new ExcelUtil<HongdaCountryPolicyArticle>(HongdaCountryPolicyArticle.class);
        util.exportExcel(response, list, "国别政策管理数据");
    }

    /**
     * 获取国别政策管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('content:countryPolicy:query')")
    @GetMapping(value = "/{articleId}")
    public AjaxResult getInfo(@PathVariable("articleId") Long articleId)
    {
        return success(hongdaCountryPolicyArticleService.selectHongdaCountryPolicyArticleByArticleId(articleId));
    }

    /**
     * 新增国别政策管理
     */
    @PreAuthorize("@ss.hasPermi('content:countryPolicy:add')")
    @Log(title = "国别政策管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HongdaCountryPolicyArticle hongdaCountryPolicyArticle)
    {
        return toAjax(hongdaCountryPolicyArticleService.insertHongdaCountryPolicyArticle(hongdaCountryPolicyArticle));
    }

    /**
     * 修改国别政策管理
     */
    @PreAuthorize("@ss.hasPermi('content:countryPolicy:edit')")
    @Log(title = "国别政策管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HongdaCountryPolicyArticle hongdaCountryPolicyArticle)
    {
        return toAjax(hongdaCountryPolicyArticleService.updateHongdaCountryPolicyArticle(hongdaCountryPolicyArticle));
    }

    /**
     * 删除国别政策管理
     */
    @PreAuthorize("@ss.hasPermi('content:countryPolicy:remove')")
    @Log(title = "国别政策管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{articleIds}")
    public AjaxResult remove(@PathVariable Long[] articleIds)
    {
        return toAjax(hongdaCountryPolicyArticleService.deleteHongdaCountryPolicyArticleByArticleIds(articleIds));
    }
}
