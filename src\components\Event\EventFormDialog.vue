<template>
  <el-dialog 
    :title="title" 
    v-model="open" 
    width="1000px" 
    append-to-body
  >
    <el-tabs v-model="activeTab">
      <el-tab-pane label="基本信息" name="basicInfo">
        <EventBasicForm 
          ref="eventFormRef"
          v-model:form="form"
          :rules="rules"
          :selectedLocation="selectedLocation"
          :locationOptions="locationOptions"
          @location-change="handleLocationChange"
        />
      </el-tab-pane>
      
      <el-tab-pane label="报名表单设计" name="formDesign">
        <fc-designer 
          ref="designer" 
          :rule="currentFormDefinition" 
          :option="designerOption"
        />
      </el-tab-pane>
    </el-tabs>
    
    <template #footer>
      <div class="dialog-footer">
        <div class="footer-left"></div>
        <div class="footer-right">
          <el-button size="large" @click="handleCancel">取 消</el-button>
          <el-button type="primary" size="large" @click="handleConfirm">
            确 定
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import EventBasicForm from './EventBasicForm.vue'

// Props
const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: ''
  },
  form: {
    type: Object,
    required: true
  },
  rules: {
    type: Object,
    required: true
  },
  selectedLocation: {
    type: Array,
    default: () => []
  },
  locationOptions: {
    type: Array,
    default: () => []
  },
  currentFormDefinition: {
    type: Array,
    default: () => []
  },
  designerOption: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits([
  'update:open',
  'update:form',
  'location-change',
  'confirm',
  'cancel'
])

// Refs
const activeTab = ref('basicInfo')
const eventFormRef = ref()
const designer = ref()

// Methods
const handleLocationChange = (value) => {
  emit('location-change', value)
}

const handleConfirm = () => {
  emit('confirm', {
    activeTab: activeTab.value,
    eventFormRef: eventFormRef.value,
    designer: designer.value
  })
}

const handleCancel = () => {
  emit('cancel')
}

// Watch for open prop changes
watch(() => props.open, (newVal) => {
  if (newVal) {
    activeTab.value = 'basicInfo'
  }
})

// Expose refs for parent component
defineExpose({
  designer,
  eventFormRef,
  activeTab
})
</script>

<style scoped>
/* 对话框底部样式 */
.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}

.footer-left {
  flex: 1;
}

.footer-right {
  display: flex;
  gap: 12px;
}
</style>
