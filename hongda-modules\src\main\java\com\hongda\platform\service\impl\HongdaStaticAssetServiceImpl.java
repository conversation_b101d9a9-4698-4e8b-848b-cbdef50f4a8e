package com.hongda.platform.service.impl;

import java.util.List;
import com.hongda.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.hongda.platform.mapper.HongdaStaticAssetMapper;
import com.hongda.platform.domain.HongdaStaticAsset;
import com.hongda.platform.service.IHongdaStaticAssetService;

/**
 * 小程序静态资源配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Service
public class HongdaStaticAssetServiceImpl implements IHongdaStaticAssetService 
{
    @Autowired
    private HongdaStaticAssetMapper hongdaStaticAssetMapper;

    /**
     * 查询小程序静态资源配置
     * 
     * @param id 小程序静态资源配置主键
     * @return 小程序静态资源配置
     */
    @Override
    public HongdaStaticAsset selectHongdaStaticAssetById(Long id)
    {
        return hongdaStaticAssetMapper.selectHongdaStaticAssetById(id);
    }

    /**
     * 查询小程序静态资源配置列表
     * 
     * @param hongdaStaticAsset 小程序静态资源配置
     * @return 小程序静态资源配置
     */
    @Override
    public List<HongdaStaticAsset> selectHongdaStaticAssetList(HongdaStaticAsset hongdaStaticAsset)
    {
        return hongdaStaticAssetMapper.selectHongdaStaticAssetList(hongdaStaticAsset);
    }

    /**
     * 新增小程序静态资源配置
     * 
     * @param hongdaStaticAsset 小程序静态资源配置
     * @return 结果
     */
    @Override
    public int insertHongdaStaticAsset(HongdaStaticAsset hongdaStaticAsset)
    {
        hongdaStaticAsset.setCreateTime(DateUtils.getNowDate());
        return hongdaStaticAssetMapper.insertHongdaStaticAsset(hongdaStaticAsset);
    }

    /**
     * 修改小程序静态资源配置
     * 
     * @param hongdaStaticAsset 小程序静态资源配置
     * @return 结果
     */
    @Override
    public int updateHongdaStaticAsset(HongdaStaticAsset hongdaStaticAsset)
    {
        hongdaStaticAsset.setUpdateTime(DateUtils.getNowDate());
        return hongdaStaticAssetMapper.updateHongdaStaticAsset(hongdaStaticAsset);
    }

    /**
     * 批量删除小程序静态资源配置
     * 
     * @param ids 需要删除的小程序静态资源配置主键
     * @return 结果
     */
    @Override
    public int deleteHongdaStaticAssetByIds(Long[] ids)
    {
        return hongdaStaticAssetMapper.deleteHongdaStaticAssetByIds(ids);
    }

    /**
     * 删除小程序静态资源配置信息
     * 
     * @param id 小程序静态资源配置主键
     * @return 结果
     */
    @Override
    public int deleteHongdaStaticAssetById(Long id)
    {
        return hongdaStaticAssetMapper.deleteHongdaStaticAssetById(id);
    }
}
