package com.hongda.wxapp.domain;

import java.util.Date;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.hongda.common.annotation.Excel;
import com.hongda.common.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 协议同意留痕记录对象 hd_policy_accept_log
 * 
 * <AUTHOR>
 * @date 2025-01-22
 */
@Schema(description = "协议同意留痕记录")
public class PolicyAcceptLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 记录ID */
    @Schema(description = "记录ID", example = "1")
    private Long id;

    /** 用户ID */
    @Excel(name = "用户ID")
    @Schema(description = "用户ID", example = "123")
    private Long userId;

    /** 协议类型 (user_agreement / privacy_policy) */
    @Excel(name = "协议类型")
    @Schema(description = "协议类型", example = "user_agreement", allowableValues = {"user_agreement", "privacy_policy"})
    private String policyType;

    /** 协议版本 */
    @Excel(name = "协议版本")
    @Schema(description = "协议版本", example = "1.0.0")
    private String policyVersion;

    /** 同意时间 */
    @Excel(name = "同意时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "同意时间")
    private Date acceptedAt;

    /** 用户IP地址 */
    @Excel(name = "用户IP地址")
    @Schema(description = "用户IP地址", example = "***********")
    private String ip;

    /** 用户代理信息 */
    @Excel(name = "用户代理信息")
    @Schema(description = "用户代理信息")
    private String userAgent;

    /** 文件名 (可选，增强留痕) */
    @Excel(name = "文件名")
    @Schema(description = "文件名", example = "user_agreement_v1.0.0.html")
    private String fileName;

    /** 内容哈希 (可选，增强留痕) */
    @Excel(name = "内容哈希")
    @Schema(description = "内容哈希", example = "a1b2c3d4e5f6...")
    private String contentHash;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    public void setPolicyType(String policyType) 
    {
        this.policyType = policyType;
    }

    public String getPolicyType() 
    {
        return policyType;
    }

    public void setPolicyVersion(String policyVersion) 
    {
        this.policyVersion = policyVersion;
    }

    public String getPolicyVersion() 
    {
        return policyVersion;
    }

    public void setAcceptedAt(Date acceptedAt) 
    {
        this.acceptedAt = acceptedAt;
    }

    public Date getAcceptedAt() 
    {
        return acceptedAt;
    }

    public void setIp(String ip) 
    {
        this.ip = ip;
    }

    public String getIp() 
    {
        return ip;
    }

    public void setUserAgent(String userAgent) 
    {
        this.userAgent = userAgent;
    }

    public String getUserAgent() 
    {
        return userAgent;
    }

    public void setFileName(String fileName) 
    {
        this.fileName = fileName;
    }

    public String getFileName() 
    {
        return fileName;
    }

    public void setContentHash(String contentHash) 
    {
        this.contentHash = contentHash;
    }

    public String getContentHash() 
    {
        return contentHash;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("userId", getUserId())
            .append("policyType", getPolicyType())
            .append("policyVersion", getPolicyVersion())
            .append("acceptedAt", getAcceptedAt())
            .append("ip", getIp())
            .append("userAgent", getUserAgent())
            .append("fileName", getFileName())
            .append("contentHash", getContentHash())
            .toString();
    }
}
