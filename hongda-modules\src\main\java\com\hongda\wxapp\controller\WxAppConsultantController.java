package com.hongda.wxapp.controller;

import com.hongda.common.core.controller.BaseController;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.platform.domain.HongdaConsultant;
import com.hongda.platform.service.IHongdaConsultantService;
import com.hongda.wxapp.domain.vo.ConsultantVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "小程序顾问接口", description = "小程序端联系顾问相关接口")
@RestController
@RequestMapping("/api/v1/consultant")
public class WxAppConsultantController extends BaseController {

    @Autowired
    private IHongdaConsultantService hongdaConsultantService;

    /**
     * 获取当前展示的顾问信息
     * 业务逻辑: 获取状态为“启用”且排序号最小的一条顾问信息
     */
    @Operation(summary = "获取当前展示的顾问信息", description = "获取状态为启用且排序最靠前的唯一顾问信息")
    @GetMapping("/display")
    public AjaxResult getDisplayConsultant() {
        // 调用Service层方法获取唯一的、应该被展示的顾问
        HongdaConsultant consultant = hongdaConsultantService.selectDisplayConsultant();

        if (consultant == null) {
            // 如果没有找到任何启用的顾问，可以返回成功但数据为空
            return AjaxResult.success(null);
        }

        // 将查询到的 Domain 对象转换为 VO 对象
        ConsultantVO vo = new ConsultantVO();
        BeanUtils.copyProperties(consultant, vo);

        // 返回成功结果
        return AjaxResult.success(vo);
    }
}