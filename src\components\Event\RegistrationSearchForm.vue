<template>
  <el-form 
    :model="queryParams" 
    ref="registrationQueryRef" 
    :inline="true" 
    label-width="80px" 
    class="mb15"
  >
    <el-form-item label="用户昵称" prop="nickname">
      <el-input
        v-model="queryParams.nickname"
        placeholder="请输入用户昵称"
        clearable
        style="width: 200px"
        @keyup.enter="handleSearch"
      />
    </el-form-item>
    <el-form-item label="联系方式" prop="phone">
      <el-input
        v-model="queryParams.phone"
        placeholder="请输入联系方式"
        clearable
        style="width: 200px"
        @keyup.enter="handleSearch"
      />
    </el-form-item>
    <el-form-item label="状态" prop="status">
      <el-select 
        v-model="queryParams.status" 
        placeholder="请选择状态" 
        clearable 
        style="width: 120px"
      >
        <el-option label="已报名" value="0" />
        <el-option label="已取消" value="1" />
      </el-select>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" icon="Search" @click="handleSearch">搜索</el-button>
      <el-button icon="Refresh" @click="handleReset">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup>
import { ref, getCurrentInstance } from 'vue'

const { proxy } = getCurrentInstance()

// Props
const props = defineProps({
  queryParams: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['search', 'reset', 'update:queryParams'])

// Refs
const registrationQueryRef = ref()

// Methods
const handleSearch = () => {
  emit('search')
}

const handleReset = () => {
  proxy.resetForm("registrationQueryRef")
  emit('reset')
}
</script>
