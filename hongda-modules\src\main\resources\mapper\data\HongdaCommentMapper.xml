<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongda.data.mapper.HongdaCommentMapper">

    <resultMap type="com.hongda.data.domain.HongdaComment" id="HongdaCommentResult">
        <result property="id"             column="id"               />
        <result property="userId"         column="user_id"          />
        <result property="relatedType"    column="related_type"     />
        <result property="relatedId"      column="related_id"       />
        <result property="parentId"       column="parent_id"        />
        <result property="replyToUserId"  column="reply_to_user_id"   />
        <result property="content"        column="content"          />
        <result property="status"         column="status"           />
        <result property="createTime"     column="create_time"      />
        <result property="updateTime"     column="update_time"      />
    </resultMap>

    <resultMap type="com.hongda.data.domain.vo.HongdaCommentVo" id="HongdaCommentVoResult" extends="HongdaCommentResult">
        <result property="nickname"        column="nickname"/>
        <result property="replyToNickname" column="reply_to_nickname"/>
        <result property="relatedTitle"    column="related_title"/>
        <result property="avatarUrl"       column="avatar_url"/>
    </resultMap>

    <select id="selectHongdaCommentList" parameterType="com.hongda.data.domain.HongdaComment" resultMap="HongdaCommentVoResult">
        SELECT
        c.id, c.user_id, c.related_type, c.related_id, c.parent_id, c.reply_to_user_id, c.content, c.status, c.create_time,
        u.nickname,
        u.avatar_url,
        ru.nickname AS reply_to_nickname,
        CASE
        WHEN c.related_type = 'article' THEN a.title
        WHEN c.related_type = 'event' THEN e.title
        ELSE '未知内容'
        END AS related_title
        FROM
        hongda_comment c
        LEFT JOIN
        hongda_user u ON c.user_id = u.id
        LEFT JOIN
        hongda_user ru ON c.reply_to_user_id = ru.id
        LEFT JOIN
        hongda_article a ON c.related_id = a.id AND c.related_type = 'article'
        LEFT JOIN
        hongda_event e ON c.related_id = e.id AND c.related_type = 'event'
        <where>
            <if test="relatedType != null and relatedType != ''">
                AND c.related_type = #{relatedType}
            </if>
            <if test="relatedId != null">
                AND c.related_id = #{relatedId}
            </if>
            <if test="status != null">
                AND c.status = #{status}
            </if>
            <if test="userId != null">
                AND c.user_id = #{userId}
            </if>
            <if test="parentId != null">
                AND c.parent_id = #{parentId}
            </if>
            <if test="relatedTitle != null and relatedTitle != ''">
                AND (a.title LIKE concat('%', #{relatedTitle}, '%') OR e.title LIKE concat('%', #{relatedTitle}, '%'))
            </if>
        </where>
    </select>

    <select id="selectHongdaCommentById" parameterType="java.lang.Long" resultMap="HongdaCommentVoResult">
        SELECT
            c.id, c.user_id, c.related_type, c.related_id, c.parent_id, c.reply_to_user_id, c.content, c.status, c.create_time,
            u.nickname,
            u.avatar_url,
            ru.nickname AS reply_to_nickname,
            CASE
                WHEN c.related_type = 'article' THEN a.title
                WHEN c.related_type = 'event' THEN e.title
                ELSE '未知内容'
                END AS related_title
        FROM
            hongda_comment c
                LEFT JOIN
            hongda_user u ON c.user_id = u.id
                LEFT JOIN
            hongda_user ru ON c.reply_to_user_id = ru.id
                LEFT JOIN
            hongda_article a ON c.related_id = a.id AND c.related_type = 'article'
                LEFT JOIN
            hongda_event e ON c.related_id = e.id AND c.related_type = 'event'
        WHERE c.id = #{id}
    </select>

    <insert id="insertHongdaComment" parameterType="com.hongda.data.domain.HongdaComment" useGeneratedKeys="true" keyProperty="id">
        insert into hongda_comment (
            user_id,
            related_id,
            related_type,
            parent_id,
            reply_to_user_id,
            content,
            status
        )
        values (
                   #{userId},
                   #{relatedId},
                   #{relatedType},
                   #{parentId},
                   #{replyToUserId},
                   #{content},
                   #{status}
               )
    </insert>

    <update id="updateHongdaComment" parameterType="com.hongda.data.domain.HongdaComment">
        update hongda_comment
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="replyToUserId != null">reply_to_user_id = #{replyToUserId},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

</mapper>