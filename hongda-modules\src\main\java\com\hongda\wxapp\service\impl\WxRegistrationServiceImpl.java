package com.hongda.wxapp.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.hongda.content.mapper.HongdaEventMapper;
import com.hongda.data.domain.HongdaEventRegistration;
import com.hongda.data.mapper.HongdaEventRegistrationMapper;
import com.hongda.wxapp.service.IWxRegistrationService;

/**
 * 微信小程序报名服务实现
 * 
 * <AUTHOR>
 */
@Service
public class WxRegistrationServiceImpl implements IWxRegistrationService
{
    @Autowired
    private HongdaEventRegistrationMapper registrationMapper;
    
    @Autowired
    private HongdaEventMapper hongdaEventMapper;

    /**
     * 检查用户是否已报名指定活动
     */
    @Override
    public boolean checkUserRegistration(Long eventId, Long userId)
    {
        if (eventId == null || userId == null)
        {
            return false;
        }
        
        HongdaEventRegistration registration = new HongdaEventRegistration();
        registration.setEventId(eventId);
        registration.setUserId(userId);
        registration.setStatus(0); // 只查询有效报名记录
        
        List<HongdaEventRegistration> list = registrationMapper.selectHongdaEventRegistrationList(registration);
        return list != null && !list.isEmpty();
    }

    /**
     * 根据活动ID和用户ID获取报名记录
     */
    @Override
    public HongdaEventRegistration getRegistrationByEventAndUser(Long eventId, Long userId)
    {
        if (eventId == null || userId == null)
        {
            return null;
        }
        
        HongdaEventRegistration registration = new HongdaEventRegistration();
        registration.setEventId(eventId);
        registration.setUserId(userId);
        registration.setStatus(0); // 只查询有效报名记录
        
        List<HongdaEventRegistration> list = registrationMapper.selectHongdaEventRegistrationList(registration);
        return (list != null && !list.isEmpty()) ? list.get(0) : null;
    }

    /**
     * 用户报名活动
     */
    @Override
    @Transactional
    public HongdaEventRegistration registerEvent(Long eventId, Long userId, String formData)
    {
        if (eventId == null || userId == null)
        {
            throw new RuntimeException("活动ID和用户ID不能为空");
        }
        
        // 检查是否已报名
        if (checkUserRegistration(eventId, userId))
        {
            throw new RuntimeException("您已报名此活动，请勿重复报名");
        }
        
        // 创建报名记录
        HongdaEventRegistration registration = new HongdaEventRegistration();
        registration.setEventId(eventId);
        registration.setUserId(userId);
        registration.setRegistrationTime(new Date());
        registration.setFormData(formData);
        registration.setStatus(0); // 已报名
        registration.setCreateTime(new Date());
        
        // 第一步：插入报名记录
        registrationMapper.insertHongdaEventRegistration(registration);
        
        // 第二步：同步更新活动表的报名计数
        hongdaEventMapper.incrementRegisteredCount(eventId);
        
        return registration;
    }

    /**
     * 取消报名
     */
    @Override
    @Transactional
    public boolean cancelRegistration(Long eventId, Long userId)
    {
        HongdaEventRegistration registration = getRegistrationByEventAndUser(eventId, userId);
        if (registration == null)
        {
            return false;
        }
        
        // 第一步：更新状态为已取消
        registration.setStatus(1);
        registration.setUpdateTime(new Date());
        
        boolean updateResult = registrationMapper.updateHongdaEventRegistration(registration) > 0;
        
        // 第二步：如果取消成功，同步减少活动表的报名计数
        if (updateResult)
        {
            hongdaEventMapper.decrementRegisteredCount(eventId);
        }
        
        return updateResult;
    }

    /**
     * 获取用户的报名记录列表
     */
    @Override
    public List<HongdaEventRegistration> getUserRegistrations(Long userId)
    {
        if (userId == null)
        {
            return List.of();
        }
        
        HongdaEventRegistration registration = new HongdaEventRegistration();
        registration.setUserId(userId);
        // 移除状态过滤，返回所有状态的报名记录（包括已取消的）
        
        return registrationMapper.selectHongdaEventRegistrationList(registration);
    }
}