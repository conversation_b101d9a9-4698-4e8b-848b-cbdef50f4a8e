package com.hongda.data.service;

import java.util.Map;

/**
 * 数据统计Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-22
 */
public interface IStatisticService 
{
    /**
     * 获取核心指标统计数据
     * 
     * @return 核心指标数据
     */
    public Map<String, Object> getSummaryStatistics();

    /**
     * 获取热门活动报名排行TOP5
     * 
     * @return 热门活动排行数据
     */
    public Map<String, Object> getTopEventsStatistics();

    /**
     * 获取近7日报名趋势
     * 
     * @return 每日报名趋势数据
     */
    public Map<String, Object> getDailyTrendStatistics();
}