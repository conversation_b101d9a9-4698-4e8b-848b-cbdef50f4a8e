package com.hongda.wxapp.controller;

import com.hongda.common.core.controller.BaseController;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.data.domain.HongdaComment;
import com.hongda.data.domain.vo.HongdaCommentVo;
import com.hongda.data.service.IHongdaCommentService;
import com.hongda.framework.web.service.TokenService; // 假设您会用到
import com.hongda.common.utils.ServletUtils; // 假设您会用到
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 小程序端评论接口
 */
@RestController
@RequestMapping("/api/v1/comment")
public class WxAppCommentController extends BaseController {

    @Autowired
    private IHongdaCommentService hongdaCommentService;

    @Autowired
    private TokenService tokenService; // 确保已注入

    /**
     * 小程序端：根据关联ID和类型获取评论列表（树形结构）
     */
    @GetMapping("/list")
    public AjaxResult getComments(HongdaComment queryParams) {
        try {
            queryParams.setStatus(1); // 业务逻辑：只查询已审核通过的评论
            List<HongdaCommentVo> treeComments = hongdaCommentService.selectWxAppCommentList(queryParams);

            Map<String, Object> data = new HashMap<>();
            data.put("comments", treeComments);
            long totalCount = countTotalComments(treeComments);
            data.put("total", totalCount);

            return AjaxResult.success(data);
        } catch (Exception e) {
            logger.error("获取评论列表失败，查询参数: {}", queryParams, e);
            return AjaxResult.error("获取评论失败，请稍后重试");
        }
    }

    private long countTotalComments(List<HongdaCommentVo> comments) {
        if (comments == null || comments.isEmpty()) {
            return 0;
        }
        long count = comments.size();
        for (HongdaCommentVo comment : comments) {
            count += countTotalComments(comment.getChildren());
        }
        return count;
    }

    /**
     * 小程序端：新增评论
     */
    @PostMapping("/add")
    public AjaxResult addComment(@RequestBody HongdaComment hongdaComment) {
        try {
            Long userId = tokenService.getWxUserId(ServletUtils.getRequest());
            if (userId == null) {
                return AjaxResult.error(401, "用户未登录");
            }
            hongdaComment.setUserId(userId);
            hongdaComment.setStatus(0); // 新评论默认为待审核状态

            return toAjax(hongdaCommentService.insertHongdaComment(hongdaComment));
        } catch (Exception e) {
            logger.error("提交评论失败", e);
            return AjaxResult.error("评论失败，请稍后重试");
        }
    }

    // 已删除 /like/{id} 点赞接口
}