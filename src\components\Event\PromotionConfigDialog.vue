<template>
  <el-dialog
    :title="dialogTitle"
    :model-value="open"
    @update:model-value="$emit('update:open', $event)"
    width="600px"
    append-to-body
  >
    <el-form 
      ref="promotionFormRef" 
      :model="form" 
      :rules="rules" 
      label-width="120px"
    >
      <el-form-item label="推广标题" prop="promotionTitle">
        <el-input
          :model-value="form.promotionTitle"
          @update:model-value="updateField('promotionTitle', $event)"
          placeholder="请输入推广标题"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="推广图片" prop="promotionImageUrl">
        <image-upload
          :model-value="form.promotionImageUrl"
          @update:model-value="updateField('promotionImageUrl', $event)"
        />
      </el-form-item>

      <el-form-item label="推广开始时间" prop="promotionStartTime">
        <el-date-picker
          :model-value="form.promotionStartTime"
          @update:model-value="updateField('promotionStartTime', $event)"
          type="datetime"
          value-format="YYYY-MM-DD HH:mm:ss"
          placeholder="请选择推广开始时间"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="推广结束时间" prop="promotionEndTime">
        <el-date-picker
          :model-value="form.promotionEndTime"
          @update:model-value="updateField('promotionEndTime', $event)"
          type="datetime"
          value-format="YYYY-MM-DD HH:mm:ss"
          placeholder="请选择推广结束时间"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="推广排序" prop="promotionSortOrder">
        <el-input-number
          :model-value="form.promotionSortOrder"
          @update:model-value="updateField('promotionSortOrder', $event)"
          :min="0"
          controls-position="right"
          style="width: 100%"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
        <el-button @click="handleCancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue'

// Props
const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  dialogTitle: {
    type: String,
    default: ''
  },
  form: {
    type: Object,
    required: true
  },
  rules: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['update:open', 'update:form', 'submit', 'cancel'])

// Refs
const promotionFormRef = ref()

// Methods
const handleSubmit = () => {
  promotionFormRef.value.validate(valid => {
    if (valid) {
      emit('submit', props.form)
    }
  })
}

const handleCancel = () => {
  emit('cancel')
}

const updateField = (field, value) => {
  const updatedForm = { ...props.form, [field]: value }
  emit('update:form', updatedForm)
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
}
</style>
