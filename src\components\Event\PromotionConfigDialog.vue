<template>
  <el-dialog 
    :title="dialogTitle" 
    v-model="open" 
    width="600px" 
    append-to-body
  >
    <el-form 
      ref="promotionFormRef" 
      :model="form" 
      :rules="rules" 
      label-width="120px"
    >
      <el-form-item label="推广标题" prop="promotionTitle">
        <el-input 
          v-model="form.promotionTitle" 
          placeholder="请输入推广标题" 
          maxlength="100" 
          show-word-limit 
        />
      </el-form-item>
      
      <el-form-item label="推广图片" prop="promotionImageUrl">
        <image-upload v-model="form.promotionImageUrl" />
      </el-form-item>
      
      <el-form-item label="推广开始时间" prop="promotionStartTime">
        <el-date-picker 
          v-model="form.promotionStartTime" 
          type="datetime" 
          value-format="YYYY-MM-DD HH:mm:ss" 
          placeholder="请选择推广开始时间"
          style="width: 100%"
        />
      </el-form-item>
      
      <el-form-item label="推广结束时间" prop="promotionEndTime">
        <el-date-picker 
          v-model="form.promotionEndTime" 
          type="datetime" 
          value-format="YYYY-MM-DD HH:mm:ss" 
          placeholder="请选择推广结束时间"
          style="width: 100%"
        />
      </el-form-item>
      
      <el-form-item label="推广排序" prop="promotionSortOrder">
        <el-input-number 
          v-model="form.promotionSortOrder" 
          :min="0" 
          controls-position="right" 
          style="width: 100%" 
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
        <el-button @click="handleCancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, getCurrentInstance } from 'vue'

const { proxy } = getCurrentInstance()

// Props
const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  dialogTitle: {
    type: String,
    default: ''
  },
  form: {
    type: Object,
    required: true
  },
  rules: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['update:open', 'submit', 'cancel'])

// Refs
const promotionFormRef = ref()

// Methods
const handleSubmit = () => {
  promotionFormRef.value.validate(valid => {
    if (valid) {
      emit('submit', props.form)
    }
  })
}

const handleCancel = () => {
  emit('cancel')
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
}
</style>
