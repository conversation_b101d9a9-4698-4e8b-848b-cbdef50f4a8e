package com.hongda.wxapp.domain.vo;

import com.hongda.common.annotation.OssUrl;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;

@Schema(description = "小程序端顾问信息展示VO")
public class ConsultantVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "顾问名称")
    private String name;

    @Schema(description = "顾问头像URL")
    @OssUrl
    private String avatarUrl;

    @Schema(description = "简介")
    private String introduction;

    @Schema(description = "二维码图片URL")
    @OssUrl
    private String qrCodeUrl;

    // --- Getter and Setter ---

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public String getIntroduction() {
        return introduction;
    }

    public void setIntroduction(String introduction) {
        this.introduction = introduction;
    }

    public String getQrCodeUrl() {
        return qrCodeUrl;
    }

    public void setQrCodeUrl(String qrCodeUrl) {
        this.qrCodeUrl = qrCodeUrl;
    }
}