package com.hongda.content.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hongda.common.annotation.Log;
import com.hongda.common.core.controller.BaseController;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.common.enums.BusinessType;
import com.hongda.content.domain.HongdaRegion;
import com.hongda.content.service.IHongdaRegionService;
import com.hongda.common.utils.poi.ExcelUtil;
import com.hongda.common.core.page.TableDataInfo;

/**
 * 地区管理Controller
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@RestController
@RequestMapping("/content/region")
public class HongdaRegionController extends BaseController
{
    @Autowired
    private IHongdaRegionService hongdaRegionService;

    /**
     * 查询地区管理列表
     */
    @PreAuthorize("@ss.hasPermi('content:region:list')")
    @GetMapping("/list")
    public TableDataInfo list(HongdaRegion hongdaRegion)
    {
        startPage();
        List<HongdaRegion> list = hongdaRegionService.selectHongdaRegionList(hongdaRegion);
        return getDataTable(list);
    }

    /**
     * 导出地区管理列表
     */
    @PreAuthorize("@ss.hasPermi('content:region:export')")
    @Log(title = "地区管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HongdaRegion hongdaRegion)
    {
        List<HongdaRegion> list = hongdaRegionService.selectHongdaRegionList(hongdaRegion);
        ExcelUtil<HongdaRegion> util = new ExcelUtil<HongdaRegion>(HongdaRegion.class);
        util.exportExcel(response, list, "地区管理数据");
    }

    /**
     * 获取地区管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('content:region:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(hongdaRegionService.selectHongdaRegionById(id));
    }

    /**
     * 新增地区管理
     */
    @PreAuthorize("@ss.hasPermi('content:region:add')")
    @Log(title = "地区管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HongdaRegion hongdaRegion)
    {
        return toAjax(hongdaRegionService.insertHongdaRegion(hongdaRegion));
    }

    /**
     * 修改地区管理
     */
    @PreAuthorize("@ss.hasPermi('content:region:edit')")
    @Log(title = "地区管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HongdaRegion hongdaRegion)
    {
        return toAjax(hongdaRegionService.updateHongdaRegion(hongdaRegion));
    }

    /**
     * 删除地区管理
     */
    @PreAuthorize("@ss.hasPermi('content:region:remove')")
    @Log(title = "地区管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(hongdaRegionService.deleteHongdaRegionByIds(ids));
    }
}
