<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <!-- <el-form-item label="微信用户唯一标识" prop="openid">
        <el-input
          v-model="queryParams.openid"
          placeholder="请输入微信用户唯一标识"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="微信开放平台唯一标识 (可选)" prop="unionid">
        <el-input
          v-model="queryParams.unionid"
          placeholder="请输入微信开放平台唯一标识 (可选)"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="手机号" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入手机号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户昵称" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入用户昵称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['data:miniuser:add']"
        >新增</el-button>
      </el-col> -->
      <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['data:miniuser:edit']"
        >修改</el-button>
      </el-col> -->
      <!-- <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['data:miniuser:remove']"
        >删除</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['data:miniuser:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="miniuserList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="用户信息" align="left" prop="nickname" width="250">
        <template #default="scope">
          <div>
            <div style="font-weight: 500;">{{ scope.row.nickname || '未设置昵称' }}</div>
            <div style="font-size: 12px; color: #999;">ID: {{ scope.row.id }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="手机号" align="center" prop="phone" />
      <el-table-column label="账户状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120">
        <template #default="scope">
          <el-dropdown trigger="click">
            <el-button link type="primary">
              更多操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item 
                  icon="Edit" 
                  @click="handleUpdate(scope.row)" 
                  v-hasPermi="['data:miniuser:edit']">
                  管理
                </el-dropdown-item>
                <el-dropdown-item 
                  icon="Delete" 
                  @click="handleDelete(scope.row)" 
                  v-hasPermi="['data:miniuser:remove']"
                  :style="{ color: 'var(--el-color-danger)' }">
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 管理用户信息对话框 -->
    <el-dialog title="管理用户信息" v-model="open" width="600px" append-to-body>
      <el-form ref="miniuserRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="用户ID">
          <el-input v-model="form.id" :disabled="true" />
        </el-form-item>
        <el-form-item label="微信用户标识" prop="openid">
          <el-input v-model="form.openid" :disabled="true" placeholder="微信用户唯一标识" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="form.phone"/>
        </el-form-item>
        <el-form-item label="用户昵称" prop="nickname">
          <el-input v-model="form.nickname"/>
        </el-form-item>
        <el-form-item label="账户状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :label="parseInt(dict.value)">{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-input v-model="form.createTime" :disabled="true" />
        </el-form-item>
        <el-form-item label="更新时间">
          <el-input v-model="form.updateTime" :disabled="true" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Miniuser">
import { listMiniuser, getMiniuser, delMiniuser, addMiniuser, updateMiniuser } from "@/api/data/miniuser"

const { proxy } = getCurrentInstance()
const { sys_normal_disable } = proxy.useDict("sys_normal_disable")

const miniuserList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    openid: null,
    unionid: null,
    phone: null,
    nickname: null,
    avatarUrl: null,
    status: null,
  },
  rules: {
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询小程序用户信息 (对应用户中心、登录功能)列表 */
function getList() {
  loading.value = true
  listMiniuser(queryParams.value).then(response => {
    miniuserList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    openid: null,
    unionid: null,
    phone: null,
    nickname: null,
    avatarUrl: null,
    status: null,
    createTime: null,
    updateTime: null
  }
  proxy.resetForm("miniuserRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 管理按钮操作 */
function handleUpdate(row) {
  reset()
  const _id = row.id || ids.value
  getMiniuser(_id).then(response => {
    form.value = response.data
    open.value = true
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["miniuserRef"].validate(valid => {
    if (valid) {
      updateMiniuser(form.value).then(response => {
        proxy.$modal.msgSuccess("用户信息更新成功")
        open.value = false
        getList()
      })
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value
  proxy.$modal.confirm('是否确认删除用户ID为"' + _ids + '"的用户数据？').then(function() {
    return delMiniuser(_ids)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('data/miniuser/export', {
    ...queryParams.value
  }, `miniuser_${new Date().getTime()}.xlsx`)
}

getList()
</script>
