package com.hongda.content.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.hongda.common.annotation.Excel;
import com.hongda.common.core.domain.BaseEntity;

/**
 * 资讯分类标签对象 hongda_tag
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
public class HongdaTag extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 标签ID */
    @Excel(name = "标签ID")
    private Long id;

    /** 标签名称 */
    @Excel(name = "标签名称")
    private String name;

    /** 显示顺序 */
    @Excel(name = "显示顺序")
    private Long sortOrder;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }

    public void setSortOrder(Long sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Long getSortOrder() 
    {
        return sortOrder;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("sortOrder", getSortOrder())
            .toString();
    }
}
