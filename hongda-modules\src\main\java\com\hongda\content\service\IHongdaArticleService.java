package com.hongda.content.service;

import java.util.List;
import com.hongda.content.domain.HongdaArticle;

/**
 * 资讯文章Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface IHongdaArticleService 
{
    /**
     * 查询资讯文章
     * 
     * @param id 资讯文章主键
     * @return 资讯文章
     */
    public HongdaArticle selectHongdaArticleById(Long id);

    /**
     * 查询资讯文章列表
     * 
     * @param hongdaArticle 资讯文章
     * @return 资讯文章集合
     */
    public List<HongdaArticle> selectHongdaArticleList(HongdaArticle hongdaArticle);

    /**
     * 新增资讯文章
     * 
     * @param hongdaArticle 资讯文章
     * @return 结果
     */
    public int insertHongdaArticle(HongdaArticle hongdaArticle);

    /**
     * 修改资讯文章
     * 
     * @param hongdaArticle 资讯文章
     * @return 结果
     */
    public int updateHongdaArticle(HongdaArticle hongdaArticle);

    /**
     * 批量删除资讯文章
     * 
     * @param ids 需要删除的资讯文章主键集合
     * @return 结果
     */
    public int deleteHongdaArticleByIds(Long[] ids);

    /**
     * 删除资讯文章信息
     * 
     * @param id 资讯文章主键
     * @return 结果
     */
    public int deleteHongdaArticleById(Long id);

    void incrementArticleViewCount(Long articleId);
}
