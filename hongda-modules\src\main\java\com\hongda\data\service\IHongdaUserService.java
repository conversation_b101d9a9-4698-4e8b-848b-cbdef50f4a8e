package com.hongda.data.service;

import java.util.List;
import com.hongda.data.domain.HongdaUser;

/**
 * 小程序用户信息 (对应用户中心、登录功能)Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface IHongdaUserService 
{
    /**
     * 查询小程序用户信息 (对应用户中心、登录功能)
     * 
     * @param id 小程序用户信息 (对应用户中心、登录功能)主键
     * @return 小程序用户信息 (对应用户中心、登录功能)
     */
    public HongdaUser selectHongdaUserById(Long id);

    /**
     * 查询小程序用户信息 (对应用户中心、登录功能)列表
     * 
     * @param hongdaUser 小程序用户信息 (对应用户中心、登录功能)
     * @return 小程序用户信息 (对应用户中心、登录功能)集合
     */
    public List<HongdaUser> selectHongdaUserList(HongdaUser hongdaUser);

    /**
     * 新增小程序用户信息 (对应用户中心、登录功能)
     * 
     * @param hongdaUser 小程序用户信息 (对应用户中心、登录功能)
     * @return 结果
     */
    public int insertHongdaUser(HongdaUser hongdaUser);

    /**
     * 修改小程序用户信息 (对应用户中心、登录功能)
     * 
     * @param hongdaUser 小程序用户信息 (对应用户中心、登录功能)
     * @return 结果
     */
    public int updateHongdaUser(HongdaUser hongdaUser);

    /**
     * 批量删除小程序用户信息 (对应用户中心、登录功能)
     * 
     * @param ids 需要删除的小程序用户信息 (对应用户中心、登录功能)主键集合
     * @return 结果
     */
    public int deleteHongdaUserByIds(Long[] ids);

    /**
     * 删除小程序用户信息 (对应用户中心、登录功能)信息
     * 
     * @param id 小程序用户信息 (对应用户中心、登录功能)主键
     * @return 结果
     */
    public int deleteHongdaUserById(Long id);
}
