package com.hongda.content.domain;

import com.hongda.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 文章与标签关联对象 hongda_article_tag_relation
 * <p>
 * 这个类是一个简单的POJO (Plain Old Java Object)，
 * 它的作用是在业务逻辑中临时存储一篇文章和一个标签的ID对应关系。
 * 我们将用它来构建一个列表，然后一次性批量插入到数据库的中间关系表中。
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class HongdaArticleTagRelation extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 文章ID
     */
    private Long articleId;

    /**
     * 标签ID
     */
    private Long tagId;

}
