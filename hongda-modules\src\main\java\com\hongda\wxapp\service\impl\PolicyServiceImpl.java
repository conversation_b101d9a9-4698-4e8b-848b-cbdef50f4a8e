package com.hongda.wxapp.service.impl;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StreamUtils;

import com.hongda.common.core.domain.AjaxResult;
import com.hongda.common.core.domain.dto.PolicyAcceptDTO;
import com.hongda.common.utils.StringUtils;
import com.hongda.wxapp.domain.PolicyAcceptLog;
import com.hongda.wxapp.mapper.PolicyAcceptLogMapper;
import com.hongda.wxapp.service.IPolicyService;
import com.alibaba.fastjson2.JSON;

/**
 * 协议服务实现
 *
 * <AUTHOR>
 */
@Service
public class PolicyServiceImpl implements IPolicyService
{
    @Autowired
    private PolicyAcceptLogMapper policyAcceptLogMapper;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    // 用户协议配置
    @Value("${policy.user-agreement.version}")
    private String userAgreementVersion;

    @Value("${policy.user-agreement.title}")
    private String userAgreementTitle;

    @Value("${policy.user-agreement.effective-time}")
    private String userAgreementEffectiveTime;

    @Value("${policy.user-agreement.file}")
    private String userAgreementFile;

    // 隐私政策配置
    @Value("${policy.privacy-policy.version}")
    private String privacyPolicyVersion;

    @Value("${policy.privacy-policy.title}")
    private String privacyPolicyTitle;

    @Value("${policy.privacy-policy.effective-time}")
    private String privacyPolicyEffectiveTime;

    @Value("${policy.privacy-policy.file}")
    private String privacyPolicyFile;

    /**
     * 获取最新协议内容
     */
    @Override
    public AjaxResult getLatestPolicy(String type)
    {
        try {
            // 参数验证
            if (StringUtils.isBlank(type) || 
                (!type.equals("user_agreement") && !type.equals("privacy_policy"))) {
                return AjaxResult.error("协议类型参数无效");
            }

            // 从Redis缓存中获取
            String cacheKey = "policy:" + type + ":latest";
            String cachedContent = redisTemplate.opsForValue().get(cacheKey);
            if (StringUtils.isNotBlank(cachedContent)) {
                try {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> cachedMap = JSON.parseObject(cachedContent, Map.class);
                    System.out.println("从缓存获取协议内容: " + type + ", 版本: " + cachedMap.get("version"));
                    return AjaxResult.success(cachedMap);
                } catch (Exception parseErr) {
                    System.out.println("缓存解析失败，清理缓存并回源: " + parseErr.getMessage());
                    try { redisTemplate.delete(cacheKey); } catch (Exception ignore) {}
                    // 继续回源
                }
            }

            // 根据类型获取配置
            String version, title, effectiveTime, filePath;
            if (type.equals("user_agreement")) {
                version = userAgreementVersion;
                title = userAgreementTitle;
                effectiveTime = userAgreementEffectiveTime;
                filePath = userAgreementFile;
            } else {
                version = privacyPolicyVersion;
                title = privacyPolicyTitle;
                effectiveTime = privacyPolicyEffectiveTime;
                filePath = privacyPolicyFile;
            }

            // 读取资源文件
            String contentHtml = readResourceFile(filePath);
            if (StringUtils.isBlank(contentHtml)) {
                return AjaxResult.error("协议文件不存在或为空");
            }

            // 构建返回数据
            Map<String, Object> data = new LinkedHashMap<>();
            data.put("type", type);
            data.put("version", version);
            data.put("title", title);
            data.put("effectiveTime", effectiveTime);
            data.put("contentHtml", contentHtml);

            // 存入缓存 (10分钟过期) - 使用标准JSON序列化
            String jsonStr = JSON.toJSONString(data);
            redisTemplate.opsForValue().set(cacheKey, jsonStr, 10, TimeUnit.MINUTES);

            System.out.println("成功获取协议内容: " + type + ", 版本: " + version);
            return AjaxResult.success(data);

        } catch (Exception e) {
            System.err.println("获取协议内容失败: " + e.getMessage());
            e.printStackTrace();
            return AjaxResult.error("获取协议内容失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定版本协议内容
     */
    @Override
    public AjaxResult getPolicyByVersion(String type, String version)
    {
        try {
            // 参数验证
            if (StringUtils.isBlank(type) || StringUtils.isBlank(version)) {
                return AjaxResult.error("参数不能为空");
            }

            if (!type.equals("user_agreement") && !type.equals("privacy_policy")) {
                return AjaxResult.error("协议类型参数无效");
            }

            // 构建文件路径
            String fileName = type + "_v" + version + ".html";
            String filePath = "policy/" + fileName;

            // 读取资源文件
            String contentHtml = readResourceFile(filePath);
            if (StringUtils.isBlank(contentHtml)) {
                return AjaxResult.error("指定版本的协议文件不存在");
            }

            // 获取标题
            String title = type.equals("user_agreement") ? "用户协议" : "隐私政策";

            // 构建返回数据
            Map<String, Object> data = new LinkedHashMap<>();
            data.put("type", type);
            data.put("version", version);
            data.put("title", title);
            data.put("contentHtml", contentHtml);

            return AjaxResult.success(data);

        } catch (Exception e) {
            System.err.println("获取指定版本协议内容失败: " + e.getMessage());
            e.printStackTrace();
            return AjaxResult.error("获取协议内容失败: " + e.getMessage());
        }
    }

    /**
     * 记录用户同意协议
     */
    @Override
    public AjaxResult acceptPolicy(Long userId, PolicyAcceptDTO acceptDTO, String ip, String userAgent)
    {
        try {
            // 参数验证
            if (userId == null || acceptDTO == null) {
                return AjaxResult.error("参数不能为空");
            }

            if (StringUtils.isBlank(acceptDTO.getPolicyType()) || 
                StringUtils.isBlank(acceptDTO.getPolicyVersion())) {
                return AjaxResult.error("协议类型和版本不能为空");
            }

            // 检查是否已经记录过相同版本的同意
            PolicyAcceptLog existingLog = policyAcceptLogMapper.selectByUserIdAndTypeAndVersion(
                userId, acceptDTO.getPolicyType(), acceptDTO.getPolicyVersion());
            
            if (existingLog != null) {
                System.out.println("用户已同意过此版本协议，跳过重复记录: userId=" + userId + 
                    ", type=" + acceptDTO.getPolicyType() + ", version=" + acceptDTO.getPolicyVersion());
                return AjaxResult.success("已记录同意");
            }

            // 获取当前协议内容用于计算哈希（可选）
            String contentHash = null;
            String fileName = null;
            try {
                AjaxResult policyResult = getLatestPolicy(acceptDTO.getPolicyType());
                if (policyResult.get("data") != null) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> policyData = (Map<String, Object>) policyResult.get("data");
                    String content = (String) policyData.get("contentHtml");
                    if (StringUtils.isNotBlank(content)) {
                        contentHash = calculateMD5(content);
                        fileName = acceptDTO.getPolicyType() + "_v" + acceptDTO.getPolicyVersion() + ".html";
                    }
                }
            } catch (Exception e) {
                System.err.println("计算内容哈希失败: " + e.getMessage());
            }

            // 创建留痕记录
            PolicyAcceptLog log = new PolicyAcceptLog();
            log.setUserId(userId);
            log.setPolicyType(acceptDTO.getPolicyType());
            log.setPolicyVersion(acceptDTO.getPolicyVersion());
            log.setAcceptedAt(new Date());
            log.setIp(ip);
            log.setUserAgent(userAgent);
            log.setFileName(fileName);
            log.setContentHash(contentHash);

            // 插入数据库
            int result = policyAcceptLogMapper.insertPolicyAcceptLog(log);
            if (result > 0) {
                System.out.println("协议同意记录成功: userId=" + userId + 
                    ", type=" + acceptDTO.getPolicyType() + ", version=" + acceptDTO.getPolicyVersion());
                return AjaxResult.success("记录成功");
            } else {
                return AjaxResult.error("记录失败");
            }

        } catch (Exception e) {
            System.err.println("记录协议同意失败: " + e.getMessage());
            e.printStackTrace();
            return AjaxResult.error("记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户已同意的协议版本
     */
    @Override
    public AjaxResult getUserAcceptedPolicy(Long userId, String type)
    {
        try {
            // 参数验证
            if (userId == null || StringUtils.isBlank(type)) {
                return AjaxResult.error("参数不能为空");
            }

            // 查询最新的同意记录
            PolicyAcceptLog log = policyAcceptLogMapper.selectLatestByUserIdAndType(userId, type);
            if (log == null) {
                return AjaxResult.error("未找到同意记录");
            }

            // 构建返回数据
            Map<String, Object> data = new LinkedHashMap<>();
            data.put("policyType", log.getPolicyType());
            data.put("policyVersion", log.getPolicyVersion());
            data.put("acceptedAt", log.getAcceptedAt());

            return AjaxResult.success(data);

        } catch (Exception e) {
            System.err.println("获取用户已同意协议版本失败: " + e.getMessage());
            e.printStackTrace();
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 读取资源文件内容
     */
    private String readResourceFile(String filePath) throws IOException
    {
        ClassPathResource resource = new ClassPathResource(filePath);
        if (!resource.exists()) {
            throw new IOException("资源文件不存在: " + filePath);
        }
        
        return StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);
    }

    /**
     * 计算MD5哈希
     */
    private String calculateMD5(String content)
    {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hash = md.digest(content.getBytes(StandardCharsets.UTF_8));
            StringBuilder sb = new StringBuilder();
            for (byte b : hash) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            System.err.println("计算MD5失败: " + e.getMessage());
            return null;
        }
    }
}
