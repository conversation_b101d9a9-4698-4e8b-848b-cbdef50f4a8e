package com.hongda.content.service.impl;

import java.util.List;
import com.hongda.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.hongda.content.mapper.HongdaParkMapper;
import com.hongda.content.domain.HongdaPark;
import com.hongda.content.service.IHongdaParkService;

/**
 * 园区信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
@Service
public class HongdaParkServiceImpl implements IHongdaParkService 
{
    @Autowired
    private HongdaParkMapper hongdaParkMapper;

    /**
     * 查询园区信息
     * 
     * @param id 园区信息主键
     * @return 园区信息
     */
    @Override
    public HongdaPark selectHongdaParkById(Long id)
    {
        return hongdaParkMapper.selectHongdaParkById(id);
    }

    /**
     * 查询园区信息列表
     * 
     * @param hongdaPark 园区信息
     * @return 园区信息
     */
    @Override
    public List<HongdaPark> selectHongdaParkList(HongdaPark hongdaPark)
    {
        return hongdaParkMapper.selectHongdaParkList(hongdaPark);
    }

    /**
     * 新增园区信息
     * 
     * @param hongdaPark 园区信息
     * @return 结果
     */
    @Override
    public int insertHongdaPark(HongdaPark hongdaPark)
    {
        hongdaPark.setCreateTime(DateUtils.getNowDate());
        return hongdaParkMapper.insertHongdaPark(hongdaPark);
    }

    /**
     * 修改园区信息
     * 
     * @param hongdaPark 园区信息
     * @return 结果
     */
    @Override
    public int updateHongdaPark(HongdaPark hongdaPark)
    {
        hongdaPark.setUpdateTime(DateUtils.getNowDate());
        return hongdaParkMapper.updateHongdaPark(hongdaPark);
    }

    /**
     * 批量删除园区信息
     * 
     * @param ids 需要删除的园区信息主键
     * @return 结果
     */
    @Override
    public int deleteHongdaParkByIds(Long[] ids)
    {
        return hongdaParkMapper.deleteHongdaParkByIds(ids);
    }

    /**
     * 删除园区信息信息
     * 
     * @param id 园区信息主键
     * @return 结果
     */
    @Override
    public int deleteHongdaParkById(Long id)
    {
        return hongdaParkMapper.deleteHongdaParkById(id);
    }
}
