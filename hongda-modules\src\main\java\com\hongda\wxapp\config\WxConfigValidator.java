package com.hongda.wxapp.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import com.alibaba.fastjson2.JSONObject;
import com.hongda.common.utils.http.HttpUtils;

/**
 * 微信配置验证工具
 *
 * <AUTHOR>
 */
@Component
public class WxConfigValidator
{
    @Value("${wx.app-id}")
    private String appId;

    @Value("${wx.app-secret}")
    private String appSecret;

    /**
     * 验证微信配置是否正确
     */
    public void validateConfig()
    {
        System.out.println("=== 微信配置验证开始 ===");
        System.out.println("AppID: " + appId);
        System.out.println("AppSecret: " + (appSecret != null ? appSecret.substring(0, 8) + "..." : "null"));
        
        // 检查配置是否为空
        if (appId == null || appId.trim().isEmpty()) {
            System.out.println(" AppID 配置为空！");
            return;
        }
        
        if (appSecret == null || appSecret.trim().isEmpty()) {
            System.out.println(" AppSecret 配置为空！");
            return;
        }
        
        // 检查AppID格式
        if (!appId.startsWith("wx") || appId.length() != 18) {
            System.out.println(" AppID 格式不正确！应该以'wx'开头，总长度18位");
            return;
        }
        
        // 检查AppSecret格式
        if (appSecret.length() != 32) {
            System.out.println(" AppSecret 格式不正确！应该是32位字符串");
            return;
        }
        
        System.out.println("基础格式验证通过");
        
        // 尝试获取AccessToken验证配置是否有效
        try {
            String url = "https://api.weixin.qq.com/cgi-bin/token?appid={0}&secret={1}&grant_type=client_credential";
            String replaceUrl = url.replace("{0}", appId).replace("{1}", appSecret);
            
            System.out.println("测试微信API连通性...");
            String response = HttpUtils.sendGet(replaceUrl);
            System.out.println("微信API响应: " + response);
            
            JSONObject jsonObject = JSONObject.parseObject(response);
            Integer errcode = jsonObject.getInteger("errcode");
            
            if (errcode == null) {
                String accessToken = jsonObject.getString("access_token");
                if (accessToken != null && !accessToken.isEmpty()) {
                    System.out.println("微信配置验证成功！AccessToken已获取");
                } else {
                    System.out.println("微信配置验证失败：未能获取AccessToken");
                }
            } else {
                String errmsg = jsonObject.getString("errmsg");
                System.out.println("微信配置验证失败: " + errmsg + " (错误代码: " + errcode + ")");
                
                // 提供详细的错误解释
                switch (errcode) {
                    case 40013:
                        System.out.println("错误说明：AppID 无效，请检查是否复制正确");
                        break;
                    case 40001:
                        System.out.println("错误说明：AppSecret 无效，请检查是否复制正确");
                        break;
                    case 40164:
                        System.out.println("错误说明：请在微信公众平台配置IP白名单");
                        break;
                    default:
                        System.out.println("请参考微信官方文档：https://developers.weixin.qq.com/miniprogram/dev/api-backend/");
                }
            }
        } catch (Exception e) {
            System.out.println("验证过程出现异常: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("=== 微信配置验证完成 ===");
    }
} 