<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongda.data.mapper.HongdaUserMapper">
    
    <resultMap type="HongdaUser" id="HongdaUserResult">
        <result property="id"    column="id"    />
        <result property="openid"    column="openid"    />
        <result property="unionid"    column="unionid"    />
        <result property="phone"    column="phone"    />
        <result property="nickname"    column="nickname"    />
        <result property="avatarUrl"    column="avatar_url"    />
        <result property="status"    column="status"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="deletedAt"    column="deleted_at"    />
        <result property="phoneHash"    column="phone_hash"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectHongdaUserVo">
        select id, openid, unionid, phone, nickname, avatar_url, status, is_deleted, deleted_at, phone_hash, create_time, update_time from hongda_user
    </sql>

    <select id="selectHongdaUserList" parameterType="HongdaUser" resultMap="HongdaUserResult">
        <include refid="selectHongdaUserVo"/>
        <where>  
            and (is_deleted = 0 or is_deleted is null)
            <if test="openid != null  and openid != ''"> and openid = #{openid}</if>
            <if test="unionid != null  and unionid != ''"> and unionid = #{unionid}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="nickname != null  and nickname != ''"> and nickname like concat('%', #{nickname}, '%')</if>
            <if test="avatarUrl != null  and avatarUrl != ''"> and avatar_url = #{avatarUrl}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectHongdaUserById" parameterType="Long" resultMap="HongdaUserResult">
        <include refid="selectHongdaUserVo"/>
        where id = #{id} and (is_deleted = 0 or is_deleted is null)
    </select>

    <select id="selectHongdaUserByOpenid" parameterType="String" resultMap="HongdaUserResult">
        <include refid="selectHongdaUserVo"/>
        where openid = #{openid} and (is_deleted = 0 or is_deleted is null)
    </select>

    <insert id="insertHongdaUser" parameterType="HongdaUser" useGeneratedKeys="true" keyProperty="id">
        insert into hongda_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="openid != null">openid,</if>
            <if test="unionid != null">unionid,</if>
            <if test="phone != null">phone,</if>
            <if test="nickname != null">nickname,</if>
            <if test="avatarUrl != null">avatar_url,</if>
            <if test="status != null">status,</if>
            <if test="isDeleted != null">is_deleted,</if>
            <if test="deletedAt != null">deleted_at,</if>
            <if test="phoneHash != null">phone_hash,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="openid != null">#{openid},</if>
            <if test="unionid != null">#{unionid},</if>
            <if test="phone != null">#{phone},</if>
            <if test="nickname != null">#{nickname},</if>
            <if test="avatarUrl != null">#{avatarUrl},</if>
            <if test="status != null">#{status},</if>
            <if test="isDeleted != null">#{isDeleted},</if>
            <if test="deletedAt != null">#{deletedAt},</if>
            <if test="phoneHash != null">#{phoneHash},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateHongdaUser" parameterType="HongdaUser">
        update hongda_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="openid != null">openid = #{openid},</if>
            <if test="unionid != null">unionid = #{unionid},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="nickname != null">nickname = #{nickname},</if>
            <if test="avatarUrl != null">avatar_url = #{avatarUrl},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="deletedAt != null">deleted_at = #{deletedAt},</if>
            <if test="phoneHash != null">phone_hash = #{phoneHash},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHongdaUserById" parameterType="Long">
        delete from hongda_user where id = #{id}
    </delete>

    <delete id="deleteHongdaUserByIds" parameterType="String">
        delete from hongda_user where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 软删除相关查询方法 -->
    
    <!-- 根据openid查询用户（包含软删除用户） -->
    <select id="selectHongdaUserByOpenidIncludeDeleted" parameterType="String" resultMap="HongdaUserResult">
        <include refid="selectHongdaUserVo"/>
        where openid = #{openid}
    </select>

    <!-- 根据手机号哈希查询已删除的用户 -->
    <select id="selectDeletedByPhoneHash" parameterType="String" resultMap="HongdaUserResult">
        <include refid="selectHongdaUserVo"/>
        where is_deleted = 1 and phone_hash = #{phoneHash}
        limit 1
    </select>

    <!-- 复活用户账号（将软删除状态改为正常；若被禁用status=1不自动改状态，保留禁用） -->
    <update id="reactivateUserById" parameterType="Long">
        update hongda_user 
        set is_deleted = 0, deleted_at = null, update_time = now()
        where id = #{userId}
    </update>

</mapper>