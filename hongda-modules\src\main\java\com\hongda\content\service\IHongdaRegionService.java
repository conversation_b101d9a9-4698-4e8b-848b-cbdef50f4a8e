package com.hongda.content.service;

import java.util.List;
import com.hongda.content.domain.HongdaRegion;

/**
 * 地区管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
public interface IHongdaRegionService 
{
    /**
     * 查询地区管理
     * 
     * @param id 地区管理主键
     * @return 地区管理
     */
    public HongdaRegion selectHongdaRegionById(Long id);

    /**
     * 查询地区管理列表
     * 
     * @param hongdaRegion 地区管理
     * @return 地区管理集合
     */
    public List<HongdaRegion> selectHongdaRegionList(HongdaRegion hongdaRegion);

    /**
     * 新增地区管理
     * 
     * @param hongdaRegion 地区管理
     * @return 结果
     */
    public int insertHongdaRegion(HongdaRegion hongdaRegion);

    /**
     * 修改地区管理
     * 
     * @param hongdaRegion 地区管理
     * @return 结果
     */
    public int updateHongdaRegion(HongdaRegion hongdaRegion);

    /**
     * 批量删除地区管理
     * 
     * @param ids 需要删除的地区管理主键集合
     * @return 结果
     */
    public int deleteHongdaRegionByIds(Long[] ids);

    /**
     * 删除地区管理信息
     * 
     * @param id 地区管理主键
     * @return 结果
     */
    public int deleteHongdaRegionById(Long id);
}
