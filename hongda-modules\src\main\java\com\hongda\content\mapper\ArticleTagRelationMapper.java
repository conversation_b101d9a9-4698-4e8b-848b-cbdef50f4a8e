package com.hongda.content.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.hongda.content.domain.ArticleTagRelation;

/**
 * 文章标签关联表 数据层
 * * <AUTHOR>
 */
@Mapper
public interface ArticleTagRelationMapper
{
    /**
     * 批量新增文章标签信息
     * * @param articleTagList 文章标签列表
     * @return 结果
     */
    public int batchArticleTag(List<ArticleTagRelation> articleTagList);

    /**
     * 通过文章ID删除文章标签关联
     * * @param articleId 文章ID
     * @return 结果
     */
    public int deleteArticleTagByArticleId(Long articleId);

    /**
     * 根据文章ID查询所有关联的标签ID
     *
     * @param articleId 文章ID
     * @return 标签ID列表
     */
    public List<Long> selectTagIdsByArticleId(Long articleId);
}