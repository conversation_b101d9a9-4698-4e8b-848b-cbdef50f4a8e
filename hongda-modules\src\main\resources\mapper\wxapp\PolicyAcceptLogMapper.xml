<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongda.wxapp.mapper.PolicyAcceptLogMapper">
    
    <resultMap type="PolicyAcceptLog" id="PolicyAcceptLogResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="policyType"    column="policy_type"    />
        <result property="policyVersion"    column="policy_version"    />
        <result property="acceptedAt"    column="accepted_at"    />
        <result property="ip"    column="ip"    />
        <result property="userAgent"    column="user_agent"    />
        <result property="fileName"    column="file_name"    />
        <result property="contentHash"    column="content_hash"    />
    </resultMap>

    <sql id="selectPolicyAcceptLogVo">
        select id, user_id, policy_type, policy_version, accepted_at, ip, user_agent, file_name, content_hash from hd_policy_accept_log
    </sql>

    <select id="selectPolicyAcceptLogList" parameterType="PolicyAcceptLog" resultMap="PolicyAcceptLogResult">
        <include refid="selectPolicyAcceptLogVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="policyType != null  and policyType != ''"> and policy_type = #{policyType}</if>
            <if test="policyVersion != null  and policyVersion != ''"> and policy_version = #{policyVersion}</if>
            <if test="acceptedAt != null "> and accepted_at = #{acceptedAt}</if>
            <if test="ip != null  and ip != ''"> and ip = #{ip}</if>
        </where>
        order by accepted_at desc
    </select>
    
    <select id="selectPolicyAcceptLogById" parameterType="Long" resultMap="PolicyAcceptLogResult">
        <include refid="selectPolicyAcceptLogVo"/>
        where id = #{id}
    </select>

    <select id="selectLatestByUserIdAndType" resultMap="PolicyAcceptLogResult">
        <include refid="selectPolicyAcceptLogVo"/>
        where user_id = #{userId} and policy_type = #{policyType}
        order by accepted_at desc
        limit 1
    </select>

    <select id="selectByUserIdAndTypeAndVersion" resultMap="PolicyAcceptLogResult">
        <include refid="selectPolicyAcceptLogVo"/>
        where user_id = #{userId} and policy_type = #{policyType} and policy_version = #{policyVersion}
        limit 1
    </select>
        
    <insert id="insertPolicyAcceptLog" parameterType="PolicyAcceptLog" useGeneratedKeys="true" keyProperty="id">
        insert into hd_policy_accept_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="policyType != null and policyType != ''">policy_type,</if>
            <if test="policyVersion != null and policyVersion != ''">policy_version,</if>
            <if test="acceptedAt != null">accepted_at,</if>
            <if test="ip != null">ip,</if>
            <if test="userAgent != null">user_agent,</if>
            <if test="fileName != null">file_name,</if>
            <if test="contentHash != null">content_hash,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="policyType != null and policyType != ''">#{policyType},</if>
            <if test="policyVersion != null and policyVersion != ''">#{policyVersion},</if>
            <if test="acceptedAt != null">#{acceptedAt},</if>
            <if test="ip != null">#{ip},</if>
            <if test="userAgent != null">#{userAgent},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="contentHash != null">#{contentHash},</if>
         </trim>
    </insert>

    <update id="updatePolicyAcceptLog" parameterType="PolicyAcceptLog">
        update hd_policy_accept_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="policyType != null and policyType != ''">policy_type = #{policyType},</if>
            <if test="policyVersion != null and policyVersion != ''">policy_version = #{policyVersion},</if>
            <if test="acceptedAt != null">accepted_at = #{acceptedAt},</if>
            <if test="ip != null">ip = #{ip},</if>
            <if test="userAgent != null">user_agent = #{userAgent},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="contentHash != null">content_hash = #{contentHash},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePolicyAcceptLogById" parameterType="Long">
        delete from hd_policy_accept_log where id = #{id}
    </delete>

    <delete id="deletePolicyAcceptLogByIds" parameterType="String">
        delete from hd_policy_accept_log where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
