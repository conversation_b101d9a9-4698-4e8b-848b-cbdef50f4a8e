<template>
  <el-row :gutter="10" class="mb8">
    <el-col :span="1.5">
      <el-button 
        type="primary" 
        plain 
        icon="Plus" 
        @click="handleAdd" 
        v-hasPermi="['data:registration:add']"
      >新增</el-button>
    </el-col>
    <el-col :span="1.5">
      <el-button 
        type="success" 
        plain 
        icon="Edit" 
        :disabled="single" 
        @click="handleEdit" 
        v-hasPermi="['data:registration:edit']"
      >修改</el-button>
    </el-col>
    <el-col :span="1.5">
      <el-button 
        type="danger" 
        plain 
        icon="Delete" 
        :disabled="multiple" 
        @click="handleDelete" 
        v-hasPermi="['data:registration:remove']"
      >删除</el-button>
    </el-col>
    <el-col :span="1.5">
      <el-button 
        type="warning" 
        plain 
        icon="Download" 
        @click="handleExport" 
        v-hasPermi="['data:registration:export']"
      >导出</el-button>
    </el-col>
  </el-row>
</template>

<script setup>
// Props
const props = defineProps({
  single: {
    type: Boolean,
    default: true
  },
  multiple: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits(['add', 'edit', 'delete', 'export'])

// Methods
const handleAdd = () => {
  emit('add')
}

const handleEdit = () => {
  emit('edit')
}

const handleDelete = () => {
  emit('delete')
}

const handleExport = () => {
  emit('export')
}
</script>
