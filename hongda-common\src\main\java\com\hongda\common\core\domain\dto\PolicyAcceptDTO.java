package com.hongda.common.core.domain.dto;

/**
 * 协议同意DTO
 *
 * <AUTHOR>
 */
public class PolicyAcceptDTO
{
    /**
     * 协议类型 (user_agreement / privacy_policy)
     */
    private String policyType;

    /**
     * 协议版本
     */
    private String policyVersion;

    public PolicyAcceptDTO()
    {
    }

    public PolicyAcceptDTO(String policyType, String policyVersion)
    {
        this.policyType = policyType;
        this.policyVersion = policyVersion;
    }

    public String getPolicyType()
    {
        return policyType;
    }

    public void setPolicyType(String policyType)
    {
        this.policyType = policyType;
    }

    public String getPolicyVersion()
    {
        return policyVersion;
    }

    public void setPolicyVersion(String policyVersion)
    {
        this.policyVersion = policyVersion;
    }

    @Override
    public String toString()
    {
        return "PolicyAcceptDTO{" +
                "policyType='" + policyType + '\'' +
                ", policyVersion='" + policyVersion + '\'' +
                '}';
    }
}
