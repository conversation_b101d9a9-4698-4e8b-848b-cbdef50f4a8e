package com.hongda.content.enums;

import java.util.Date;

/**
 * 活动状态枚举
 * 基于活动开始时间和结束时间动态计算
 * 
 * <AUTHOR>
 * @date 2025-01-22
 */
public enum ActivityStatus {
    NOT_STARTED(0, "未开始"),
    IN_PROGRESS(1, "进行中"),
    ENDED(2, "已结束");
    
    private final Integer code;
    private final String desc;
    
    ActivityStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据活动开始时间和结束时间计算活动状态
     * 
     * @param startTime 活动开始时间
     * @param endTime 活动结束时间
     * @return 活动状态
     */
    public static ActivityStatus calculateStatus(Date startTime, Date endTime) {
        Date now = new Date();
        
        // 如果开始时间为空，认为是未开始
        if (startTime == null) {
            return NOT_STARTED;
        }
        
        // 未开始：当前时间 < 开始时间
        if (now.before(startTime)) {
            return NOT_STARTED;
        } 
        // 进行中：当前时间 >= 开始时间 && (结束时间为空 || 当前时间 <= 结束时间)
        else if (now.compareTo(startTime) >= 0 && (endTime == null || now.compareTo(endTime) <= 0)) {
            return IN_PROGRESS;
        } 
        // 已结束：结束时间不为空 && 当前时间 > 结束时间
        else if (endTime != null && now.after(endTime)) {
            return ENDED;
        }
        
        // 默认返回未开始
        return NOT_STARTED;
    }
    
    /**
     * 根据状态码获取枚举
     * 
     * @param code 状态码
     * @return 活动状态枚举
     */
    public static ActivityStatus getByCode(Integer code) {
        if (code == null) {
            return NOT_STARTED;
        }
        
        for (ActivityStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        
        return NOT_STARTED;
    }
}
