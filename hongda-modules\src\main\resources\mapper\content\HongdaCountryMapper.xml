<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongda.content.mapper.HongdaCountryMapper">

    <resultMap type="HongdaCountry" id="HongdaCountryResult">
        <result property="id"    column="id"    />
        <result property="nameCn"    column="name_cn"    />
        <result property="nameEn"    column="name_en"    />
        <result property="continent"    column="continent"    />
        <result property="summary"    column="summary"    />
        <result property="flagUrl"    column="flag_url"    />
        <result property="listCoverUrl"    column="list_cover_url"    />
        <result property="detailsCoverUrl"    column="details_cover_url"    />
        <result property="introduction"    column="introduction"    />
        <result property="basicInfoJson"    column="basic_info_json"    />
        <result property="investmentPolicy"    column="investment_policy"    />
        <result property="customsPolicy"    column="customs_policy"    />
        <result property="taxPolicy"    column="tax_policy"    />
        <result property="status"    column="status"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectHongdaCountryVo">
        select id, name_cn, name_en, continent, summary, flag_url, list_cover_url, details_cover_url, introduction, basic_info_json, investment_policy, customs_policy, tax_policy, status, sort_order, create_by, create_time, update_by, update_time from hongda_country
    </sql>

    <select id="selectHongdaCountryList" parameterType="HongdaCountry" resultMap="HongdaCountryResult">
        <include refid="selectHongdaCountryVo"/>
        <where>
            <if test="nameCn != null and nameCn != ''">
                and (name_cn like concat('%', #{nameCn}, '%') or name_en like concat('%', #{nameCn}, '%'))
            </if>
            <if test="continent != null and continent != ''"> and continent = #{continent}</if>
            <if test="status != null and status != ''"> and status = #{status}</if>
        </where>
    </select>

    <select id="selectHongdaCountryById" parameterType="Long" resultMap="HongdaCountryResult">
        <include refid="selectHongdaCountryVo"/>
        where id = #{id}
    </select>

    <insert id="insertHongdaCountry" parameterType="HongdaCountry" useGeneratedKeys="true" keyProperty="id">
        insert into hongda_country
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="nameCn != null and nameCn != ''">name_cn,</if>
            <if test="nameEn != null">name_en,</if>
            <if test="continent != null">continent,</if>
            <if test="summary != null">summary,</if>
            <if test="flagUrl != null">flag_url,</if>
            <if test="listCoverUrl != null">list_cover_url,</if>
            <if test="detailsCoverUrl != null">details_cover_url,</if>
            <if test="introduction != null">introduction,</if>
            <if test="basicInfoJson != null">basic_info_json,</if>
            <if test="investmentPolicy != null">investment_policy,</if>
            <if test="customsPolicy != null">customs_policy,</if>
            <if test="taxPolicy != null">tax_policy,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="nameCn != null and nameCn != ''">#{nameCn},</if>
            <if test="nameEn != null">#{nameEn},</if>
            <if test="continent != null">#{continent},</if>
            <if test="summary != null">#{summary},</if>
            <if test="flagUrl != null">#{flagUrl},</if>
            <if test="listCoverUrl != null">#{listCoverUrl},</if>
            <if test="detailsCoverUrl != null">#{detailsCoverUrl},</if>
            <if test="introduction != null">#{introduction},</if>
            <if test="basicInfoJson != null">#{basicInfoJson},</if>
            <if test="investmentPolicy != null">#{investmentPolicy},</if>
            <if test="customsPolicy != null">#{customsPolicy},</if>
            <if test="taxPolicy != null">#{taxPolicy},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateHongdaCountry" parameterType="HongdaCountry">
        update hongda_country
        <trim prefix="SET" suffixOverrides=",">
            <if test="nameCn != null and nameCn != ''">name_cn = #{nameCn},</if>
            <if test="nameEn != null">name_en = #{nameEn},</if>
            <if test="continent != null">continent = #{continent},</if>
            <if test="summary != null">summary = #{summary},</if>
            <if test="flagUrl != null">flag_url = #{flagUrl},</if>
            <if test="listCoverUrl != null">list_cover_url = #{listCoverUrl},</if>
            <if test="detailsCoverUrl != null">details_cover_url = #{detailsCoverUrl},</if>
            <if test="introduction != null">introduction = #{introduction},</if>
            <if test="basicInfoJson != null">basic_info_json = #{basicInfoJson},</if>
            <if test="investmentPolicy != null">investment_policy = #{investmentPolicy},</if>
            <if test="customsPolicy != null">customs_policy = #{customsPolicy},</if>
            <if test="taxPolicy != null">tax_policy = #{taxPolicy},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHongdaCountryById" parameterType="Long">
        delete from hongda_country where id = #{id}
    </delete>

    <delete id="deleteHongdaCountryByIds" parameterType="String">
        delete from hongda_country where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>