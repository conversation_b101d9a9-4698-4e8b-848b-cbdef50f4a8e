<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="关联国家" prop="countryId">
        <el-select
            v-model="queryParams.countryId"
            placeholder="请选择国家"
            clearable
            filterable
            style="width: 200px"
            @change="handleQuery"
        >
        <el-option
            v-for="item in countryOptions"
            :key="item.id"
            :label="item.nameCn"
            :value="item.id"
        />
        </el-select>
      </el-form-item>
      <el-form-item label="政策大类" prop="policyType">
        <el-select v-model="queryParams.policyType" placeholder="请选择政策大类" clearable style="width: 150px">
          <el-option
              v-for="dict in country_policy_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="文章标题" prop="title">
        <el-input
            v-model="queryParams.title"
            placeholder="请输入文章标题"
            clearable
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 150px">
          <el-option
              v-for="dict in hongda_article_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['content:countryPolicy:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['content:countryPolicy:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['content:countryPolicy:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="countryPolicyList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="文章标题" align="center" prop="title" min-width="250" :show-overflow-tooltip="true" />
      <el-table-column label="关联国家" align="center" prop="countryName" width="150" />
      <el-table-column label="政策大类" align="center" prop="policyType" width="120">
        <template #default="scope">
          <dict-tag :options="country_policy_type" :value="scope.row.policyType"/>
        </template>
      </el-table-column>
      <el-table-column label="文章子分类" align="center" prop="categoryName" width="150" />
      <!-- [!] 修改：内容预览列，替换为“查看全文”按钮 -->
      <el-table-column label="内容预览" align="center" width="120">
        <template #default="scope">
          <el-button link type="primary" @click="handleView(scope.row)">查看全文</el-button>
        </template>
      </el-table-column>
<!--      <el-table-column label="封面图" align="center" prop="coverImage" width="100">
        <template #default="scope">
          <image-preview :src="scope.row.coverImage" :width="50" :height="50"/>
        </template>
      </el-table-column>-->
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template #default="scope">
          <dict-tag :options="hongda_article_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="显示顺序" align="center" prop="sortOrder" width="100" sortable />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180" sortable />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120" fixed="right">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['content:countryPolicy:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['content:countryPolicy:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <el-dialog :title="title" v-model="open" width="80%" top="5vh" append-to-body :close-on-click-modal="false">
      <el-form ref="countryPolicyRef" :model="form" :rules="rules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="关联国家" prop="countryId">
              <el-select
                  v-model="form.countryId"
                  placeholder="请输入国家名称搜索"
                  filterable
                  remote
                  :remote-method="searchCountries"
                  :loading="countrySearchLoading"
                  style="width: 100%;"
              >
                <el-option
                    v-for="item in countryOptions"
                    :key="item.id"
                    :label="item.nameCn"
                    :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="政策大类" prop="policyType">
              <el-select v-model="form.policyType" placeholder="请选择政策大类" style="width: 100%;">
                <el-option
                    v-for="dict in country_policy_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="文章子分类" prop="categoryName">
              <el-input v-model="form.categoryName" placeholder="请输入文章子分类" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="文章标题" prop="title">
              <el-input v-model="form.title" placeholder="请输入文章标题" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="文章摘要" prop="summary">
              <el-input v-model="form.summary" type="textarea" :rows="3" placeholder="请输入摘要内容" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="封面图" prop="coverImage">
              <image-upload v-model="form.coverImage"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="文章正文" prop="content">
              <editor v-model="form.content" :min-height="192"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio
                    v-for="dict in hongda_article_status"
                    :key="dict.value"
                    :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="显示顺序" prop="sortOrder">
              <el-input-number v-model="form.sortOrder" controls-position="right" :min="0" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- [!] 新增：设备预览弹窗 -->
    <el-dialog
        v-model="contentDialogVisible"
        width="auto"
        :show-close="true"
        align-center
        class="device-preview-dialog"
    >
      <template #header>
        <el-dropdown trigger="click" @command="handleDeviceChange">
          <span class="el-dropdown-link">
            {{ `设备预览 - ${selectedDevice.name}` }}
            <el-icon class="el-icon--right"><arrow-down /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                  v-for="device in devicePresets"
                  :key="device.name"
                  :command="device"
              >
                {{ device.name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>

      <div class="device-frame" :style="{ width: selectedDevice.width + 'px', height: selectedDevice.height + 'px' }">
        <MiniProgramPreview
            v-if="contentDialogVisible"
            :article="currentPolicyForPreview"
        />
      </div>

      <template #footer>
        <el-button @click="contentDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="CountryPolicy">
import { listCountryPolicy, getCountryPolicy, delCountryPolicy, addCountryPolicy, updateCountryPolicy } from "@/api/content/countryPolicy";
import { getCountry, listAllCountry } from "@/api/content/country";
// [!] 新增：引入图标和预览组件
import { ArrowDown } from '@element-plus/icons-vue';
import MiniProgramPreview from '@/components/MiniProgramPreview/index.vue';

const { proxy } = getCurrentInstance();
const { hongda_article_status, country_policy_type } = proxy.useDict('hongda_article_status', 'country_policy_type');

const countryPolicyList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const countryOptions = ref([]);
const countrySearchLoading = ref(false);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    countryId: null,
    policyType: null,
    categoryName: null,
    title: null,
    status: null,
  },
  rules: {
    countryId: [
      { required: true, message: "关联国家不能为空", trigger: "change" }
    ],
    policyType: [
      { required: true, message: "政策大类不能为空", trigger: "change" }
    ],
    categoryName: [
      { required: true, message: "文章子分类不能为空", trigger: "blur" }
    ],
    title: [
      { required: true, message: "文章标题不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

// [!] 新增：设备预览功能所需的状态和方法
const contentDialogVisible = ref(false);
const currentPolicyForPreview = ref(null);
const devicePresets = ref([
  { name: 'iPhone 13 Pro (390x844)', width: 390, height: 844 },
  { name: 'iPhone 8 (414x736)', width: 414, height: 736 },
  { name: '主流安卓 (412x915)', width: 412, height: 915 }
]);
const selectedDevice = ref(devicePresets.value[0]);

function handleView(row) {
  if (!row || !row.content) {
    proxy.$modal.msgWarning('该文章暂无内容');
    return;
  }
  // 适配预览组件，它可能需要一个名为 article 的 prop
  // 同时确保传递的数据是干净的，避免循环引用等问题
  currentPolicyForPreview.value = { ...row };
  contentDialogVisible.value = true;
}

function handleDeviceChange(device) {
  selectedDevice.value = device;
}
// ===============================================

/** 查询国别政策管理列表 */
function getList() {
  loading.value = true;
  listCountryPolicy(queryParams.value).then(response => {
    // [!] 恢复：前端ID到名称的转换逻辑
    countryPolicyList.value = response.rows.map(row => {
      const country = countryOptions.value.find(c => c.id === row.countryId);
      return {
        ...row,
        countryName: country ? country.nameCn : '未知国家'
      };
    });
    total.value = response.total;
    loading.value = false;
  });
}

async function getCountryOptions() {
  const response = await listAllCountry();
  countryOptions.value = response.data;
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    articleId: null,
    countryId: null,
    policyType: null,
    categoryName: null,
    title: null,
    summary: null,
    content: null,
    coverImage: null,
    status: "1",
    sortOrder: 0,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    remark: null
  };
  proxy.resetForm("countryPolicyRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.articleId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  countryOptions.value = [];
  open.value = true;
  title.value = "添加国别政策";
}

/** 修改按钮操作 */
async function handleUpdate(row) {
  reset();
  const _articleId = row.articleId || ids.value[0];
  const response = await getCountryPolicy(_articleId);
  form.value = response.data;
  // [!] 简化：不再需要手动处理 countryOptions，因为它已经包含了所有国家
  open.value = true;
  title.value = "修改国别政策";
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["countryPolicyRef"].validate(valid => {
    if (valid) {
      if (form.value.articleId != null) {
        updateCountryPolicy(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addCountryPolicy(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _articleIds = row.articleId || ids.value;
  proxy.$modal.confirm('是否确认删除国别政策管理编号为"' + _articleIds + '"的数据项？').then(function() {
    return delCountryPolicy(_articleIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

onMounted(async () => {
  await getCountryOptions();
  getList();
});
</script>

<style scoped>
/* [!] 新增：设备预览功能所需的样式 */
:deep(.device-preview-dialog .el-dialog) {
  --el-dialog-width: auto !important;
  background-color: transparent !important;
  box-shadow: none !important;
  border: none !important;
  padding: 20px;
}
:deep(.device-preview-dialog .el-dialog__header) {
  text-align: center;
  margin-right: 0;
  padding-bottom: 10px;
}
:deep(.device-preview-dialog .el-dialog__headerbtn) {
  color: #fff;
}
:deep(.device-preview-dialog .el-dialog__body) {
  padding: 0 !important;
  display: flex;
  justify-content: center;
}
:deep(.device-preview-dialog .el-dialog__footer) {
  text-align: center;
  padding-top: 10px;
}

.el-dropdown-link {
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  outline: none;
  cursor: pointer;
}

.device-frame {
  background: #1a1a1a;
  border-radius: 40px;
  padding: 8px;
  box-shadow:
      0 0 0 2px #333,
      0 20px 60px rgba(0, 0, 0, 0.4),
      inset 0 0 0 2px #444;
  transition: all 0.3s ease-in-out;
}
</style>
