package com.hongda.content.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hongda.common.annotation.Log;
import com.hongda.common.core.controller.BaseController;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.common.enums.BusinessType;
import com.hongda.content.domain.HongdaTag;
import com.hongda.content.service.IHongdaTagService;
import com.hongda.common.utils.poi.ExcelUtil;
import com.hongda.common.core.page.TableDataInfo;

/**
 * 资讯分类标签Controller
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
@RestController
@RequestMapping("/content/tag")
public class HongdaTagController extends BaseController
{
    @Autowired
    private IHongdaTagService hongdaTagService;

    /**
     * 查询资讯分类标签列表
     */
    @PreAuthorize("@ss.hasPermi('content:tag:list')")
    @GetMapping("/list")
    public TableDataInfo list(HongdaTag hongdaTag)
    {
        startPage();
        List<HongdaTag> list = hongdaTagService.selectHongdaTagList(hongdaTag);
        return getDataTable(list);
    }

    /**
     * 【新增】获取所有资讯分类标签列表
     */
    @GetMapping("/listAll")
    public AjaxResult listAll()
    {
        List<HongdaTag> list = hongdaTagService.selectHongdaTagList(new HongdaTag());
        return success(list);
    }

    /**
     * 导出资讯分类标签列表
     */
    @PreAuthorize("@ss.hasPermi('content:tag:export')")
    @Log(title = "资讯分类标签", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HongdaTag hongdaTag)
    {
        List<HongdaTag> list = hongdaTagService.selectHongdaTagList(hongdaTag);
        ExcelUtil<HongdaTag> util = new ExcelUtil<HongdaTag>(HongdaTag.class);
        util.exportExcel(response, list, "资讯分类标签数据");
    }

    /**
     * 获取资讯分类标签详细信息
     */
    @PreAuthorize("@ss.hasPermi('content:tag:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(hongdaTagService.selectHongdaTagById(id));
    }

    /**
     * 新增资讯分类标签
     */
    @PreAuthorize("@ss.hasPermi('content:tag:add')")
    @Log(title = "资讯分类标签", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HongdaTag hongdaTag)
    {
        return toAjax(hongdaTagService.insertHongdaTag(hongdaTag));
    }

    /**
     * 修改资讯分类标签
     */
    @PreAuthorize("@ss.hasPermi('content:tag:edit')")
    @Log(title = "资讯分类标签", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HongdaTag hongdaTag)
    {
        return toAjax(hongdaTagService.updateHongdaTag(hongdaTag));
    }

    /**
     * 删除资讯分类标签
     */
    @PreAuthorize("@ss.hasPermi('content:tag:remove')")
    @Log(title = "资讯分类标签", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(hongdaTagService.deleteHongdaTagByIds(ids));
    }
}
