package com.hongda.common.core.domain.dto;

import com.hongda.common.annotation.OssUrl;

/**
 * 用户资料更新DTO
 *
 * <AUTHOR>
 */
public class UserProfileDTO
{
    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 用户头像URL
     */
    @OssUrl
    private String avatarUrl;

    public UserProfileDTO()
    {
    }

    public UserProfileDTO(String nickname, String avatarUrl)
    {
        this.nickname = nickname;
        this.avatarUrl = avatarUrl;
    }

    public String getNickname()
    {
        return nickname;
    }

    public void setNickname(String nickname)
    {
        this.nickname = nickname;
    }

    public String getAvatarUrl()
    {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl)
    {
        this.avatarUrl = avatarUrl;
    }

    @Override
    public String toString()
    {
        return "UserProfileDTO{" +
                "nickname='" + nickname + '\'' +
                ", avatarUrl='" + avatarUrl + '\'' +
                '}';
    }
} 