package com.hongda.wxapp.service;

import com.hongda.common.core.domain.AjaxResult;
import com.hongda.common.core.domain.dto.UserProfileDTO;

/**
 * 微信小程序用户服务接口
 *
 * <AUTHOR>
 */
public interface IWxUserService
{
    /**
     * 小程序登录
     * 
     * @param code 微信登录凭证
     * @return 登录结果，包含token和用户信息
     */
    AjaxResult wxLogin(String code);

    /**
     * 获取用户手机号
     * 
     * @param code 微信手机号授权码
     * @return 手机号信息
     */
    AjaxResult getPhoneNumber(String code);

    /**
     * 获取微信access_token
     * 
     * @return access_token
     */
    AjaxResult getAccessToken();

    /**
     * 更新用户资料
     * 
     * @param userId 用户ID
     * @param profileDTO 用户资料信息
     * @return 更新结果
     */
    AjaxResult updateProfile(Long userId, UserProfileDTO profileDTO);

    /**
     * 获取用户信息
     * 
     * @param userId 用户ID
     * @return 用户信息
     */
    AjaxResult getUserInfo(Long userId);

    /**
     * 删除用户账号
     * 
     * @param userId 用户ID
     * @return 删除结果
     */
    AjaxResult deleteAccount(Long userId);

    /**
     * 生成手机号哈希值
     * 
     * @param phone 手机号
     * @return 哈希值
     */
    String generatePhoneHash(String phone);

}