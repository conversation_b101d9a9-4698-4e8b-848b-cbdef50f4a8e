<template>
  <el-table 
    v-loading="loading" 
    :data="eventList" 
    row-key="id" 
    @selection-change="handleSelectionChange"
  >
    <el-table-column type="selection" width="55" align="center" />
    <el-table-column label="活动ID" align="center" prop="id" width="80" />
    <el-table-column label="活动名称" align="center" prop="title" show-overflow-tooltip />
    
    <!-- 报名情况列 -->
    <el-table-column label="报名情况" align="center" width="120">
      <template #default="scope">
        <span v-if="scope.row.maxParticipants > 0">
          {{ scope.row.registeredCount || 0 }} / {{ scope.row.maxParticipants }}
        </span>
        <span v-else>
          {{ scope.row.registeredCount || 0 }} / 不限制
        </span>
      </template>
    </el-table-column>
    
    <el-table-column label="活动地点" align="center" show-overflow-tooltip>
      <template #default="scope">
        <span>{{ formatEventLocation(scope.row) }}</span>
      </template>
    </el-table-column>
    
    <el-table-column label="开始时间" align="center" prop="startTime" width="160">
      <template #default="scope">
        <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d}') }}</span>
      </template>
    </el-table-column>
    
    <el-table-column label="报名状态" align="center" width="100">
       <template #default="scope">
          <el-tag :type="getRegistrationStatusType(scope.row.registrationStatus)">
            {{ getRegistrationStatusText(scope.row.registrationStatus) }}
          </el-tag>
       </template>
    </el-table-column>
    
    <el-table-column label="是否热门" align="center" prop="isHot" width="100">
      <template #default="scope">
        <el-switch
          v-model="scope.row.isHot"
          :active-value="1"
          :inactive-value="0"
          @change="(value) => handleHotStatusChange(scope.row, value)"
          :loading="scope.row.hotStatusLoading"
        />
      </template>
    </el-table-column>
    
    <el-table-column label="是否推广" align="center" prop="isPromoted" width="100">
      <template #default="scope">
        <el-switch
          v-model="scope.row.isPromoted"
          :active-value="1"
          :inactive-value="0"
          @change="(value) => handlePromotionStatusChange(scope.row, value)"
          :loading="scope.row.promotionStatusLoading"
        />
      </template>
    </el-table-column>
    
    <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width">
      <template #default="scope">
        <el-dropdown trigger="click">
          <el-button link type="primary">
            更多操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item 
                icon="View" 
                @click="handleViewRegistrations(scope.row)" 
                v-hasPermi="['content:event:query']">
                查看报名
              </el-dropdown-item>
              <el-dropdown-item 
                icon="Edit" 
                @click="handleUpdate(scope.row)" 
                v-hasPermi="['content:event:edit']">
                修改
              </el-dropdown-item>
              <el-dropdown-item 
                icon="Delete" 
                @click="handleDelete(scope.row)" 
                v-hasPermi="['content:event:remove']"
                :style="{ color: 'var(--el-color-danger)' }">
                删除
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup>
import { ArrowDown } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  eventList: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits([
  'selection-change',
  'view-registrations',
  'update',
  'delete',
  'hot-status-change',
  'promotion-status-change'
])

// Methods
const handleSelectionChange = (selection) => {
  emit('selection-change', selection)
}

const handleViewRegistrations = (row) => {
  emit('view-registrations', row)
}

const handleUpdate = (row) => {
  emit('update', row)
}

const handleDelete = (row) => {
  emit('delete', row)
}

const handleHotStatusChange = (row, value) => {
  emit('hot-status-change', row, value)
}

const handlePromotionStatusChange = (row, value) => {
  emit('promotion-status-change', row, value)
}

// Utility functions (these should be imported from utils or passed as props)
const formatEventLocation = (event) => {
  // 优先使用省市字段
  if (event.province && event.city) {
    return event.province === event.city ? event.city : `${event.province}${event.city}`;
  }
  
  // 如果有city字段，直接使用
  if (event.city && event.city.trim()) {
    return event.city.trim();
  }
  
  // 如果只有完整地址，尝试提取省市信息
  if (event.location && event.location.trim()) {
    const location = event.location.trim();
    // 简单的省市提取逻辑，匹配常见的省市格式
    const match = location.match(/^(.+?省|.+?市|.+?自治区|.+?特别行政区)(.+?市|.+?区|.+?县)?/);
    if (match) {
      const province = match[1];
      const city = match[2];
      if (city) {
        return province === city ? city : `${province}${city}`;
      }
      return province;
    }
  }
  
  return '待定';
}

const parseTime = (time, pattern) => {
  if (!time) return ''
  const date = new Date(time)
  const format = {
    y: date.getFullYear(),
    m: String(date.getMonth() + 1).padStart(2, '0'),
    d: String(date.getDate()).padStart(2, '0')
  }
  return pattern.replace(/{(y|m|d)}/g, (match, key) => format[key])
}

const getRegistrationStatusType = (status) => {
  switch (status) {
    case 0: return 'warning';  // 未开始 - 橙色
    case 1: return 'success';  // 报名中 - 绿色
    case 2: return 'info';     // 已结束 - 灰色
    default: return 'info';
  }
}

const getRegistrationStatusText = (status) => {
  switch (status) {
    case 0: return '未开始';
    case 1: return '报名中';
    case 2: return '已结束';
    default: return '未知';
  }
}
</script>
