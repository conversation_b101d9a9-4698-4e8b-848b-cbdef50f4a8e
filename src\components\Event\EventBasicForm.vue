<template>
  <el-form ref="eventRef" :model="form" :rules="rules" label-width="120px">
    <!-- 基础信息卡片 -->
    <el-card shadow="never" class="form-card">
      <template #header>
        <div class="card-header">
          <span class="card-title">基础信息</span>
        </div>
      </template>
      
      <!-- 核心信息区 -->
      <el-form-item label="活动名称" prop="title">
        <el-input
          :model-value="form.title"
          @update:model-value="updateField('title', $event)"
          placeholder="请输入活动名称"
          maxlength="100"
          show-word-limit
          size="large"
        />
      </el-form-item>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="活动简介" prop="summary">
            <el-input
              :model-value="form.summary"
              @update:model-value="updateField('summary', $event)"
              placeholder="请输入活动简介，20字以内"
              maxlength="20"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="卖点" prop="sellPoint">
            <el-input
              :model-value="form.sellPoint"
              @update:model-value="updateField('sellPoint', $event)"
              placeholder="请输入活动卖点，15字以内"
              maxlength="15"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 媒体资源区 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="活动封面" prop="coverImageUrl">
            <div class="upload-tip">封面将用于活动详情页顶部展示</div>
            <image-upload
              :model-value="form.coverImageUrl"
              @update:model-value="updateField('coverImageUrl', $event)"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="卡片图标" prop="iconUrl">
            <div class="upload-tip">图标用于活动列表页卡片展示</div>
            <image-upload
              :model-value="form.iconUrl"
              @update:model-value="updateField('iconUrl', $event)"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-card>
    
    <!-- 时间设置卡片 -->
    <EventTimeForm :form="form" @update:form="$emit('update:form', $event)" />

    <!-- 地点设置卡片 -->
    <EventLocationForm
      :form="form"
      :selectedLocation="selectedLocation"
      :locationOptions="locationOptions"
      @location-change="handleLocationChange"
      @update:form="$emit('update:form', $event)"
    />

    <!-- 报名设置卡片 -->
    <EventRegistrationForm :form="form" @update:form="$emit('update:form', $event)" />

    <!-- 详细内容卡片 -->
    <EventContentForm :form="form" @update:form="$emit('update:form', $event)" />
  </el-form>
</template>

<script setup>
import { ref } from 'vue'
import EventTimeForm from './EventTimeForm.vue'
import EventLocationForm from './EventLocationForm.vue'
import EventRegistrationForm from './EventRegistrationForm.vue'
import EventContentForm from './EventContentForm.vue'

// Props
const props = defineProps({
  form: {
    type: Object,
    required: true
  },
  rules: {
    type: Object,
    required: true
  },
  selectedLocation: {
    type: Array,
    default: () => []
  },
  locationOptions: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:form', 'location-change'])

// Refs
const eventRef = ref()

// Methods
const handleLocationChange = (value) => {
  emit('location-change', value)
}

const updateField = (field, value) => {
  const updatedForm = { ...props.form, [field]: value }
  emit('update:form', updatedForm)
}

// Expose ref for parent component
defineExpose({
  eventRef
})
</script>

<style scoped>
/* 表单卡片样式 */
.form-card {
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.form-card :deep(.el-card__header) {
  padding: 16px 20px;
  background-color: #fafafa;
  border-bottom: 1px solid #e4e7ed;
}

.form-card :deep(.el-card__body) {
  padding: 24px 20px;
}

/* 卡片标题样式 */
.card-header {
  display: flex;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

/* 媒体上传区域样式 */
.upload-tip {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
  line-height: 1.4;
}

/* 表单项间距优化 */
.el-form .el-form-item {
  margin-bottom: 20px;
}
</style>
