# 项目相关配置
ruoyi:
  # 名称
  name: RuoYi
  # 版本
  version: 3.9.0
  # 版权年份
  copyrightYear: 2025
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: D:/ruoyi/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 80
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.hongda: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: druid
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 50MB
      # 设置总上传的文件大小
      max-request-size: 100MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  data:
    # redis 配置
    redis:
      # 地址
      host: localhost
      # 端口，默认为6379
      port: 6379
      # 数据库索引
      database: 0
      # 密码
      password:
      # 连接超时时间
      timeout: 10s
      lettuce:
        pool:
          # 连接池中的最小空闲连接
          min-idle: 0
          # 连接池中的最大空闲连接
          max-idle: 8
          # 连接池的最大数据库连接数
          max-active: 8
          # #连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: -1ms

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 480

# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.hongda.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Springdoc配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    tags-sorter: alpha
  group-configs:
    - group: 'content'
      display-name: '内容管理模块'
      paths-to-match: '/content/**'
      packages-to-scan: com.hongda.content.controller
    - group: 'data'
      display-name: '数据管理模块'
      paths-to-match: '/data/**'
      packages-to-scan: com.hongda.data.controller
    - group: 'platform'
      display-name: '平台管理模块'
      paths-to-match: '/platform/**'
      packages-to-scan: com.hongda.platform.controller
    - group: 'system'
      display-name: '系统管理模块'
      paths-to-match: '/system/**'
      packages-to-scan: com.ruoyi.web.controller.system
    - group: 'all'
      display-name: '所有接口'
      paths-to-match: '/**'
      packages-to-scan: com.hongda

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# --- 阿里云 OSS 配置 ---
aliyun:
  oss:
    # 访问域名/地域节点 (假设您的Java程序部署在北京地域外的服务器)
    endpoint: oss-cn-beijing.aliyuncs.com
    # 您的 AccessKey ID
    accessKeyId: LTAI4FkjUCzvZTUL9Ecg9sNt
    # 您的 AccessKey Secret (请务必保密)
    accessKeySecret: ******************************
    # 您创建的 Bucket 名称
    bucketName: hd-zcq

# --- 微信小程序配置 ---
wx:
  app-id: wxfcd1103eda5730d2
  app-secret: bb2a7423e25cc4f66e112806ff55c7a0

policy:
  user-agreement:
    version: "1.0.0"
    title: "用户协议"
    effective-time: "2025-06-01T00:00:00"
    file: "policy/user_agreement_v1.0.0.html"
  privacy-policy:
    version: "1.0.0"
    title: "隐私政策"
    effective-time: "2025-06-01T00:00:00"
    file: "policy/privacy_policy_v1.0.0.html"