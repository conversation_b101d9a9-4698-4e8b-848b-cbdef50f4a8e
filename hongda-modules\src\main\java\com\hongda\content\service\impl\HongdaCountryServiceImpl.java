package com.hongda.content.service.impl;

import java.util.List;
import com.hongda.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.hongda.content.mapper.HongdaCountryMapper;
import com.hongda.content.domain.HongdaCountry;
import com.hongda.content.service.IHongdaCountryService;

/**
 * 国别信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
@Service
public class HongdaCountryServiceImpl implements IHongdaCountryService 
{
    @Autowired
    private HongdaCountryMapper hongdaCountryMapper;

    /**
     * 查询国别信息
     * 
     * @param id 国别信息主键
     * @return 国别信息
     */
    @Override
    public HongdaCountry selectHongdaCountryById(Long id)
    {
        return hongdaCountryMapper.selectHongdaCountryById(id);
    }

    /**
     * 查询国别信息列表
     * 
     * @param hongdaCountry 国别信息
     * @return 国别信息
     */
    @Override
    public List<HongdaCountry> selectHongdaCountryList(HongdaCountry hongdaCountry)
    {
        return hongdaCountryMapper.selectHongdaCountryList(hongdaCountry);
    }

    /**
     * 新增国别信息
     * 
     * @param hongdaCountry 国别信息
     * @return 结果
     */
    @Override
    public int insertHongdaCountry(HongdaCountry hongdaCountry)
    {
        hongdaCountry.setCreateTime(DateUtils.getNowDate());
        return hongdaCountryMapper.insertHongdaCountry(hongdaCountry);
    }

    /**
     * 修改国别信息
     * 
     * @param hongdaCountry 国别信息
     * @return 结果
     */
    @Override
    public int updateHongdaCountry(HongdaCountry hongdaCountry)
    {
        hongdaCountry.setUpdateTime(DateUtils.getNowDate());
        return hongdaCountryMapper.updateHongdaCountry(hongdaCountry);
    }

    /**
     * 批量删除国别信息
     * 
     * @param ids 需要删除的国别信息主键
     * @return 结果
     */
    @Override
    public int deleteHongdaCountryByIds(Long[] ids)
    {
        return hongdaCountryMapper.deleteHongdaCountryByIds(ids);
    }

    /**
     * 删除国别信息信息
     * 
     * @param id 国别信息主键
     * @return 结果
     */
    @Override
    public int deleteHongdaCountryById(Long id)
    {
        return hongdaCountryMapper.deleteHongdaCountryById(id);
    }
}
