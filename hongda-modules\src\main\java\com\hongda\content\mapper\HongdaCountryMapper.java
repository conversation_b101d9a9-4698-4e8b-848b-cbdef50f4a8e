    package com.hongda.content.mapper;

    import java.util.List;
    import com.hongda.content.domain.HongdaCountry;

    /**
     * 国别信息Mapper接口
     *
     * <AUTHOR>
     * @date 2025-07-18
     */
    public interface HongdaCountryMapper
    {
        /**
         * 查询国别信息
         *
         * @param id 国别信息主键
         * @return 国别信息
         */
        public HongdaCountry selectHongdaCountryById(Long id);

        /**
         * 查询国别信息列表
         *
         * @param hongdaCountry 国别信息
         * @return 国别信息集合
         */
        public List<HongdaCountry> selectHongdaCountryList(HongdaCountry hongdaCountry);

        /**
         * 新增国别信息
         *
         * @param hongdaCountry 国别信息
         * @return 结果
         */
        public int insertHongdaCountry(HongdaCountry hongdaCountry);

        /**
         * 修改国别信息
         *
         * @param hongdaCountry 国别信息
         * @return 结果
         */
        public int updateHongdaCountry(HongdaCountry hongdaCountry);

        /**
         * 删除国别信息
         *
         * @param id 国别信息主键
         * @return 结果
         */
        public int deleteHongdaCountryById(Long id);

        /**
         * 批量删除国别信息
         *
         * @param ids 需要删除的数据主键集合
         * @return 结果
         */
        public int deleteHongdaCountryByIds(Long[] ids);
    }
